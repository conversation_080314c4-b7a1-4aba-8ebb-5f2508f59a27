import { useEffect, useRef } from 'react';
import { useSessionTimeoutStore } from './sessionStore';
import { useAuthStore } from './authStore';

/**
 * Hook to trigger an action (e.g., popup) after a timeout when betshopSettings changes.
 * @param onTimeout Callback to execute when timeout completes
 */
export function useBetshopSettingsTimeout(
    onTimeout: () => void,
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    useEffect(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // Only run session timeout logic if user is authenticated
        // Check for betshopSettings !== undefined instead of truthy check to handle 0 values
        if (isAuthenticated && betshopSettings !== undefined) {
            // getTimeoutMs should return minutes, convert to ms
            const minutes = Number(betshopSettings);

            // Validate that minutes is a positive number
            if (!isNaN(minutes) && minutes > 0) {
                const ms = minutes * 60 * 1000;

                timeoutRef.current = setTimeout(() => {
                    onTimeout();
                }, ms);
            }
        }

        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [isAuthenticated, betshopSettings, onTimeout]);
}

