'use client';
import React, { useState } from 'react';
import { useBetshopSettingsTimeout } from './useBetshopSettingsTimeout';
import { useAuthStore } from './authStore';
import { useRouter } from 'next/navigation';
import { AuthErrorModal } from '@/shared/components/ui-elements/alerts';

// Example usage in a component
export function SessionTimeoutHandler() {
    const [modalOpen, setModalOpen] = useState(false);

    const handleTimeout = () => {
        setModalOpen(true);
    };

    const router = useRouter();
    const handleClose = () => {
        setModalOpen(false);
        useAuthStore.getState().clearAuth();
        router.replace('/authentication/sign-in');
    };

    useBetshopSettingsTimeout(handleTimeout);

    return (
        <AuthErrorModal
            isOpen={modalOpen}
            onClose={handleClose}
        />
    );
}
