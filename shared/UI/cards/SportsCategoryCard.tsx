"use client";

import React from "react";
import { SportsCategoryCardProps } from "@/shared/types/sportsbook-types";

// Re-export the type for convenience
export type { SportsCategoryCardProps };

/**
 * Sports category card component with specified styling
 * Displays sport icon and name with gradient background and hover effects
 */
export const SportsCategoryCard: React.FC<SportsCategoryCardProps> = ({
  discipline,
  onClick,
  className = "",
}) => {
  const handleClick = () => {
    onClick(discipline);
  };

  // Generate a placeholder icon based on sport name
  const getPlaceholderIcon = (sportName: string) => {
    // Simple icon mapping - in production, you'd want proper sport icons
    const iconMap: Record<string, string> = {
      'soccer': '⚽',
      'football': '🏈',
      'basketball': '🏀',
      'tennis': '🎾',
      'baseball': '⚾',
      'hockey': '🏒',
      'golf': '⛳',
      'volleyball': '🏐',
      'cricket': '🏏',
      'boxing': '🥊',
      'swimming': '🏊',
      'cycling': '🚴',
      'running': '🏃',
      'wrestling': '🤼',
      'badminton': '🏸',
      'table tennis': '🏓',
      'skiing': '⛷️',
      'snowboarding': '🏂',
      'surfing': '🏄',
      'climbing': '🧗',
    };

    const lowerName = sportName.toLowerCase();
    for (const [key, icon] of Object.entries(iconMap)) {
      if (lowerName.includes(key)) {
        return icon;
      }
    }
    return '🏆'; // Default sports icon
  };

  return (
    <div
      className={`
        flex flex-col items-center justify-center
        w-[195.71px] h-[182.56px]
        rounded-[20.51px]
        px-[16.41px]
        border-2 border-[#FFFFFF1A]
        cursor-pointer
        transition-all duration-200
        hover:scale-105 hover:border-[var(--golden)]/50
        ${className}
      `}
      style={{
        background: 'linear-gradient(193.45deg, #1D1C16 9.62%, #2D2100 90.24%)',
        boxShadow: '0px 4px 12px 0px #0000008C, 4px 4px 8px 0px #FFD37426 inset',
        gap: '16.41px',
      }}
      onClick={handleClick}
    >
      {/* Sports Icon */}
      <div
        className="flex items-center justify-center text-4xl"
        style={{
          width: '57.44px',
          height: '57.44px',
        }}
      >
        {getPlaceholderIcon(discipline.name)}
      </div>

      {/* Sports Title */}
      <div
        className="text-center text-white font-rubik font-bold leading-none"
        style={{
          fontSize: '20px',
          lineHeight: '100%',
          letterSpacing: '0%',
        }}
      >
        {discipline.name}
      </div>
    </div>
  );
};

export default SportsCategoryCard;
