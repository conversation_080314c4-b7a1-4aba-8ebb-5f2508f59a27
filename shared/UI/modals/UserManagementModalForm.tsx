// shared/UI/components/modal/UserManagementModalForm.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { UserDetailsData } from '@/shared/types/user-management-types';
import { CreateUserData, EditUserData } from '@/shared/query';
import { UserManagementModalMode } from './UserManagementModal';
import { PrimaryButton, AccessToggleSwitch } from '@/shared/UI/components';
import { encodePassword } from '@/shared/utils/passwordEncryption';

interface UserManagementModalFormProps {
  mode: UserManagementModalMode;
  userData?: UserDetailsData;
  onSubmit: (formData: CreateUserData | EditUserData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * UserManagementModalForm Component
 * 
 * A simplified form component specifically designed for the UserManagementModal.
 * Contains essential fields for user creation and editing with proper validation
 * and dark theme styling.
 * 
 * Features:
 * - Essential fields: username, email, firstName, lastName, phone, password
 * - Role and status management
 * - Form validation
 * - Dark theme styling matching modal design
 * - Integration with existing user management API
 */
const UserManagementModalForm: React.FC<UserManagementModalFormProps> = ({
  mode,
  userData,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  // Form state
  const [formData, setFormData] = useState<CreateUserData | EditUserData>(() => {
    if (mode === 'edit' && userData) {
      return {
        id: Number(userData.id),
        userName: userData.userName || '',
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        phoneCode: userData.phoneCode || '+94',
        zipCode: '', // Not available in UserDetailsData
        dateOfBirth: '', // Not available in UserDetailsData
        countryCode: 'LK', // Default value
        currencyId: Number(userData.currencyId) || 1,
        vipLevel: Number(userData.playerCategoryLevel) || 1,
        city: '', // Not available in UserDetailsData
        emailVerified: userData.emailVerified || false,
        phoneVerified: userData.phoneVerified || false,
        forceResetPassword: userData.forceResetPassword || false,
        markAsBot: false, // Not available in UserDetailsData
        active: userData.active !== false,
        demo: userData.demo || false,
        userType: userData.userType || 1,
        encryptedPassword: '', // Don't pre-fill password for edit
      } as EditUserData;
    }

    // Default create form data
    return {
      userName: '',
      nickName: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      phoneCode: '+94',
      zipCode: '',
      dateOfBirth: '',
      countryCode: 'LK',
      currencyId: 1,
      activeBonusId: null,
      vipLevel: 1,
      city: '',
      emailVerified: false,
      phoneVerified: false,
      forceResetPassword: false,
      markAsBot: false,
      active: true,
      demo: false,
      affiliatedData: '',
      nationalId: null,
      clickId: null,
      wyntaClickId: null,
      categoryType: null,
      userType: 1,
      encryptedPassword: '',
    } as CreateUserData;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [kioskAccess, setKioskAccess] = useState(false);
  const [websiteAccess, setWebsiteAccess] = useState(true); // Default to website access
  const [showChangePassword, setShowChangePassword] = useState(false);

  // Update form data when userData changes (for edit mode)
  useEffect(() => {
    if (mode === 'edit' && userData) {
      // Combine firstName and lastName for the name field
      const fullName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();
      setName(fullName);
      setMobileNumber(userData.phone || '');

      // Set access toggles based on userType
      const userType = userData.userType || 1;
      setKioskAccess(userType === 2 || userType === 3);
      setWebsiteAccess(userType === 1 || userType === 3);

      setFormData(prev => ({
        ...prev,
        id: Number(userData.id),
        userName: userData.userName || '',
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        active: userData.active !== false,
      }));
    }
  }, [mode, userData]);

  // Handle input changes
  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle password change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (errors.password) {
      setErrors(prev => ({
        ...prev,
        password: ''
      }));
    }
  };

  // Handle name change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (errors.name) {
      setErrors(prev => ({
        ...prev,
        name: ''
      }));
    }
  };

  // Handle mobile number change
  const handleMobileNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMobileNumber(e.target.value);
    if (errors.mobileNumber) {
      setErrors(prev => ({
        ...prev,
        mobileNumber: ''
      }));
    }
  };

  // Calculate userType based on access toggles
  const calculateUserType = (): number => {
    if (kioskAccess && websiteAccess) return 3; // Kiosk & Online
    if (kioskAccess) return 2; // Kiosk only
    return 1; // Online only (default)
  };

  // Split name into firstName and lastName
  const splitName = (fullName: string): { firstName: string; lastName: string } => {
    const nameParts = fullName.trim().split(' ');
    if (nameParts.length === 1) {
      return { firstName: nameParts[0], lastName: '' };
    }
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ');
    return { firstName, lastName };
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Username validation
    if (!formData.userName.trim()) {
      newErrors.userName = 'Username is required';
    }

    // Name validation (optional field, no validation needed)

    // Mobile number validation (optional field, no validation needed)

    // Password validation (only for create mode or if changing password in edit mode)
    if (mode === 'create' && !password.trim()) {
      newErrors.password = 'Password is required';
    } else if ((mode === 'edit' && showChangePassword && !password.trim())) {
      newErrors.password = 'Password is required when changing password';
    } else if (password.trim() && password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Split name into firstName and lastName
      const { firstName, lastName } = splitName(name);

      if (mode === 'create') {
        // For create mode, send all required fields
        const dataToSubmit: CreateUserData = {
          ...formData,
          firstName,
          lastName,
          phone: mobileNumber,
          userType: calculateUserType(),
          encryptedPassword: encodePassword(password)
        } as CreateUserData;

        await onSubmit(dataToSubmit);
      } else if (mode === 'edit') {
        // For edit mode, only send the fields that are being updated
        // Type guard to ensure formData has id property
        if (!('id' in formData) || !formData.id) {
          throw new Error('User ID is required for edit mode');
        }

        const dataToSubmit: Partial<EditUserData> & { id: number } = {
          id: Number(formData.id),
          firstName,
          lastName,
          phone: mobileNumber,
          userType: calculateUserType()
        };

        // Only include password if it's being changed
        if (password.trim() && showChangePassword) {
          dataToSubmit.encryptedPassword = encodePassword(password);
        }

        await onSubmit(dataToSubmit as EditUserData);
      }
    } catch (error) {
      onCancel?.();
      // eslint-disable-next-line no-console
      console.error('Failed to submit user form', error);
    }
  };

  // Get action button text
  const getActionButtonText = () => {
    if (isLoading) {
      return mode === 'create' ? 'Creating...' : 'Updating...';
    }
    return mode === 'create' ? 'Create User' : 'Update User';
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full" style={{ gap: '16px' }}>
      {/* Form Fields */}
      <div className="flex-1 overflow-y-auto">
        <div style={{ gap: '16px' }} className="flex flex-col">
          {/* Username Field */}
          <div className="flex flex-col gap-[8px]">
            <label
              className="font-rubik font-semibold text-white capitalize"
              style={{ fontSize: '14px', fontWeight: 600 }}
            >
              Username <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className={`w-full h-[43px] bg-elevated  rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.userName ? 'border-red-500' : 'border-border-secondary'
                }`}
              placeholder="Enter username"
              disabled={mode === 'edit'}
              value={formData.userName}
              onChange={handleInputChange('userName')}
            />
            {errors.userName && (
              <p className="text-red-500 text-sm">{errors.userName}</p>
            )}
          </div>

          {/* Name Field */}
          <div className="flex flex-col gap-[8px]">
            <label
              className="font-rubik font-semibold text-white capitalize"
              style={{ fontSize: '14px', fontWeight: 600 }}
            >
              Name
            </label>
            <input
              type="text"
              className={`w-full bg-elevated h-[43px] border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.name ? 'border-red-500' : 'border-border-secondary'
                }`}
              placeholder="Enter full name"
              value={name}
              onChange={handleNameChange}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name}</p>
            )}
          </div>

          {/* Mobile Number Field */}
          <div className="flex flex-col gap-[8px]">
            <label
              className="font-rubik font-semibold text-white capitalize"
              style={{ fontSize: '14px', fontWeight: 600 }}
            >
              Mobile Number
            </label>
            <input
              type="tel"
              className={`w-full h-[43px] bg-elevated border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.mobileNumber ? 'border-red-500' : 'border-border-secondary'
                }`}
              placeholder="Enter mobile number"
              value={mobileNumber}
              onChange={handleMobileNumberChange}
            />
            {errors.mobileNumber && (
              <p className="text-red-500 text-sm">{errors.mobileNumber}</p>
            )}
          </div>

          {/* Password Field */}
          <div className="flex flex-col gap-[8px]">
            <div className="flex items-center justify-between">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                Password {mode === 'create' && <span className="text-red-500">*</span>}
              </label>
              {mode === 'edit' && (
                <button
                  type="button"
                  onClick={() => setShowChangePassword(!showChangePassword)}
                  className="text-[var(--golden)] text-sm font-medium hover:underline"
                >
                  {showChangePassword ? 'Cancel Change' : 'Change Password'}
                </button>
              )}
            </div>
            {(mode === 'create' || showChangePassword) && (
              <input
                type="password"
                className={`w-full   h-[43px]bg-elevated border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.password ? 'border-red-500' : 'border-border-secondary'
                  }`}
                placeholder="Enter password"
                value={password}
                onChange={handlePasswordChange}
              />
            )}
            {errors.password && (
              <p className="text-red-500 text-sm">{errors.password}</p>
            )}
          </div>

          {/* Access Control Toggles */}
          <div className="space-y-4">

            <div className="flex justify-around">
              {/* Kiosk Access Toggle */}
              <AccessToggleSwitch
                id="kiosk-access"
                label="Kiosk Access"
                icon="ri-computer-line"
                checked={kioskAccess}
                onChange={setKioskAccess}
              />

              {/* Website Access Toggle */}
              <AccessToggleSwitch
                id="website-access"
                label="Website Access"
                icon="ri-global-line"
                checked={websiteAccess}
                onChange={setWebsiteAccess}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <div className="mt-auto">
        <PrimaryButton
          type="submit"
          className="w-full"
          loading={isLoading}
          disabled={isLoading}
        >
          {getActionButtonText()}
        </PrimaryButton>
      </div>
    </form>
  );
};

export default UserManagementModalForm;
