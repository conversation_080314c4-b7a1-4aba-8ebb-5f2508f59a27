// shared/UI/modals/ExportCenterModal.tsx - Export Center Modal Component

"use client";

import React, { useCallback, useMemo } from 'react';
import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useExportCenter } from '@/shared/hooks/business/useExportCenter';
import { ExportCenterFilters, ExportCenterResponse } from '@/shared/types/export-types';

// Import global components
import {
  GlobalDataTable,
  GlobalFilterSection,
  SpkErrorMessage,
  SpkLoadingSpinner
} from '@/shared/UI/components';
import { EXPORT_CENTER_FILTERS, DEFAULT_EXPORT_CENTER_VISIBLE_FILTERS } from '@/shared/config/exportCenterFilters';
import BaseModal from './BaseModal';

import { SpkTableColumn } from '@/shared/UI/components';

interface ExportCenterModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialExportCenterResponse?: ExportCenterResponse | undefined;
  initialFilters?: ExportCenterFilters;
}

/**
 * Export Center Modal Component
 * 
 * Modal component that displays CSV export requests and their status
 * Uses BaseModal for consistent styling and behavior
 * 
 * Features:
 * - Filter functionality (search by ID, status)
 * - Table display with download functionality
 * - Pagination support
 * - Real-time status updates
 * - Dark theme styling
 */
const ExportCenterModal: React.FC<ExportCenterModalProps> = ({
  isOpen,
  onClose,
  initialExportCenterResponse = undefined,
  initialFilters
}) => {
  // Use custom hook for all business logic
  const {
    filters,
    exportCenterResponse,
    exportData,
    isLoading,
    isError,
    error,
    isFetching,
    totalRecords,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    isAuthenticated,
    hasHydrated
  } = useExportCenter({
    initialExportCenterResponse,
    initialFilters
  });

  // Memoize table columns for performance - Convert to SpkTableColumn format
  const columns = useMemo((): SpkTableColumn[] => {
    return [
      {
        key: 'id',
        title: 'ID',
        render: (_value: any, record: any) => (
          <div className="font-rubik text-white text-sm font-medium">
            #{record.id}
          </div>
        )
      },
      {
        key: 'type',
        title: 'Type',
        render: (_value: any, record: any) => {
          const type = record.type;
          const recordModule = record.module;
          let displayName = type;

          // Special case for financial reports
          if (type === 'casino_transactions_db' && recordModule === 'financial_report') {
            displayName = 'Financial Report';
          } else if (type === 'cashier_bet_report') {
            displayName = 'Bet Report';
          } else if (type === 'casino_transactions_db') {
            displayName = 'Casino Transactions';
          }

          return (
            <div className="font-rubik text-text-secondary text-sm">
              {displayName}
            </div>
          );
        }
      },
      {
        key: 'createdAt',
        title: 'Date',
        width: "200px",
        render: (_value: any, record: any) => {
          const date = new Date(record.createdAt);
          return (
            <div className="font-rubik text-text-secondary text-sm">
              <div>{date.toLocaleDateString()} : {date.toLocaleTimeString()}</div>
            </div>
          );
        }
      },
      {
        key: 'status',
        title: 'Status',
        render: (_value: any, record: any) => {
          const status = record.status;
          const colorMap: Record<string, string> = {
            pending: 'text-yellow-400 border-yellow-400 bg-yellow-400/10',
            processing: 'text-blue-400 border-blue-400 bg-blue-400/10',
            completed: 'text-green-400 border-green-400 bg-green-400/10',
            failed: 'text-red-400 border-red-400 bg-red-400/10'
          };
          const iconMap: Record<string, string> = {
            pending: 'ri-time-line',
            processing: 'ri-loader-4-line',
            completed: 'ri-check-line',
            failed: 'ri-close-line'
          };

          const colorClass = colorMap[status] || colorMap.pending;
          const iconClass = iconMap[status] || iconMap.pending;

          return (
            <div className={`${colorClass} min-w-[100px] border font-rubik text-xs px-2 py-1 rounded-md flex items-center gap-1.5 w-fit`}>
              <i className={`${iconClass} text-xs ${status === 'processing' ? 'animate-spin' : ''}`}></i>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </div>
          );
        }
      },
      {
        key: 'fullDownloadUrl',
        title: 'Download',
        render: (_value: any, record: any) => {
          const isCompleted = record.status === 'completed';
          const hasDownloadUrl = !!record.fullDownloadUrl;
          const hasCsvPath = !!record.downloadUrl;

          // Use the proxy API for download, passing the csv path as a query param
          const proxyUrl = `/api/proxy-csv?file=${encodeURIComponent(record.downloadUrl)}`;

          // Determine if the button should be disabled
          const isDisabled = !isCompleted || !hasDownloadUrl || !hasCsvPath;

          return (
            <div className="flex items-start justify-start">
              <a
                href={isDisabled ? undefined : proxyUrl}
                download={!isDisabled}
                rel="noopener noreferrer"
                tabIndex={isDisabled ? -1 : 0}
                aria-disabled={isDisabled}
                onClick={isDisabled ? (e) => e.preventDefault() : undefined}
                className={`bg-gradient-to-r from-[var(--golden)]  to-[#8A5911]  rounded-md text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1 font-rubik transition-all duration-200 ${isDisabled ? 'opacity-10 cursor-not-allowed pointer-events-none' : 'hover:from-[#E3B84B] hover:to-[#8A5911]'}`}
              >
                <i className="ri-download-line text-xs"></i>
              </a>
            </div>
          );
        }
      }
    ];
  }, []);

  // Handle items per page change - optimized with useCallback
  const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
    handleFilterChange({ size: itemsPerPage });
  }, [handleFilterChange]);

  // Handle page change with tracking
  const handlePageChangeWithTracking = useCallback((page: number) => {
    handlePageChange(page);
  }, [handlePageChange]);

  // Don't render if not authenticated and hydrated
  if (hasHydrated && !isAuthenticated) {
    return null;
  }

  // Loading state
  if (isLoading && !exportCenterResponse) {
    return (
      <BaseModal
        isOpen={isOpen}
        onClose={onClose}
        title="Export Center"
        size="xl"
        headerIcon={
          <div className="w-6 h-6 flex items-center justify-center">
            <i className="ri-download-cloud-line text-[var(--golden)] text-lg"></i>
          </div>
        }
      >
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <SpkLoadingSpinner size="lg" />
        </div>
      </BaseModal>
    );
  }

  // Error state
  if (isError && error) {
    return (
      <BaseModal
        isOpen={isOpen}
        onClose={onClose}
        title="Export Center"
        size="xl"
        headerIcon={
          <div className="w-6 h-6 flex items-center justify-center">
            <i className="ri-download-cloud-line text-[var(--golden)] text-lg"></i>
          </div>
        }
      >
        <div className="p-6">
          <SpkErrorMessage
            message={error.message}
            onRetry={handleRefresh}
            variant="box"
            size="md"
          />
        </div>
      </BaseModal>
    );
  }

  // exportData is now provided by the hook

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Export Center"
      size="xl"
      headerIcon={
        <div className="w-6 h-6 flex items-center justify-center">
          <i className="ri-download-cloud-line text-[var(--golden)] text-lg"></i>
        </div>
      }
      bodyClassName="p-0 h-auto"
    >
      <div className={`${fadeInStyles.fadeIn} bg-background h-auto`}>
        {/* Filter Section */}
        <div className="p-6 border-b border-border-secondary">
          <GlobalFilterSection
            filters={filters}
            onFilterChange={handleFilterChange}
            availableFilters={EXPORT_CENTER_FILTERS}
            defaultVisibleFilters={DEFAULT_EXPORT_CENTER_VISIBLE_FILTERS}
            showRefreshButton={true}
            onRefresh={handleRefresh}
            isRefreshing={isFetching}
          />
        </div>

        {/* Table Section */}
        <div className="p-6">
          <GlobalDataTable
            data={exportData}
            columns={columns}
            isLoading={isLoading || isFetching}
            totalItems={totalRecords}
            currentPage={filters.page}
            itemsPerPage={filters.size}
            onPageChange={handlePageChangeWithTracking}
            onItemsPerPageChange={handleItemsPerPageChange}
            emptyText="No export requests found"
            showPagination={true}
            scrollableBody={true}
            maxBodyHeight="50vh"
            className="min-h-[400px]"
          />
        </div>
      </div>
    </BaseModal>
  );
};

export default ExportCenterModal;
