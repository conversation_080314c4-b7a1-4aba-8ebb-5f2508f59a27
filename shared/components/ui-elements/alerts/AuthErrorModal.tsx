import React, { Fragment, useEffect } from 'react';
import SpkButton from '@/shared/@spk-reusable-components/uielements/spk-button';
import { SpkAlert } from '@/shared/UI/components';

export interface AuthErrorModalProps {
  /** Whether the modal is open/visible */
  isOpen: boolean;
  /** Function called when user acknowledges the error */
  onClose: () => void;
  /** Custom error message to display */
  message?: string;
  /** Custom title for the modal */
  title?: string;
  /** Whether to show the modal backdrop */
  showBackdrop?: boolean;
  /** Additional CSS classes for the modal */
  className?: string;
}

const AuthErrorModal: React.FC<AuthErrorModalProps> = ({
  isOpen,
  onClose,
  message = "Your session has expired or you are not authorized to access this resource. Please sign in again to continue.",
  title = "Authentication Required",
  showBackdrop = true,
  className = ''
}) => {
  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const errorIcon = (
    <div className="text-red-400 text-4xl mb-2">
      <i className="ri-error-warning-line"></i>
    </div>
  );

  return (
    <Fragment>
      <div
        className={`
          hs-overlay ti-modal 
          ${isOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}
          size-full fixed top-0 start-0 z-[999] 
          overflow-x-hidden transition-all overflow-y-auto
          ${className}
        `}
        tabIndex={-1}
        role="dialog"
        aria-labelledby="auth-error-modal-title"
        aria-describedby="auth-error-modal-description"
        aria-hidden={!isOpen}
      >
        {/* Backdrop */}
        {showBackdrop && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-[-1]"
            onClick={handleBackdropClick}
            aria-hidden="true"
          />
        )}

        {/* Modal Container */}
        <div className="ti-modal-box">
          <div className="ti-modal-dialog">
            <div className="ti-modal-content">
              {/* Modal Header */}
              <div className="ti-modal-header">
                <h3
                  id="auth-error-modal-title"
                  className="ti-modal-title text-lg font-semibold"
                >
                  {title}
                </h3>
                <SpkButton
                  type="button"
                  customClass="ti-modal-close-btn"
                  onclickfunc={onClose}
                  aria-label="Close modal"
                >
                  <i className="ri-close-line"></i>
                </SpkButton>
              </div>

              {/* Modal Body */}
              <div className="ti-modal-body">
                <div className="text-center">
                  <SpkAlert
                    variant="danger"
                    style="modern"
                    bordered={true}
                    borderPosition="left"
                    icon={errorIcon}
                    title="Session Expired"

                    className="max-w-md mx-auto border-none"
                    id="auth-error-modal-description"
                  >
                    {message}
                  </SpkAlert>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="ti-modal-footer">
                <SpkButton
                  type="button"
                  variant="secondary"
                  customClass="ti-btn ti-btn-secondary me-2"
                  onclickfunc={onClose}
                >
                  Cancel
                </SpkButton>
                <SpkButton
                  type="button"
                  variant="primary"
                  customClass="ti-btn ti-btn-primary"
                  onclickfunc={onClose}
                >
                  Sign In Again
                </SpkButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default AuthErrorModal;
