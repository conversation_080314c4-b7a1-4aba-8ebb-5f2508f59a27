// shared/hooks/business/useBulkUpload.ts - Business logic hook for bulk upload functionality

import { useCallback, useState } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { useToast } from '@/shared/UI/components';

interface UseBulkUploadReturn {
  downloadSampleCsv: () => Promise<void>;
  uploadCsv: (file: File) => Promise<void>;
  isDownloadingSample: boolean;
  isUploading: boolean;
}

/**
 * Custom hook for bulk upload functionality
 * Handles sample CSV download and CSV file upload with proper error handling
 */
export const useBulkUpload = (): UseBulkUploadReturn => {
  const [isDownloadingSample, setIsDownloadingSample] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const { showSuccess, showError } = useToast();

  // Download sample CSV
  const downloadSampleCsv = useCallback(async () => {
    setIsDownloadingSample(true);
    try {
      const token = useAuthStore.getState().token;

      if (!token) {
        throw new Error('Authentication token is required');
      }

      const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

      if (!baseUrl) {
        throw new Error('Admin API base URL is not configured');
      }

      // Prepare payload for sample CSV download
      const payload = {
        payload: JSON.stringify({}), // Empty payload for sample
        module: 'players',
        type: 'sample_user_rfid_list'
      };

      const response = await fetch(`${baseUrl}/api/admin/exportCsvCenter/store`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to download sample CSV: ${response.status}`);
      }

      const result = await response.json();

      // Check if the response contains a download URL or file data
      if (result.success && result.data?.csv_url) {
        // If we get a CSV URL, trigger download
        const csvUrl = result.data.csv_url;
        const link = document.createElement('a');
        link.href = csvUrl;
        link.download = 'sample_user_rfid_list.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showSuccess('Sample CSV Downloaded', 'Sample CSV file has been downloaded successfully.');
      } else if (result.success) {
        // If successful but no direct download, show success message
        showSuccess('Sample CSV Request Submitted', 'Sample CSV generation request has been submitted. Check the Export Center for download.');
      } else {
        throw new Error(result.message || 'Failed to generate sample CSV');
      }

    } catch (error) {
      // Log error for debugging in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Sample CSV download failed:', error);
      }
      showError(
        'Download Failed',
        error instanceof Error ? error.message : 'Failed to download sample CSV. Please try again.'
      );
    } finally {
      setIsDownloadingSample(false);
    }
  }, [showSuccess, showError]);

  // Upload CSV file
  const uploadCsv = useCallback(async (file: File) => {
    setIsUploading(true);
    try {
      const token = useAuthStore.getState().token;

      if (!token) {
        throw new Error('Authentication token is required');
      }

      const baseUrl = 'https://reporting.ingrandstation.com';

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('csvFile', file);

      const response = await fetch(`${baseUrl}/api/v2/cashier/bulk-user-create/rfid-update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          // Don't set Content-Type header - let browser set it with boundary for FormData
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = `Upload failed with status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // If response is not JSON, use status message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();

      if (result.success) {
        showSuccess(
          'Upload Successful',
          `CSV file uploaded successfully. ${result.data?.processed_count ? `Processed ${result.data.processed_count} records.` : ''}`
        );
      } else {
        throw new Error(result.message || 'Upload failed');
      }

    } catch (error) {
      // Log error for debugging in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('CSV upload failed:', error);
      }
      showError(
        'Upload Failed',
        error instanceof Error ? error.message : 'Failed to upload CSV file. Please try again.'
      );
      throw error; // Re-throw to allow caller to handle
    } finally {
      setIsUploading(false);
    }
  }, [showSuccess, showError]);

  return {
    downloadSampleCsv,
    uploadCsv,
    isDownloadingSample,
    isUploading
  };
};
