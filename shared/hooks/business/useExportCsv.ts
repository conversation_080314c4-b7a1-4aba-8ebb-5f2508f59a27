// shared/hooks/business/useExportCsv.ts - Business logic hook for CSV export functionality

import { useCallback } from 'react';
import { useExportRequestMutation } from '@/shared/query/useExportQuery';
import { useToast } from '@/shared/UI/components';
import {
  ExportRequestPayload,
  ExportModuleType,
  ExportDataType
} from '@/shared/types/export-types';
import {
  CashierReportFilters,
  BetReportFilters,
  FinancialReportFilters
} from '@/shared/types/report-types';
import { UserListFilters, LoginHistoryFilters } from '@/shared/types/user-management-types';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';

// Type for all possible filter types - Extended to include user management and login history
type ReportFilters = CashierReportFilters | BetReportFilters | FinancialReportFilters | UserListFilters | LoginHistoryFilters;

interface UseExportCsvProps {
  module: ExportModuleType;
  type: ExportDataType;
}

interface UseExportCsvReturn {
  exportCsv: (filters: ReportFilters) => Promise<void>;
  isExporting: boolean;
}

/**
 * Custom hook for CSV export functionality
 * Handles the business logic for exporting data from different report pages
 */
export const useExportCsv = ({ module, type }: UseExportCsvProps): UseExportCsvReturn => {
  const { showSuccess, showError } = useToast();
  const exportMutation = useExportRequestMutation();

  // Format filters for casino transactions (cashier report)
  const formatCasinoTransactionFilters = useCallback((filters: CashierReportFilters) => {
    return {
      size: filters.size || 25,
      page: filters.page || 1,
      search: filters.search || '',
      transactionId: filters.transactionId || '',
      debitTransactionId: filters.debitTransactionId || '',
      roundId: filters.roundId || '',
      tenantId: filters.tenantId || '',
      order: filters.order || 'desc',
      sortBy: filters.sortBy || 'created_at',
      actionType: transformActionTypeForApi(filters.actionType) || '',
      timePeriod: filters.timePeriod || '',
      userId: filters.userId || '',
      agentId: filters.agentId || '',
      timeZone: filters.timeZone || 'UTC +05:30',
      timeZoneName: filters.timeZoneName || 'Asia/Colombo',
      dateTime: filters.dateTime || ''
    };
  }, []);

  // Format filters for financial report
  const formatFinancialReportFilters = useCallback((filters: FinancialReportFilters) => {
    return {
      size: filters.size || 25,
      page: filters.page || 1,
      search: filters.search || '',
      amount: filters.amount || '',
      transactionId: filters.transactionId || '',
      debitTransactionId: filters.debitTransactionId || '',
      roundId: filters.roundId || '',
      utrNumber: filters.utrNumber || '',

      tenantId: filters.tenantId || '',
      order: filters.order || 'desc',
      sortBy: filters.sortBy || 'created_at',
      currencyId: filters.currencyId || '',
      actionType: transformActionTypeForApi(filters.actionType) || '',
      actionCategory: 'financial', // Always set for financial reports
      timePeriod: filters.timePeriod || '',
      playerId: filters.playerId || '',
      timeZone: filters.timeZone || 'UTC +05:30',
      timeZoneName: filters.timeZoneName || 'Asia/Colombo',
      gameProvider: filters.gameProvider || '',
      gameType: filters.gameType || '',

      dateTime: filters.dateTime || ''
    };
  }, []);

  // Format filters for bet report
  const formatBetReportFilters = useCallback((filters: BetReportFilters) => {
    return {
      page: filters.page?.toString() || '1',
      limit: filters.limit?.toString() || '10',
      startDate: filters.startDate || '',
      endDate: filters.endDate || '',
      status: filters.status || '',
      payoutStatus: filters.payoutStatus || '',
      transactionId: filters.transactionId || '',
      playerId: filters.playerId || '',
      marketId: filters.marketId || ''
    };
  }, []);

  // Format user management filters for API
  const formatUserManagementFilters = useCallback((filters: UserListFilters) => {
    return {
      page: filters.page?.toString() || '1',
      size: filters.size?.toString() || '10',
      search: filters.search || '',
      status: filters.status || '',
      // Add other user management specific filters as needed
    };
  }, []);

  // Format login history filters for API
  const formatLoginHistoryFilters = useCallback((filters: LoginHistoryFilters) => {
    return {
      page: filters.page?.toString() || '1',
      limit: filters.limit?.toString() || '10',
      startDate: filters.startDate || '',
      endDate: filters.endDate || '',
      playerId: filters.playerId || '',
      // Add other login history specific filters as needed
    };
  }, []);

  // Main export function
  const exportCsv = useCallback(async (filters: ReportFilters) => {
    try {
      let formattedFilters: any;

      // Format filters based on module type - Extended to include user management and login history
      switch (module) {
        case 'casino_transactions':
          formattedFilters = formatCasinoTransactionFilters(filters as CashierReportFilters);
          break;
        case 'financial_report':
          formattedFilters = formatFinancialReportFilters(filters as FinancialReportFilters);
          break;
        case 'bet_report':
          formattedFilters = formatBetReportFilters(filters as BetReportFilters);
          break;
        case 'players':
          if (type === 'player_list') {
            formattedFilters = formatUserManagementFilters(filters as UserListFilters);
          } else if (type === 'user_login_history') {
            formattedFilters = formatLoginHistoryFilters(filters as LoginHistoryFilters);
          } else {
            throw new Error(`Unsupported export type for players module: ${type}`);
          }
          break;
        default:
          throw new Error(`Unsupported module type: ${module}`);
      }

      // Create export request payload
      const payload: ExportRequestPayload = {
        payload: JSON.stringify(formattedFilters),
        module,
        type
      };

      // Submit export request
      await exportMutation.mutateAsync(payload);

      // Show success notification
      showSuccess(
        'Export Request Submitted',
        'Your CSV export request has been submitted successfully. You can check the progress in the Export Center.'
      );

    } catch (error) {
      // Log error for debugging in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Export failed:', error);
      }
      showError(
        'Export Failed',
        error instanceof Error ? error.message : 'Failed to submit export request. Please try again.'
      );
    }
  }, [
    module,
    type,
    exportMutation,
    showSuccess,
    showError,
    formatCasinoTransactionFilters,
    formatFinancialReportFilters,
    formatBetReportFilters,
    formatUserManagementFilters,
    formatLoginHistoryFilters
  ]);

  return {
    exportCsv,
    isExporting: exportMutation.isPending
  };
};
