"use client";

import React, { useEffect, useState } from 'react';

export default function FontTestPage() {
  const [fontTests, setFontTests] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const testFontFiles = async () => {
      const fontFiles = [
        '/assets/icon-fonts/RemixIcons/fonts/remixicon.woff2',
        '/assets/icon-fonts/RemixIcons/fonts/remixicon.woff',
        '/assets/icon-fonts/RemixIcons/fonts/remixicon.ttf',
        '/assets/icon-fonts/RemixIcons/fonts/remixicon.css'
      ];

      const results: { [key: string]: boolean } = {};

      for (const fontFile of fontFiles) {
        try {
          const response = await fetch(fontFile);
          results[fontFile] = response.ok;
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(`Failed to fetch ${fontFile}:`, error);
          results[fontFile] = false;
        }
      }

      setFontTests(results);
    };

    testFontFiles();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Font File Accessibility Test</h1>

      {/* Font File Tests */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Font File Accessibility</h2>
        <div className="space-y-2">
          {Object.entries(fontTests).map(([file, accessible]) => (
            <div key={file} className={`p-3 rounded ${accessible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              <div className="font-mono text-sm">{file}</div>
              <div className="text-xs">{accessible ? '✅ Accessible' : '❌ Not Accessible'}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Direct Icon Tests */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Direct Icon Tests</h2>
        <div className="grid grid-cols-6 gap-4">
          {[
            'ri-home-line',
            'ri-user-line',
            'ri-settings-line',
            'ri-search-line',
            'ri-heart-line',
            'ri-arrow-down-s-line'
          ].map((iconClass) => (
            <div key={iconClass} className="text-center p-4 border rounded">
              <i className={`${iconClass} text-3xl block mb-2`}></i>
              <div className="text-xs font-mono">{iconClass}</div>
            </div>
          ))}
        </div>
      </div>

      {/* CSS Font Family Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">CSS Font Family Test</h2>
        <div className="p-4 bg-gray-50 rounded">
          <div style={{ fontFamily: 'remixicon', fontSize: '24px' }}>
            This text should use RemixIcon font family
          </div>
        </div>
      </div>

      {/* Browser Font Loading API Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Browser Font Loading API</h2>
        <div className="p-4 bg-gray-50 rounded space-y-2">
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded mr-2"
            onClick={() => {
              if (document.fonts && document.fonts.check) {
                const isLoaded = document.fonts.check('16px remixicon');
                alert(`RemixIcon font loaded: ${isLoaded}`);
              } else {
                alert('Font Loading API not supported');
              }
            }}
          >
            Check Font Loading Status
          </button>
          <button
            className="px-4 py-2 bg-green-500 text-white rounded"
            onClick={async () => {
              try {
                const response = await fetch('/assets/icon-fonts/RemixIcons/fonts/remixicon.woff2');
                alert(`WOFF2 file accessible: ${response.ok} (Status: ${response.status})`);
              } catch (error) {
                alert(`Error accessing WOFF2 file: ${error}`);
              }
            }}
          >
            Test WOFF2 File Access
          </button>
        </div>
      </div>
    </div>
  );
}
