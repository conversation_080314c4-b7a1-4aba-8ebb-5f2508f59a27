"use client";

import React from 'react';

export default function SimpleIconTest() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Simple Icon Test</h1>
      
      {/* Direct icon test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Direct RemixIcon Test</h2>
        <div className="flex items-center gap-4 text-4xl">
          <i className="ri-home-line text-blue-500"></i>
          <i className="ri-user-line text-green-500"></i>
          <i className="ri-settings-line text-purple-500"></i>
          <i className="ri-search-line text-orange-500"></i>
          <i className="ri-heart-line text-red-500"></i>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          If you see colored icons above, RemixIcon is working correctly.
          If you see only text/squares, there's still a loading issue.
        </p>
      </div>

      {/* CSS Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">CSS Font Family Test</h2>
        <div className="p-4 bg-gray-100 rounded">
          <div style={{ fontFamily: 'remixicon', fontSize: '24px' }}>
            This should show RemixIcon font if loaded
          </div>
        </div>
      </div>

      {/* Network Status */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Check Network Tab</h2>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm">
            Open browser DevTools (F12) → Network tab → Refresh this page
          </p>
          <p className="text-sm mt-1">
            You should see requests for:
          </p>
          <ul className="text-xs mt-2 list-disc list-inside">
            <li>remixicon.css from cdnjs.cloudflare.com</li>
            <li>tabler-icons.min.css from cdn.jsdelivr.net</li>
            <li>Font files (.woff2, .woff, .ttf)</li>
          </ul>
        </div>
      </div>

      {/* Manual Test Buttons */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Manual Tests</h2>
        <div className="space-x-4">
          <button 
            className="px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => {
              const computed = getComputedStyle(document.querySelector('.ri-home-line') as Element);
              alert(`Font family: ${computed.fontFamily}`);
            }}
          >
            Check Font Family
          </button>
          <button 
            className="px-4 py-2 bg-green-500 text-white rounded"
            onClick={() => {
              if (document.fonts && document.fonts.check) {
                const loaded = document.fonts.check('16px remixicon');
                alert(`RemixIcon loaded: ${loaded}`);
              } else {
                alert('Font Loading API not supported');
              }
            }}
          >
            Check Font Loading
          </button>
        </div>
      </div>
    </div>
  );
}
