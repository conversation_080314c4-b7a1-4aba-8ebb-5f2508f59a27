

:root {
	--body-bg: 											249 250 252;
	--primary: 											75 85 200;
	--primary-rgb: 										75, 85, 200;
	--primary-tint1-rgb: 								227 84 212;
	--primary-tint2-rgb: 								255 93 159;
	--primary-tint3-rgb: 								255 142 111;
	--secondary: 										158 92 247;
	--warning: 											204 133 0;
	--info: 											14 165 232;
	--success: 											0 150 100;
	--danger: 											180 30 30;
	--light: 											249 249 250;
	--dark: 											10 10 10;
	--orange: 											253 97 43;
	--pink: 											254 84 155;
	--teal: 											0 216 216;
	--purple: 											123 118 254;
	--green: 											1 239 140;
	--default-body-bg-color: 							249 250 252;
	--default-text-color: 							  	33 43 55;
	--default-border: 									#FFFFFF33;
	--default-background: 							  	249 250 251;
	--menu-bg: 											255 255 255;
	--menu-prime-color: 								97 116 143;
	--menu-border-color: 								226 230 241;
	--header-bg: 										255 255 255;
	--header-prime-color: 								97 116 143;
	--header-border-color: 								226 230 241;
	--custom-white: 									255 255 255;
	--custom-black: 								  	0 0 0;
	--list-hover-focus-bg: 							  	245 246 247;
	--text-muted: 										110 130 159;
	--input-border: 									222 231 241;
	--form-control-bg: 								  	255 255 255;
	--bs-link-color-rgb: 							  	33 43 55;
	--facebook: 										59 89 152;
    --twitter: 											0 172 238;
    --github: 											51 51 51;    
    --google: 											207 78 67;
    --youtube: 											255 0 0;
}


/* dark mode - NEW DARK THEME COLOR SYSTEM */
.dark{
	/* === NEW LAYERED BACKGROUND SYSTEM === */
	/* Layer 1: Main body background */
	--body-bg: 									        15 15 15;	/* #0F0F0F - Main body background */
	--default-body-bg-color: 							15 15 15;	/* #0F0F0F - Body background (legacy) */

	/* Layer 2: Navigation & Section backgrounds */
	--dark-bg: 									        29 29 29;	/* #1D1D1D - Navigation background */
	--menu-bg: 											29 29 29;	/* #1D1D1D - Navigation/Menu background */
	--header-bg: 										29 29 29;	/* #1D1D1D - Header background */
	--default-background: 								29 29 29;	/* #1D1D1D - Default background */
	--form-control-bg: 									44 44 47;	/* #2C2C2F - Form control background (updated to match form-input-bg) */
	--custom-white: 									29 29 29;	/* #1D1D1D - Custom white override */

	/* Layer 3: Section headers/titles and elevated content */
	--elevated-bg: 										39 39 41;	/* #272729 - Elevated content areas */
	--section-bg: 										39 39 41;	/* #272729 - Filter headings background */
	--filter-bg: 										29 29 31;	/* #1D1D1F - Filter background */
	--table-section-bg: 								29 29 31;	/* #1D1D1F - Table section background */
	--table-total-bg: 									73 76 114;	/* #494C72 - Table total/pagination background */
	--table-head-bg: 									49 52 82;	/* #313452 - Table header (thead) background */

	/* Form and Modal Styling */
	--form-input-bg: 									44 44 47;	/* #2C2C2F - Form input background */
	--form-head-bg: 									39 39 41;	/* #272729 - Form head/header background */
	--form-bg: 											29 29 31;	/* #1D1D1F - Form background */
	--modal-header-bg: 									39 39 41;	/* #272729 - Modal header background */

	/* NEW TEXT COLORS */
	--text-filter-heading: 								255 255 255;	/* #FFFFFF - Filter headings, thead text, primary button text */
	--text-filter-label: 								174 174 174;	/* #AEAEAE - Filter input labels */
	--text-filter-placeholder: 						97 97 97;		/* #616161 - Filter input placeholders */
	--text-table-body: 									153 153 153;	/* #999999 - Table body text */

	/* NEW BORDER COLORS */
	--border-filter-heading: 							51 51 51;		/* #333333 - Filter heading border-bottom */
	--border-filter-input: 								255 255 255;	/* #FFFFFF33 - Filter input borders (33% opacity white) */
	--border-table-row: 								196 196 196;	/* #C4C4C41A - Table row border-bottom (10% opacity white) */

	/* Legacy text and border colors (updated for consistency) */
	--menu-border-color: 								51 51 51;		/* #333333 */
	--menu-prime-color: 								255 255 255;	/* #FFFFFF */
	--header-prime-color: 								255 255 255;	/* #FFFFFF */
	--header-border-color: 								51 51 51;		/* #333333 */
	--custom-black: 									255 255 255;	/* #FFFFFF */
	--default-border: 									51 51 51;		/* #333333 */
	--default-text-color: 								255 255 255;	/* #FFFFFF */
	--light:								     		43 46 49;		/* Keep existing */
	--dark: 											249 249 250;	/* Keep existing */
	--list-hover-focus-bg: 								39 39 41;		/* #272729 */
	--text-muted: 										174 174 174;	/* #AEAEAE */
	--input-border: 									255 255 255;	/* #FFFFFF33 (will use opacity in components) */
}

[data-menu-styles="dark"][class="light"] {
	--menu-bg: 										32 41 71;
	--menu-prime-color: 							178 184 199;
}
[data-menu-styles=dark] {
    --menu-border-color: 							255, 255, 255, 0.1;
}
[data-menu-styles="color"] {
	--menu-bg: 										92 103 247;
	--menu-prime-color: 							255 255 255;
	--menu-border-color: 							255 255 255;
}
