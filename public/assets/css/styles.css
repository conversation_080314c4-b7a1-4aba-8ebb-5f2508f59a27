@charset "UTF-8";
/*------------------------------------------------------------------
[Master Stylesheet]

Project                            :   Xintra - NextJs App-Router <PERSON>l<PERSON>Css Admin Dashboard Template
Create Date                        :   03/03/2025
Author & Copyright Ownership       :   Spruko Technologies Private Limited 
Author URL                         :   https://themeforest.net/user/spruko
Support	                           :   https://support.spruko.com/
License Details                    :   https://spruko.com/licenses-details
------------------------------------------------------------------*/
/* Table Of Content 
variables
switcher
accordion
alerts
badge
breadcrumb
buttons
cards
dropdown
forms
input_group
list_group
modals
navbar
navs_tabs
pagination
popovers
progress
tables
toast
tooltips
authentication
custom
dashboard_styles
error
header
plugins
ribbons
widgets
closed_menu
detached_menu
double_menu
horizontal
icon_click
icon_hover
icon_overlay
icontext
menu_click
menu_hover
vertical
chat
ecommerce
file-manager
landing
mail
task
avatars
background
border
opacity
typography
*/
/* FONT */
/* Poppins */
:root {
  --body-bg:	249 250 252;
  --primary:	75 85 200;
  --primary-rgb:	75, 85, 200;
  --primary-tint1-rgb:	227 84 212;
  --primary-tint2-rgb:	255 93 159;
  --primary-tint3-rgb:	255 142 111;
  --secondary:	158 92 247;
  --warning:	204 133 0;
  --info:	14 165 232;
  --success:	0 150 100;
  --danger:	180 30 30;
  --light:	249 249 250;
  --dark:	10 10 10;
  --orange:	253 97 43;
  --pink:	254 84 155;
  --teal:	0 216 216;
  --purple:	123 118 254;
  --green:	1 239 140;
  --default-body-bg-color:	249 250 252;
  --default-text-color:	33 43 55;
  --default-border:	236 243 251;
  --default-background:	249 250 251;
  --menu-bg:	255 255 255;
  --menu-prime-color:	97 116 143;
  --menu-border-color:	226 230 241;
  --header-bg:	255 255 255;
  --header-prime-color:	97 116 143;
  --header-border-color:	226 230 241;
  --custom-white:	255 255 255;
  --custom-black:	0 0 0;
  --list-hover-focus-bg:	245 246 247;
  --text-muted:	110 130 159;
  --input-border:	222 231 241;
  --form-control-bg:	255 255 255;
  --bs-link-color-rgb:	33 43 55;
  --facebook:	59 89 152;
  --twitter:	0 172 238;
  --github:	51 51 51;
  --google:	207 78 67;
  --youtube:	255 0 0;
}

/* dark mode - NEW DARK THEME COLOR SYSTEM */
.dark {
  /* === NEW LAYERED BACKGROUND SYSTEM === */
  /* Layer 1: Main body background */
  --body-bg: 15 15 15; /* #0F0F0F - Main body background */
  --default-body-bg-color:	15 15 15; /* #0F0F0F - Body background (legacy) */
  /* Layer 2: Navigation & Section backgrounds */
  --dark-bg: 29 29 29; /* #1D1D1D - Navigation background */
  --menu-bg:	29 29 29; /* #1D1D1D - Navigation/Menu background */
  --header-bg:	29 29 29; /* #1D1D1D - Header background */
  --default-background:	29 29 29; /* #1D1D1D - Default background */
  --form-control-bg:	44 44 47; /* #2C2C2F - Form control background (updated to match form-input-bg) */
  --custom-white:	29 29 29; /* #1D1D1D - Custom white override */
  /* Layer 3: Section headers/titles and elevated content */
  --elevated-bg:	39 39 41; /* #272729 - Elevated content areas */
  --section-bg:	39 39 41; /* #272729 - Filter headings background */
  --filter-bg:	29 29 31; /* #1D1D1F - Filter background */
  --table-section-bg:	29 29 31; /* #1D1D1F - Table section background */
  --table-total-bg:	73 76 114; /* #494C72 - Table total/pagination background */
  --table-head-bg:	49 52 82; /* #313452 - Table header (thead) background */
  /* Form and Modal Styling */
  --form-input-bg:	44 44 47; /* #2C2C2F - Form input background */
  --form-head-bg:	39 39 41; /* #272729 - Form head/header background */
  --form-bg:	29 29 31; /* #1D1D1F - Form background */
  --modal-header-bg:	39 39 41; /* #272729 - Modal header background */
  /* NEW TEXT COLORS */
  --text-filter-heading:	255 255 255; /* #FFFFFF - Filter headings, thead text, primary button text */
  --text-filter-label:	174 174 174; /* #AEAEAE - Filter input labels */
  --text-filter-placeholder:	97 97 97; /* #616161 - Filter input placeholders */
  --text-table-body:	153 153 153; /* #999999 - Table body text */
  /* NEW BORDER COLORS */
  --border-filter-heading:	51 51 51; /* #333333 - Filter heading border-bottom */
  --border-filter-input:	255 255 255; /* #FFFFFF33 - Filter input borders (33% opacity white) */
  --border-table-row:	196 196 196; /* #C4C4C41A - Table row border-bottom (10% opacity white) */
  /* Legacy text and border colors (updated for consistency) */
  --menu-border-color:	51 51 51; /* #333333 */
  --menu-prime-color:	255 255 255; /* #FFFFFF */
  --header-prime-color:	255 255 255; /* #FFFFFF */
  --header-border-color:	51 51 51; /* #333333 */
  --custom-black:	255 255 255; /* #FFFFFF */
  --default-border:	51 51 51; /* #333333 */
  --default-text-color:	255 255 255; /* #FFFFFF */
  --light:	43 46 49; /* Keep existing */
  --dark:	249 249 250; /* Keep existing */
  --list-hover-focus-bg:	39 39 41; /* #272729 */
  --text-muted:	174 174 174; /* #AEAEAE */
  --input-border:	255 255 255; /* #FFFFFF33 (will use opacity in components) */
}

[data-menu-styles=dark][class=light] {
  --menu-bg:	32 41 71;
  --menu-prime-color:	178 184 199;
}

[data-menu-styles=dark] {
  --menu-border-color:	255, 255, 255, 0.1;
}

[data-menu-styles=color] {
  --menu-bg:	92 103 247;
  --menu-prime-color:	255 255 255;
  --menu-border-color:	255 255 255;
}

/* Only import the icon fonts that are actually used in the application */
/* RemixIcon now loaded via CDN in globals.scss to avoid font file resolution issues */
/* CUSTOM */
/**
 * Clean Theme Variables
 * 
 * This file defines all CSS variables used by the clean Tailwind config.
 * All colors are defined as RGB values for use with opacity modifiers.
 */
:root {
  --primary: 225 182 73;
  --primary-dark: 184 134 11;
  --secondary: 107 114 128;
  --success: 16 185 129;
  --warning: 245 158 11;
  --danger: 239 68 68;
  --info: 59 130 246;
  --golden: 225 182 73;
  --golden-dark: 184 134 11;
  --background: 15 15 15;
  --bodybg: 29 29 29;
  --nav: 29 29 29;
  --section: 39 39 41;
  --elevated: 39 39 41;
  --form-input: 44 44 47;
  --form-bg: 29 29 31;
  --table-section: 29 29 31;
  --table-head: 49 52 82;
  --modal-header: 39 39 41;
  --defaulttextcolor: 255 255 255;
  --textmuted: 153 153 153;
  --defaultborder: 51 51 51;
  --success-notification: 11 83 59;
  --error-notification: 105 0 0;
  --warning-notification: 114 73 2;
  --info-notification: 45 45 47;
  --success-message: 52 211 153;
  --error-message: 248 113 113;
  --warning-message: 251 191 36;
  --info-message: 209 213 219;
}

[data-theme=light] {
  --background: 255 255 255;
  --bodybg: 249 250 251;
  --nav: 255 255 255;
  --section: 249 250 251;
  --elevated: 255 255 255;
  --form-input: 255 255 255;
  --form-bg: 249 250 251;
  --table-section: 249 250 251;
  --table-head: 243 244 246;
  --modal-header: 255 255 255;
  --defaulttextcolor: 17 24 39;
  --textmuted: 107 114 128;
  --defaultborder: 229 231 235;
}

.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.golden-gradient {
  background: linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)));
}

.text-gradient-golden {
  background: linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (max-width: 768px) {
  :root {
    --container-padding: 1rem;
  }
}
@media (min-width: 1200px) {
  :root {
    --container-padding: 2rem;
  }
}
/* Start:: authentication */
.authentication {
  @apply min-h-screen;
}
.authentication .authentication-brand.desktop-dark {
  @apply block;
}
.authentication .form-control {
  @apply pe-10;
}
.authentication .swiper-button-next,
.authentication .swiper-button-prev {
  @apply bg-[rgba(255,255,255,0.05)] text-[rgba(255,255,255,0.5)] !important;
}
.authentication .swiper-pagination-bullet {
  @apply opacity-10;
}
.authentication .swiper-pagination-bullet-active {
  @apply opacity-50;
}
.authentication .google-svg {
  @apply w-3 h-3 me-2;
}
.authentication .authentication-barrier {
  @apply relative;
}
.authentication .authentication-barrier span {
  @apply relative z-[2];
}
.authentication .authentication-barrier:before {
  @apply absolute content-[""] w-[45%] h-0.5 bg-[linear-gradient(to_left,transparent,light)] end-[-35px] z-[1] rounded-[50%] top-2.5;
}
.authentication .authentication-barrier:after {
  @apply absolute content-[""] w-[45%] h-0.5 bg-[linear-gradient(to_left,light,transparent)] start-[-35px] z-[1] rounded-[50%] top-2.5;
}
.authentication.coming-soon .authentication-cover, .authentication.under-maintenance .authentication-cover {
  @apply bg-none bg-white dark:bg-bodybg;
}
.authentication.coming-soon .authentication-cover:before, .authentication.coming-soon .authentication-cover:after, .authentication.under-maintenance .authentication-cover:before, .authentication.under-maintenance .authentication-cover:after {
  @apply hidden;
}
.authentication.coming-soon .authentication-cover .aunthentication-cover-content, .authentication.under-maintenance .authentication-cover .aunthentication-cover-content {
  @apply w-full h-full p-12 backdrop-filter-none;
}
.authentication.coming-soon .authentication-cover .aunthentication-cover-content:before, .authentication.coming-soon .authentication-cover .aunthentication-cover-content:after, .authentication.under-maintenance .authentication-cover .aunthentication-cover-content:before, .authentication.under-maintenance .authentication-cover .aunthentication-cover-content:after {
  @apply hidden;
}
.authentication.coming-soon .authentication-cover .aunthentication-cover-content .coming-soon-time,
.authentication.coming-soon .authentication-cover .aunthentication-cover-content .under-maintenance-time, .authentication.under-maintenance .authentication-cover .aunthentication-cover-content .coming-soon-time,
.authentication.under-maintenance .authentication-cover .aunthentication-cover-content .under-maintenance-time {
  @apply border-primary/20 border-2 border-dashed;
}
.authentication.coming-soon .authentication-cover .aunthentication-cover-content .authentication-brand, .authentication.under-maintenance .authentication-cover .aunthentication-cover-content .authentication-brand {
  @apply w-auto h-8 border-0;
}
.authentication .coming-soom-image-container img,
.authentication .under-maintenance-image-container img {
  @apply w-full h-auto;
}
.authentication .authentication-cover {
  @apply w-full h-full flex items-center justify-center relative bg-primary/90;
}
.authentication .authentication-cover:before {
  @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/media-84.jpg)] bg-cover bg-center bg-no-repeat z-[-1] inset-0;
}
.authentication .authentication-cover .aunthentication-cover-content {
  @apply w-9/12 h-80 relative p-8;
}
.authentication.authentication-basic .desktop-dark {
  @apply hidden;
}
.authentication .form-control-lg#one, .authentication .form-control-lg#two, .authentication .form-control-lg#three, .authentication .form-control-lg#four {
  @apply px-0;
}

@media (min-width: 992px) {
  .aunthentication-cover-content {
    @apply rounded-[0.3rem];
  }
}
[class=dark] .authentication.authentication-basic .desktop-white {
  @apply block;
}
[class=dark] .authentication.authentication-basic .desktop-logo {
  @apply hidden;
}
[class=dark] .authentication .authentication-brand.desktop-white {
  @apply block;
}
[class=dark] .authentication.authentication-cover.desktop-white {
  @apply block;
}

.authentication .desktop-logo,
.authentication .desktop-white {
  @apply h-[1.7rem] leading-[1.7rem];
}
.authentication.authentication-basic .desktop-white {
  @apply hidden;
}

/* Start:: coming soon */
.authentication.coming-soon .form-control {
  @apply pe-4;
}

.coming-soon-main {
  @apply relative bg-white dark:bg-bodybg;
}
.coming-soon-main:before {
  @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/svg/pattern-2.svg)] bg-cover bg-center bg-no-repeat opacity-10 z-[-1];
}

.footer.authentication-footer {
  @apply bg-transparent shadow-none z-[100] ps-0;
}

.anim, .anim svg {
  @apply absolute w-full h-full opacity-80;
}

.anim path {
  @apply stroke-1;
}

/* End:: coming soon */
.show-password-button {
  @apply absolute p-[0.4rem] end-1 top-0;
}

.authentication.authentication-cover-main .show-password-button {
  @apply p-[0.45rem];
}

.authentication-background {
  @apply relative bg-primary dark:bg-primary !important;
}
.authentication-background:before {
  @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/backgrounds/1.jpg)] bg-cover bg-center bg-no-repeat z-[-1] opacity-[0.15];
}

.authentication-cover-image {
  @apply rounded-[50%];
}

.authentication-cover-icon {
  @apply bg-[rgba(255,255,255,0.2)] text-white border-0 !important;
}
.authentication-cover-icon:hover, .authentication-cover-icon:active, .authentication-cover-icon:focus {
  @apply bg-[rgba(255,255,255,0.2)] text-white border-0 !important;
}

.authentication.two-step-verification .form-control {
  @apply pe-3;
}

.authentication-cover .authentication-cover-logo {
  @apply absolute end-[30px] top-[30px];
}

/* End:: authentication */
/* Start:: custom */
/* Start::body - Updated for Layered Background System */
body {
  @apply text-defaultsize font-normal font-defaultfont bg-white dark:bg-background text-defaulttextcolor dark:text-defaulttextcolor/80 leading-normal text-start overflow-x-clip min-h-screen relative flex flex-col justify-start;
}
body ::-webkit-scrollbar {
  @apply w-0 h-1 transition-all duration-[ease] delay-[0.05s] bg-light;
}
body :hover::-webkit-scrollbar-thumb {
  @apply bg-inputborder dark:bg-defaultborder/10;
}

/* End::body */
a,
button {
  outline: 0 !important;
}

/* Start::basic */
.page {
  @apply flex flex-col justify-center min-h-screen;
}

@media (min-width: 992px) {
  .app-content {
    @apply min-h-[calc(100vh_-_10rem)] transition-all duration-[0.05s] ease-[ease] ms-60 mt-[4.25rem] mb-0;
  }
}
pre {
  @apply border-gray-200 border dark:border-white/10 bg-slate-200 text-[0.75rem] p-5;
}

html {
  @apply font-defaultfont scroll-smooth text-start;
}

html[dir=rtl] {
  @apply dir-rtl !important;
}

.icon-label {
  @apply text-gray-500 dark:text-white/70 text-xs mt-4;
}

.simplebar-scrollbar:before {
  @apply bg-gray-200 !important;
}

.app-sidebar .simplebar-track.simplebar-horizontal {
  @apply hidden !important;
}

.simplebar-track.simplebar-vertical {
  @apply w-[10px] !important;
}

.simplebar-track,
.simplebar-scrollbar {
  @apply -right-px rtl:-left-px !important;
}

[data-toggled=icon-overlay-close] .content {
  @apply lg:ms-24;
}

.scrollToTop {
  @apply fixed bottom-5 end-5 hidden border border-primary bg-primary/10 text-primary items-center justify-center text-center z-10 h-10 w-10 bg-no-repeat bg-center transition duration-100 rounded-sm shadow-lg;
}

#drag-right .box,
#drag-center .box,
#drag-left .box {
  @apply touch-none;
}

.main-content {
  @apply px-3 py-0;
}

@media (max-width: 991.98px) {
  .main-content {
    @apply pt-[3.75rem];
  }
}
/* End::basic */
/* Start::App Content */
.bd-example > .dropdown-menu {
  @apply static block;
}

/* Start::width */
.min-w-fit-content {
  @apply min-w-fit;
}

/* End::width */
/* Start::Scrollspy */
.scrollspy-example {
  @apply h-[12.5rem] overflow-auto mt-2;
}

.scrollspy-example-2 {
  @apply h-[21.875] overflow-auto;
}

.scrollspy-example-3 {
  @apply h-[13.75rem] overflow-auto;
}

.simple-list-example-scrollspy .active {
  @apply bg-primary text-white;
}

.scrollspy-example-4 {
  @apply h-[12.5rem] overflow-auto mt-2;
}

/* End::Scrollspy */
/* Start::Carousel */
.carousel-inner {
  @apply rounded-[0.35rem];
}

.carousel-caption {
  @apply text-white;
}

/* End::Carousel */
/* Start::navbar */
.fixed-top {
  @apply static mt-[-1rem] me-[-1rem] ms-[-1rem] mb-0;
}

.fixed-bottom {
  @apply static mb-[-1rem] ms-[-1rem] me-0 mt-4;
}

.sticky-top {
  @apply static mt-[-1rem] me-[-1rem] ms-[-1rem] mb-0;
}

/*End::navbar*/
/* Start::Helpers */
.bd-example-ratios .ratio {
  @apply inline-block w-40 text-textmuted dark:text-textmuted/50 bg-primary/10 border-defaultborder rounded-md;
}

@media (min-width: 768px) {
  .bd-example-ratios-breakpoint .ratio-4x3 {
    --bs-aspect-ratio: 50%;
  }
}
.bd-example-ratios-breakpoint .ratio-4x3 {
  @apply w-60;
}

/* End::Helpers */
.callout {
  @apply bg-light border-s-defaultborder my-5 p-5 border-s-4 border-solid;
}

.callout-info {
  @apply bg-info/[0.075] border-info/[0.5];
}

.callout-warning {
  @apply bg-warning/[0.075] border-warning/[0.5];
}

.callout-danger {
  @apply bg-danger/[0.075] border-danger/[0.5];
}

.flex-container div {
  @apply bg-transparent border-0;
}
.flex-container div > div {
  @apply bg-light border border-defaultborder border-solid;
}

.bd-example-position-utils {
  @apply relative p-8;
}
.bd-example-position-utils .position-relative {
  @apply h-[12.5rem] bg-defaultbackground;
}
.bd-example-position-utils .position-absolute {
  @apply w-8 h-8 bg-primary/10 rounded-md;
}

/* End::Utilities Page */
/* Start:: Images & Figures */
.bd-placeholder-img-lg {
  @apply text-[3.5rem];
}

.figure-caption {
  @apply text-textmuted dark:text-textmuted/50;
}

/* End:: Images & Figures */
/* End:App-Content */
/*  Start::Footer*/
@media (min-width: 992px) {
  [data-nav-layout=vertical] .footer {
    @apply ps-60;
  }
}
.footer {
  @apply border-t-defaultborder border-t border-solid;
}

/*  End::Footer*/
/* Start::OffCanvas */
.offcanvas {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor border-defaultborder;
}

.offcanvas-body {
  @apply grow overflow-y-auto p-4;
}

/* End::OffCanvas */
/* Start::Switcher */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
#switcher-main-tab {
  @apply border-b-0;
}

#switcher-canvas {
  @apply w-[27.5rem];
}
#switcher-canvas .offcanvas-body {
  @apply pt-0 pb-28 px-0;
}
#switcher-canvas .canvas-footer {
  @apply absolute w-full bg-white dark:bg-bodybg border-t-defaultborder shadow-[0_0.25rem_0.5rem_rgba(0,0,0,0.5)] px-[1.563rem] py-3 border-t border-dashed bottom-0;
}
#switcher-canvas #switcher-main-tab button.nav-link {
  @apply text-defaulttextcolor font-normal rounded-none;
}
#switcher-canvas #switcher-main-tab button.nav-link.active {
  @apply text-danger bg-danger/20 border-transparent;
}
#switcher-canvas #switcher-main-tab button.nav-link:hover {
  @apply border-transparent;
}

.switcher-style {
  @apply px-[1.563rem] py-3.5;
}
.switcher-style h6 {
  @apply mb-2.5;
}

.switcher-icon i {
  animation-name: spin;
  animation-duration: 3000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.switch-select .form-check-label {
  @apply text-[0.813rem] font-normal;
}
.switch-select.form-check {
  @apply min-h-[auto] mb-0;
}

.menu-image .bgimage-input {
  @apply w-14 h-[5.625rem] rounded-md border-0;
}
.menu-image .bgimage-input.form-check-input:focus {
  @apply shadow-[0_0_0_0.25rem_black1] border-transparent;
}
.menu-image .bgimage-input.bg-img1 {
  @apply bg-[url(../public/assets/images/menu-bg-images/bg-img1.jpg)] bg-center bg-cover bg-no-repeat;
}
.menu-image .bgimage-input.bg-img1.form-check-input:checked[type=radio] {
  @apply bg-none;
}
.menu-image .bgimage-input.bg-img2 {
  @apply bg-[url(../public/assets/images/menu-bg-images/bg-img2.jpg)] bg-center bg-cover bg-no-repeat;
}
.menu-image .bgimage-input.bg-img2.form-check-input:checked[type=radio] {
  @apply bg-none;
}
.menu-image .bgimage-input.bg-img3 {
  @apply bg-[url(../public/assets/images/menu-bg-images/bg-img3.jpg)] bg-center bg-cover bg-no-repeat;
}
.menu-image .bgimage-input.bg-img3.form-check-input:checked[type=radio] {
  @apply bg-none;
}
.menu-image .bgimage-input.bg-img4 {
  @apply bg-[url(../public/assets/images/menu-bg-images/bg-img4.jpg)] bg-center bg-cover bg-no-repeat;
}
.menu-image .bgimage-input.bg-img4.form-check-input:checked[type=radio] {
  @apply bg-none;
}
.menu-image .bgimage-input.bg-img5 {
  @apply bg-[url(../public/assets/images/menu-bg-images/bg-img5.jpg)] bg-center bg-cover bg-no-repeat;
}
.menu-image .bgimage-input.bg-img5.form-check-input:checked[type=radio] {
  @apply bg-none;
}

.custom-theme-colors.switcher-style {
  @apply px-[1.563rem] py-[0.938rem];
}
.custom-theme-colors .switch-select .color-input {
  @apply w-8 h-8 rounded-[50%];
}
.custom-theme-colors .switch-select .color-input.form-check-input:checked {
  @apply border border-inputborder relative shadow-[0px_6px_16px_2px_rgba(0,0,0,0.05)] border-solid;
}
.custom-theme-colors .switch-select .color-input.form-check-input:checked:before {
  @apply absolute content-[""] text-success w-full h-full flex items-center justify-center text-[1.35rem] font-normal font-[tabler];
}
.custom-theme-colors .switch-select .color-input.color-white {
  @apply bg-white;
}
.custom-theme-colors .switch-select .color-input.color-dark {
  @apply bg-black;
}
.custom-theme-colors .switch-select .color-input.color-primary {
  @apply bg-primary;
}
.custom-theme-colors .switch-select .color-input.color-primary-1 {
  @apply bg-[#7647e5];
}
.custom-theme-colors .switch-select .color-input.color-primary-2 {
  @apply bg-[#3f4bec];
}
.custom-theme-colors .switch-select .color-input.color-primary-3 {
  @apply bg-[#377dce];
}
.custom-theme-colors .switch-select .color-input.color-primary-4 {
  @apply bg-[#019fa2];
}
.custom-theme-colors .switch-select .color-input.color-primary-5 {
  @apply bg-[#8b9504];
}
.custom-theme-colors .switch-select .color-input.color-transparent {
  @apply bg-[url(../public/assets/images/menu-bg-images/transparent.png)];
}
.custom-theme-colors .switch-select .color-input.color-bg-1 {
  @apply bg-[#0c175b];
}
.custom-theme-colors .switch-select .color-input.color-bg-2 {
  @apply bg-[#320b6e];
}
.custom-theme-colors .switch-select .color-input.color-bg-3 {
  @apply bg-[#085171];
}
.custom-theme-colors .switch-select .color-input.color-bg-4 {
  @apply bg-[#03513c];
}
.custom-theme-colors .switch-select .color-input.color-bg-5 {
  @apply bg-[#494e01];
}
.custom-theme-colors .switch-select .form-check-input:checked[type=radio] {
  @apply bg-none;
}
.custom-theme-colors .switch-select .form-check-input:focus {
  @apply shadow-none;
}
.custom-theme-colors .switch-select .form-check-input:active {
  @apply brightness-[100%];
}

.switcher-style-head {
  @apply text-[0.8rem] font-medium bg-light text-defaulttextcolor mb-0 px-2.5 py-[0.313rem];
}
.switcher-style-head .switcher-style-description {
  @apply float-right text-[0.625rem] bg-secondary/20 text-secondary rounded-md px-[0.313rem] py-0.5;
}

#switcher-home,
#switcher-profile {
  @apply p-0;
}

.custom-container-primary button,
.custom-container-background button {
  @apply hidden;
}

.pickr-container-primary .pickr .pcr-button,
.pickr-container-background .pickr .pcr-button {
  @apply w-8 h-8 overflow-hidden border border-inputborder rounded-[50%] border-solid;
}
.pickr-container-primary .pickr .pcr-button:focus,
.pickr-container-background .pickr .pcr-button:focus {
  @apply shadow-none;
}
.pickr-container-primary .pickr .pcr-button::after,
.pickr-container-background .pickr .pcr-button::after {
  content: "\efc5";
  @apply text-white/70 leading-normal text-xl;
  font-family: remixicon !important;
}

@media (max-width: 991.98px) {
  .navigation-menu-styles {
    @apply hidden;
  }
}
/* End::Switcher */
.card {
  @apply bg-white dark:bg-bodybg border border-defaultborder border-solid;
}

.img-thumbnail {
  @apply bg-white dark:bg-bodybg border border-defaultborder border-solid;
}

/* Start:Responsive Dropdowns */
@media (max-width: 575.98px) {
  .cart-dropdown,
  .notifications-dropdown,
  .header-fullscreen {
    @apply hidden !important;
  }
}
/* End:Responsive Dropdowns */
/* Start::Close Button */
.btn-close:focus {
  @apply shadow-none !important;
}

/* End::Close Button */
/* Start::Icons Page */
.icons-list {
  @apply flex flex-wrap ms-0 -me-px mt-0 -mb-px p-0 list-none;
}
.icons-list .icons-list-item {
  @apply text-center h-12 w-12 flex items-center justify-center border border-defaultborder dark:border-defaultborder/10 shadow-[0px_0.125px_0.25px_rgba(0,0,0,0.05)] m-1 rounded-full border-solid !important;
}
.icons-list .icons-list-item i {
  @apply text-[1.05rem] text-defaulttextcolor;
}

.fe {
  @apply text-inherit;
}

/* End::Icons Page */
.bd-placeholder-img {
  @apply m-0.5;
}

/* End::Shadows */
/* Start::placeholders */
.placeholder-xl {
  @apply min-h-[1.5em];
}

.placeholder {
  @apply bg-gray7;
}

/* End:::placeholders */
/* Start::scrollspy */
.scrollspy-example-2 {
  @apply h-[21.875rem] border border-defaultborder rounded-md p-3 border-solid;
}

/* End::scrollspy */
/* Start::object-fit */
.object-fit-container {
  @apply flex items-center justify-center;
}
.object-fit-container img,
.object-fit-container video {
  @apply w-[15.625rem] h-[15.625rem];
}

/* End::object-fit */
/* Start::invoice */
.invoice-amount-input {
  @apply w-[9.375rem];
}

.choices-control .choices__inner {
  @apply bg-light border-0;
}

.svg-icon-background {
  @apply w-10 h-10 rounded-md flex items-center justify-center p-2.5;
}
.svg-icon-background svg {
  @apply w-5 h-5;
}

.invoice-quantity-container {
  @apply w-[8.75rem];
}

/* End::invoice */
/* Start::pricing */
.pricing-card:hover, .pricing-card.hover {
  box-shadow: 0px 7px 28px 0px rgba(100, 100, 110, 0.1);
}

.pricing-table-item-icon {
  @apply text-[15px] h-[25px] rounded-md w-[100px] flex items-center justify-center absolute text-center top-[-9px] bg-primarytint1color text-white start-auto end-4;
}

.pricing-body {
  @apply mb-0;
}
.pricing-body li {
  @apply mb-4;
}
.pricing-body li:last-child {
  @apply mb-0;
}

.pricing-card .ribbon-2.ribbon-right {
  @apply top-5;
}
.pricing-card .ribbon-2.ribbon-right:before {
  @apply border-e-[9px] border-e-transparent border-solid;
}

.switcher-box {
  @apply flex content-center justify-center mb-4;
}

.switcher-box .pricing-time-span {
  @apply text-[15px] leading-[30px] font-medium px-3 py-0;
}

.switcher-pricing .pricing-toggle:checked:after {
  transform: translatex(28px);
}

.switcher-pricing [type=checkbox]:checked {
  @apply bg-none;
}

[dir=rtl] .switcher-pricing .pricing-toggle:checked:after {
  transform: translatex(-28px);
}

.switcher-pricing .pricing-toggle {
  @apply w-[59px] h-[30px] inline-block relative border-defaultborder transition-all duration-[0.2s] ease-[ease] bg-primary m-0 rounded-[43px] border-2 border-solid;
}
.switcher-pricing .pricing-toggle:after {
  @apply content-[""] absolute w-5 h-5 bg-white shadow-[0_1px_2px_rgba(0,0,0,0.2)] transition-all duration-[0.25s] ease-linear rounded-[50%] start-[3px] top-[3px];
}

.switcher-pricing input[type=checkbox] {
  @apply appearance-none cursor-pointer;
}

#convertable-pricing .tab-content {
  @apply hidden;
}
#convertable-pricing .tab-content.show {
  @apply block;
}

/* End::pricing */
/* Start::Team */
.team-member {
  @apply z-0 overflow-hidden;
}
.team-member .avatar.avatar-xl {
  @apply rounded-[50%];
}

/* End::Team */
/* Start:: To Do Task */
.task-navigation ul.task-main-nav li {
  @apply rounded-md font-normal px-[0.8rem] py-[0.55rem];
}
.task-navigation ul.task-main-nav li:hover a {
  @apply text-primary;
}
.task-navigation ul.task-main-nav li.active {
  @apply bg-primary/[0.05] rounded-[0.3rem];
}
.task-navigation ul.task-main-nav li.active div {
  @apply text-primary;
}

th.todolist-progress {
  @apply w-[150px];
}

.todolist-handle-drag {
  @apply w-[50px];
}

.todo-box {
  @apply touch-none;
}

button.btn.todo-handle {
  @apply cursor-move;
}

.todo-list-card {
  @apply z-0 relative;
}
.todo-list-card:before {
  @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/media-67.png)] opacity-5 bg-no-repeat z-[-1] start-0 top-0;
}

/* End:: To Do Task */
/* Start:: Terms & Conditions */
.terms-list > li {
  @apply mb-[1.35rem];
}
.terms-list > li:last-child {
  @apply mb-0;
}
.terms-list > li li {
  @apply mb-[0.5rem];
}
.terms-list > li li:last-child {
  @apply mb-0;
}

/* End:: Terms & Conditions */
/* Start:: Faq's */
.faq-accordion.accordion.accordion-primary .accordion-button.collapsed {
  @apply text-defaulttextcolor;
}
.faq-accordion.accordion.accordion-primary .accordion-button.collapsed:after {
  @apply text-textmuted dark:text-textmuted/50 text-defaultsize;
}
.faq-accordion .accordion-body {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor text-defaultsize;
}
.faq-accordion .accordion-button {
  @apply font-semibold;
}

.faq-nav.nav-tabs-header .nav-item .nav-link {
  @apply bg-light;
}

/* End:: Faq's */
/* Start:: Timeline */
.timeline,
.timeline .timeline-container {
  @apply relative w-full;
}

.timeline .timeline-end {
  @apply relative -ms-4 mt-[3px];
}
.timeline .timeline-end .avatar {
  @apply inline-block;
}
.timeline .timeline-content {
  @apply relative ms-16 me-[6%];
}
.timeline .timeline-content::after {
  @apply block content-[""] w-3 absolute h-3 bg-white dark:bg-bodybg border-primary z-[1] start-[-2.83rem] shadow-[0px_0px_1px_3px_var(--primary03)] rounded-[50%] border-[3px] border-solid top-[0.1875rem];
}
.timeline .timeline-continue {
  @apply relative w-full pt-8;
}
.timeline .timeline-continue::after {
  @apply content-[""] absolute h-full border-s-primary/40 -ms-px border-s border-dashed start-6 top-0.5;
}
.timeline .timeline-date {
  @apply text-sm relative;
}
.timeline .timeline-box {
  @apply relative border border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg shadow-[0px_2px_0px_rgba(118,138,254,0.03)] mb-[15px] p-4 rounded-md border-solid;
}
.timeline .timeline-box .timeline-text {
  @apply relative;
}
.timeline .timeline-right .timeline-time {
  @apply text-end;
}

@media (max-width: 1199.98px) {
  .timeline-2 .notification {
    @apply before:start-[2.5rem];
  }
  .timeline-2 .notification .notification-time {
    @apply my-[0.35rem];
  }
  .timeline-2 .notification .notification-body {
    @apply me-0;
  }
  .timeline-2 .notification > li {
    @apply px-0 py-[9px];
  }
  .timeline-2 .notification {
    @apply ps-[4.5rem];
  }
  .timeline-2 .notification .notification-time.content-end {
    @apply me-0;
  }
  .timeline-2 .notification .notification-icon {
    @apply start-[-10.5%] end-auto;
  }
  .timeline-2 .notification:before {
    @apply start-[3rem];
  }
}
@media (max-width: 566px) {
  .notification {
    @apply ps-[1.5rem] !important;
  }
}
@media (max-width: 1500px) {
  .timeline-3 .timeline-steps .content-top {
    @apply mt-0;
  }
}
@media (min-width: 991.98px) and (max-width: 1150px) {
  .timeline-2 .notification:before {
    @apply block;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .timeline-2 .notification .notification-icon {
    @apply start-[-9%];
  }
}
@media (max-width: 767.98px) {
  .timeline .timeline-continue::after {
    @apply start-[1.5rem];
  }
  .timeline .row.timeline-right .timeline-date::after {
    @apply start-[-2.4rem];
  }
  .timeline .row.timeline-right .timeline-date,
  .timeline .row.timeline-right .timeline-time {
    @apply text-start;
  }
  .timeline .timeline-end {
    @apply text-start;
  }
  .timeline .timeline-date {
    @apply mt-0;
  }
  .timeline-2 .notification .notification-icon {
    @apply start-[-3.7rem];
  }
  .timeline-2 .notification:before {
    @apply start-[2.5rem];
  }
}
[dir=rtl] .timeline .row.timeline-right .timeline-box::after {
  @apply border-[transparent_transparent_transparent_white];
}
[dir=rtl] .timeline .row.timeline-right .timeline-box::before {
  @apply border-[transparent_transparent_transparent_defaultborder];
}
[dir=rtl] .timeline .timeline-box::before {
  @apply border-[transparent_defaultborder_transparent_transparent];
}
[dir=rtl] .timeline .timeline-box::after {
  @apply border-[transparent_white_transparent_transparent];
}

.notification {
  @apply list-none relative p-0;
}

.notification:before {
  @apply content-[""] absolute border-s-primary/40 -ms-px border-s border-dashed start-[49.05%] top-[55px] bottom-16;
}

.notification > li {
  @apply relative min-h-[50px] px-0 py-3;
}

.notification .notification-time {
  @apply my-[2.1rem];
}
.notification .notification-time.content-end {
  @apply text-end me-[2.1rem] justify-end;
}

.notification .notification-time .date,
.notification .notification-time .time {
  @apply block font-medium;
}

.notification .notification-time .date {
  @apply leading-4 text-textmuted dark:text-textmuted/50;
}

.notification .notification-time .time {
  @apply leading-6 text-lg text-textmuted dark:text-textmuted/50;
}

.notification .notification-icon {
  @apply absolute w-[10%] text-center end-[46%] top-[51px];
}

.notification .notification-icon a {
  @apply no-underline w-[11px] h-[11px] inline-block leading-[10px] text-white bg-white dark:bg-bodybg text-sm transition-[border-color] duration-[0.2s] ease-linear rounded-[50%] border-[3px] border-solid border-primary;
}

.notification .notification-icon a.primarytint1color {
  @apply border-primarytint1color;
}

.notification .notification-icon a.primarytint2color {
  @apply border-primarytint2color;
}

.notification .notification-icon a.primarytint3color {
  @apply border-primarytint3color;
}

.notification .notification-icon a.warning {
  @apply border-warning;
}

.notification .notification-icon a.secondary {
  @apply border-secondary;
}

.notification .notification-icon a.success {
  @apply border-success;
}

.notification .notification-icon a.info {
  @apply border-info;
}

.notification .notification-body {
  @apply bg-white dark:bg-bodybg border border-defaultborder shadow-[0px_2px_0px_rgba(118,138,254,0.03)] relative ms-0 me-[4%] p-4 rounded-md border-solid;
}

.notification .notification-body.notification-body-end {
  @apply me-0;
}

.notification .notification-body > div + div {
  @apply mt-[15px];
}

.timeline .profile-activity-media img {
  @apply w-16 h-12 m-1 rounded-lg;
}

@media (max-width: 576px) {
  .notification .notification-body:before {
    @apply hidden;
  }
  .notification .notification-icon a {
    @apply hidden;
  }
  .notification:before {
    @apply hidden;
  }
  .notification-body .media {
    @apply flex-col;
  }
  .notification-body .media .main-img-user {
    @apply mb-2.5 !important;
  }
  .notification .notification-body {
    @apply relative mx-0;
  }
  .notification-badge {
    @apply absolute start-2.5 top-2;
  }
  .notification .notification-time .date,
  .notification .notification-time .time {
    @apply inline;
  }
  .notification .notification-time .time {
    @apply leading-4 text-[11px] text-textmuted dark:text-textmuted/50 ms-[5px] me-2.5;
  }
}
.timeline-steps {
  @apply flex justify-center flex-wrap gap-[5px];
}
.timeline-steps .content-top {
  @apply mt-[-10.25rem];
}

.timeline-steps .timeline-step {
  @apply items-center flex flex-col relative m-4;
}

@media (min-width: 576px) {
  .timeline-steps .timeline-step:not(:last-child):after {
    @apply content-[""] block border-t-secondary/20 w-[6.46rem] absolute border-t-2 border-dotted start-32 top-[0.35rem];
  }
  .timeline-steps .timeline-step:not(:first-child):before {
    @apply content-[""] block border-t-secondary/20 w-[5.6875rem] absolute border-t-2 border-dotted end-32 top-[0.35rem];
  }
}
.timeline-steps .timeline-content {
  @apply w-52 text-center;
}

.timeline-steps .timeline-content .inner-circle {
  @apply h-2 w-2 inline-flex items-center justify-center bg-primary rounded-xl;
}

.timeline-steps .timeline-content .inner-circle:before {
  @apply content-[""] bg-primary/[0.15] inline-block h-4 w-8 min-w-[3rem] rounded-[6.25rem];
}

/* End:: Timeline */
/* Start:: Blog */
.popular-blog-content {
  @apply max-w-[14rem];
}

/* End:: Blog */
/* Start:: Blog Details */
.blog-popular-tags .badge {
  @apply text-[0.65rem] m-[0.313rem];
}

#blog-details-comment-list {
  @apply max-h-[265px];
}

/* End:: Blog Details */
/* Start:: Create Blog */
.blog-images-container .filepond--root {
  @apply w-full;
}
.blog-images-container .filepond--panel-root {
  @apply border-inputborder rounded-md;
}
.blog-images-container .filepond--root .filepond--drop-label label {
  @apply text-textmuted dark:text-textmuted/50;
}

#blog-content {
  @apply h-auto;
}

@media screen and (max-width: 400px) {
  .choices__inner .choices__list--multiple .choices__item {
    @apply mb-1 !important;
  }
}
@media screen and (max-width: 991px) {
  .ql-toolbar.ql-snow .ql-formats {
    @apply mb-1 !important;
  }
}
/* End:: Create Blog */
/* Start:: Profile */
.profile-card .avatar.avatar-xxl img {
  @apply border-primarytint1color/30 border-4 border-solid;
}

.profile-card {
  @apply overflow-hidden;
}
.profile-card .profile-banner-img {
  @apply relative;
}
.profile-card .profile-banner-img::before {
  @apply content-[""] absolute w-full h-full bg-primary/30 start-0 top-0;
}

@media (max-width: 576px) {
  .profile-card .profile-content {
    @apply mt-[-3rem];
  }
}
.profile-content {
  @apply mt-[-5rem];
}

.profile-timeline {
  @apply mb-0;
}
.profile-timeline li {
  @apply relative mb-[1.85rem] ps-12;
}
.profile-timeline li .profile-timeline-avatar {
  @apply absolute start-[0.125rem] top-0;
}
.profile-timeline li:last-child {
  @apply mb-0;
}
.profile-timeline li:last-child::before {
  @apply hidden;
}
.profile-timeline li::before {
  content: "";
  @apply bg-transparent border border-dark/10 h-full absolute border-dashed start-[0.813rem] top-[1.813rem];
}
.profile-timeline .profile-activity-media {
  @apply bg-light p-[0.3rem] rounded-lg;
}
.profile-timeline .profile-activity-media img {
  @apply w-16 h-12 rounded-md m-1;
}

.profile-settings-tab .nav-item .nav-link {
  @apply border-0;
}
.profile-settings-tab .nav-item .nav-link.active {
  @apply rounded-br-none rounded-bl-none bg-primary/10;
}
.profile-settings-tab .nav-item .nav-link.active:before {
  @apply bottom-px;
}
.profile-settings-tab .nav-item .nav-link:hover, .profile-settings-tab .nav-item .nav-link:focus {
  @apply border-0;
}

.tab-style-8.nav-tabs.scaleX.nav-tabs > .nav-item > .nav-link {
  @apply relative p-2 border-b-[none];
}

.tab-style-8.nav-tabs > .nav-item > .nav-link.active {
  @apply text-primary bg-primary/10 border-[2px] border-t-transparent border-b-primary border-x-transparent !important;
}

/* End:: Profile */
/* Start:: Full Calendar */
[dir=rtl] .fullcalendar-events-activity li {
  @apply pe-4 ps-8 py-1;
}

.fullcalendar-events-activity li {
  @apply text-[0.8125rem] relative mb-3 ps-8 pe-0 py-1;
}
.fullcalendar-events-activity li::before {
  @apply absolute content-[""] w-3 h-3 bg-white dark:bg-bodybg rounded-[3.125rem] border-2 border-solid border-primary start-[0.1rem] top-[0.563rem];
}
.fullcalendar-events-activity li::after {
  @apply absolute content-[""] h-full bg-transparent border-e-primary/10 border-e-2 border-dashed start-[0.4rem] top-5;
}
.fullcalendar-events-activity li:last-child {
  @apply mb-0;
}
.fullcalendar-events-activity li:last-child::after {
  @apply border-e-defaultborder border-e-0 border-dashed;
}

#full-calendar-activity {
  @apply max-h-96;
}

/* End:: Full Calendar */
/* Start:: Draggable Cards */
#draggable-left .card,
#draggable-right .card {
  @apply cursor-move;
}

/* End:: Draggable Cards */
/* Start:: Back to Top */
.scrollToTop {
  @apply bg-primary/10 text-primary border backdrop-blur-[30px] fixed flex items-center justify-center text-center z-[10000] h-10 w-10 bg-no-repeat bg-center transition-[background-color] duration-[0.1s] ease-linear rounded-sm shadow-none border-solid border-primary end-5 bottom-5;
}

/* End:: Back to Top */
[dir=rtl] .rtl-rotate {
  @apply rotate-180;
}

/* Start:: Projects List */
.project-list-title {
  @apply max-w-[13.375rem];
}

#project-descriptioin-editor {
  @apply h-[200px] overflow-auto;
}

.project-list-main .choices__inner {
  @apply w-[150px];
}

.project-list-description {
  @apply max-w-[350px] min-w-[350px];
  white-space: wrap !important;
}

/* End:: Projects List */
/* Start:: Job Details */
.swiper-related-jobs .swiper-button-next,
.swiper-related-jobs .swiper-button-prev {
  @apply bg-light text-defaulttextcolor;
}

.box.job-info-banner {
  @apply bg-primary dark:bg-primary z-0 h-40 !important;
}
.box.job-info-banner::before {
  @apply content-[""] absolute w-full h-full bg-cover bg-top bg-repeat bg-[url(../public/assets/images/media/media-69.jpg)] z-[-1] opacity-30;
}

.box.job-info-data {
  @apply mt-[-5rem] w-auto mx-4;
}

.swiper-vertical.swiper-related-jobs {
  @apply h-[38rem];
}

@media (max-width: 349px) {
  .swiper-vertical.swiper-related-jobs {
    @apply h-[32rem];
  }
}
/* End:: Job Details */
/* Start:: Companies Search */
@media screen and (min-width: 623px) {
  .input-group.companies-search-input .choices {
    @apply mb-0 border-solid rounded-tl-none rounded-tr-none;
  }
  .input-group.companies-search-input .choices__inner {
    @apply min-h-full bg-white dark:bg-bodybg rounded-none border-defaultborder dark:border-defaultborder/10 rounded-tl-none rounded-tr-none !important;
  }
  .input-group.companies-search-input .choices__list--dropdown .choices__item--selectable {
    @apply pe-4;
  }
  [dir=rtl] .input-group.companies-search-input .choices__inner {
    @apply rounded-none;
  }
}
@media screen and (max-width: 622px) {
  .input-group.companies-search-input {
    @apply block;
  }
  .input-group.companies-search-input .form-control {
    @apply w-full rounded-md mb-2;
  }
  .input-group.companies-search-input .choices {
    @apply rounded-md mb-2;
  }
  .input-group.companies-search-input .choices .choices__inner {
    @apply rounded-md !important;
  }
  .input-group.companies-search-input .ti-btn {
    @apply w-full rounded-md z-0 !important;
  }
  .input-group.companies-search-input input {
    @apply border border-defaultborder dark:border-defaultborder/10 rounded-md !important;
  }
}
/* End:: Companies Search */
/* Start:: Jobs Candidate Search */
.companies-search-input .choices__list.choices__list--single {
  @apply leading-[2.25];
}

.companies-search-input1 .choices {
  @apply flex-auto;
}

/* End:: Jobs Candidate Search */
/* Start:: Jobs Candidate Details */
.list-bullets li {
  @apply relative border border-defaultborder list-[circle] list-inside -mb-px px-5 py-3 border-solid;
}

.swiper-vertical.swiper-related-profiles {
  @apply h-[39.75rem];
}

@media (min-width: 1400px) and (max-width: 1433px) {
  .swiper-vertical.swiper-related-profiles {
    @apply h-[35.75rem];
  }
}
@media (max-width: 398px) {
  .swiper-vertical.swiper-related-profiles {
    @apply h-64;
  }
}
.job-candidate-details {
  @apply overflow-hidden z-0;
}
.job-candidate-details .candidate-bg-shape {
  @apply absolute w-full h-[115px] z-[-1] opacity-[0.94] bg-[url(../public/assets/images/media/media-70.jpg)] bg-cover bg-no-repeat bg-center start-0;
}

/* End:: Jobs Candidate Details */
/* Start:: CRM Companies */
#offcanvasExample {
  @apply border-transparent !important;
}

/* End:: CRM Companies */
#leads-discovered .box {
  @apply border-t-primary/[0.18] border-t-[3px] border-solid;
}
#leads-discovered .box .company-name {
  @apply text-primary;
}
#leads-discovered .box .deal-description {
  @apply bg-primary/[0.05];
}
#leads-discovered .box .avatar {
  @apply bg-primary;
}

#leads-qualified .box {
  @apply border-t-primarytint1color/[0.18] border-t-[3px] border-solid;
}
#leads-qualified .box .company-name {
  @apply text-primarytint1color;
}
#leads-qualified .box .deal-description {
  @apply bg-primarytint1color/[0.05];
}
#leads-qualified .box .avatar {
  @apply bg-primary/10;
}

#contact-initiated .box {
  @apply border-t-primarytint2color/[0.18] border-t-[3px] border-solid;
}
#contact-initiated .box .company-name {
  @apply text-primarytint2color;
}
#contact-initiated .box .deal-description {
  @apply bg-primarytint2color/[0.05];
}
#contact-initiated .box .avatar {
  @apply bg-primary/20;
}

#needs-identified .box {
  @apply border-t-primarytint3color/[0.18] border-t-[3px] border-solid;
}
#needs-identified .box .company-name {
  @apply text-primarytint3color;
}
#needs-identified .box .deal-description {
  @apply bg-primarytint3color/[0.05];
}
#needs-identified .box .avatar {
  @apply bg-primary/30;
}

#negotiation .box {
  @apply border-t-secondary/[0.18] border-t-[3px] border-solid;
}
#negotiation .box .company-name {
  @apply text-secondary;
}
#negotiation .box .deal-description {
  @apply bg-secondary/[0.05];
}
#negotiation .box .avatar {
  @apply bg-secondary;
}

#deal-finalized .box {
  @apply border-t-success/[0.18] border-t-[3px] border-solid;
}
#deal-finalized .box .company-name {
  @apply text-success;
}
#deal-finalized .box .deal-description {
  @apply bg-success/[0.05];
}
#deal-finalized .box .avatar {
  @apply bg-success;
}

#leads-discovered .box,
#leads-qualified .box,
#contact-initiated .box,
#needs-identified .box,
#negotiation .box,
#deal-finalized .box {
  @apply touch-none mb-2;
}
#leads-discovered .box:last-child,
#leads-qualified .box:last-child,
#contact-initiated .box:last-child,
#needs-identified .box:last-child,
#negotiation .box:last-child,
#deal-finalized .box:last-child {
  @apply mb-6;
}
#leads-discovered .box .box-body,
#leads-qualified .box .box-body,
#contact-initiated .box .box-body,
#needs-identified .box .box-body,
#negotiation .box .box-body,
#deal-finalized .box .box-body {
  @apply p-4;
}
#leads-discovered .box .box-body .deal-description,
#leads-qualified .box .box-body .deal-description,
#contact-initiated .box .box-body .deal-description,
#needs-identified .box .box-body .deal-description,
#negotiation .box .box-body .deal-description,
#deal-finalized .box .box-body .deal-description {
  @apply rounded-[4px] px-[9px] py-[3px] !important;
}
#leads-discovered .box .box-body .deal-description .company-name,
#leads-qualified .box .box-body .deal-description .company-name,
#contact-initiated .box .box-body .deal-description .company-name,
#needs-identified .box .box-body .deal-description .company-name,
#negotiation .box .box-body .deal-description .company-name,
#deal-finalized .box .box-body .deal-description .company-name {
  @apply text-xs;
}

.create-nft-item .filepond--root[data-style-panel-layout~=circle] {
  @apply rounded-md w-full;
}
.create-nft-item .filepond--drop-label.filepond--drop-label label {
  @apply p-[2em];
}
.create-nft-item .filepond--drop-label {
  @apply text-textmuted dark:text-textmuted/50;
}
.create-nft-item .filepond--panel-root {
  @apply border-inputborder border-2 border-dashed;
}
.create-nft-item .filepond--root[data-style-panel-layout~=circle] .filepond--image-preview-wrapper {
  @apply rounded-[0.3rem];
}

@media (min-width: 576px) {
  .create-nft-item .filepond--root[data-style-panel-layout~=circle] {
    @apply h-[15.08rem] w-[22.25rem] !important;
  }
  .create-nft-item .filepond--drop-label label {
    @apply text-xs;
  }
  .create-nft-item .filepond--root[data-style-panel-layout~=circle] .filepond--image-preview-wrapper,
  .create-nft-item .filepond--file {
    @apply h-[15.08rem] w-[22.25rem];
  }
}
.nft-wallet {
  @apply relative text-center shadow-none border border-defaultborder rounded-md border-solid;
}
.nft-wallet:hover {
  @apply bg-primary/10 dark:bg-primary/10 !important;
}
.nft-wallet.active {
  @apply border-primary/20 bg-primary/10 !important;
}

.nft-list li {
  @apply overflow-hidden rounded-md;
}
.nft-list li.active {
  @apply bg-primary text-white rounded-md !important;
}

.nft-wallet-card {
  @apply relative z-0;
}
.nft-wallet-card::before {
  @apply content-[""] absolute start-[-1.5rem] top-[-1.5rem] w-20 h-20 rounded-[2.35rem] border-[13px] border-solid border-[rgb(255,255,255,0.05)];
}
.nft-wallet-card::after {
  @apply content-[""] absolute end-[-1.5rem] bottom-[-1.5rem] w-20 h-20 rounded-[2.35rem] border-[13px] border-solid border-[rgb(255,255,255,0.05)];
}

.nft-tag.nft-tag-primary:hover, .nft-tag.nft-tag-primary.active {
  @apply text-primary;
}
.nft-tag.nft-tag-primary:hover .nft-tag-icon, .nft-tag.nft-tag-primary.active .nft-tag-icon {
  @apply bg-primary text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-primary .nft-tag-icon {
  @apply text-primary bg-primary/10;
}

.nft-tag.nft-tag-primary1:hover, .nft-tag.nft-tag-primary1.active {
  @apply text-primarytint1color;
}
.nft-tag.nft-tag-primary1:hover .nft-tag-icon, .nft-tag.nft-tag-primary1.active .nft-tag-icon {
  @apply bg-primarytint1color text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-primary1 .nft-tag-icon {
  @apply text-primarytint1color bg-primary/10;
}

.nft-tag.nft-tag-primary2:hover, .nft-tag.nft-tag-primary2.active {
  @apply text-primarytint2color;
}
.nft-tag.nft-tag-primary2:hover .nft-tag-icon, .nft-tag.nft-tag-primary2.active .nft-tag-icon {
  @apply bg-primarytint2color text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-primary2 .nft-tag-icon {
  @apply text-primarytint2color bg-primary/10;
}

.nft-tag.nft-tag-primary3:hover, .nft-tag.nft-tag-primary3.active {
  @apply text-primarytint3color;
}
.nft-tag.nft-tag-primary3:hover .nft-tag-icon, .nft-tag.nft-tag-primary3.active .nft-tag-icon {
  @apply bg-primarytint3color text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-primary3 .nft-tag-icon {
  @apply text-primarytint3color bg-primary/10;
}

.nft-tag.nft-tag-secondary:hover, .nft-tag.nft-tag-secondary.active {
  @apply text-secondary;
}
.nft-tag.nft-tag-secondary:hover .nft-tag-icon, .nft-tag.nft-tag-secondary.active .nft-tag-icon {
  @apply bg-secondary text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-secondary .nft-tag-icon {
  @apply text-secondary bg-secondary/10;
}

.nft-tag.nft-tag-warning:hover, .nft-tag.nft-tag-warning.active {
  @apply text-warning;
}
.nft-tag.nft-tag-warning:hover .nft-tag-icon, .nft-tag.nft-tag-warning.active .nft-tag-icon {
  @apply bg-warning text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-warning .nft-tag-icon {
  @apply text-warning bg-warning/10;
}

.nft-tag.nft-tag-info:hover, .nft-tag.nft-tag-info.active {
  @apply text-info;
}
.nft-tag.nft-tag-info:hover .nft-tag-icon, .nft-tag.nft-tag-info.active .nft-tag-icon {
  @apply bg-info/10 text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-info .nft-tag-icon {
  @apply text-info bg-info/10;
}

.nft-tag.nft-tag-success:hover, .nft-tag.nft-tag-success.active {
  @apply text-success;
}
.nft-tag.nft-tag-success:hover .nft-tag-icon, .nft-tag.nft-tag-success.active .nft-tag-icon {
  @apply bg-success text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-success .nft-tag-icon {
  @apply text-success bg-success/10;
}

.nft-tag.nft-tag-danger:hover, .nft-tag.nft-tag-danger.active {
  @apply text-danger;
}
.nft-tag.nft-tag-danger:hover .nft-tag-icon, .nft-tag.nft-tag-danger.active .nft-tag-icon {
  @apply bg-danger/10 text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-danger .nft-tag-icon {
  @apply text-danger bg-danger/10;
}

.nft-tag.nft-tag-dark:hover, .nft-tag.nft-tag-dark.active {
  @apply text-dark;
}
.nft-tag.nft-tag-dark:hover .nft-tag-icon, .nft-tag.nft-tag-dark.active .nft-tag-icon {
  @apply bg-danger/10 text-white shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)];
}
.nft-tag.nft-tag-dark .nft-tag-icon {
  @apply text-dark bg-dark/10;
}

.nft-tag .nft-tag-text {
  @apply font-normal inline-block ps-2 pe-5 py-0;
}

.nft-tag .nft-tag-icon {
  @apply inline-block leading-none bg-light p-[0.4rem] rounded-[50px];
}

.nft-tag {
  @apply relative inline-flex items-center border border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg text-defaulttextcolor shadow-[0px_2px_0px_rgba(118,138,254,0.03)] p-[5px] rounded-[50rem] border-solid;
}

/* End:: NFT Live Auction */
/* Start:: Crypto Wallet */
[class=dark] .qr-image {
  @apply invert-[1];
}

/* End:: Crypto Wallet */
/* Start:: Crypto Currency Exchange */
.currency-exchange-card {
  @apply relative bg-primary shadow-none min-h-[21.875rem] z-10 !important;
}
.currency-exchange-card:before {
  @apply absolute content-[""] h-full w-full bg-[url(../public/assets/images/media/media-69.jpg)] bg-no-repeat bg-cover z-0 opacity-[0.15];
}
.currency-exchange-card .currency-exchange-area {
  @apply bg-dark/10 backdrop-blur-[30px] z-[1] relative;
}
.currency-exchange-card .currency-exchange-area .form-control {
  @apply leading-[1.96];
}

.currency-exchange-area .choices__inner {
  @apply bg-white dark:bg-bodybg !important;
}

.currency-exchange-area .choices[data-type*=select-one].is-open::after {
  @apply border-b-transparent !important;
}

/* End:: Crypto Currency Exchange */
/* Start:: Crypto Buy & Sell */
#buy_sell-statistics .apexcharts-bar-series.apexcharts-plot-series .apexcharts-series .apexcharts-bar-area {
  @apply stroke-transparent;
}

/* End:: Crypto Buy & Sell */
/* Start:: Crypto Marketcap */
#bitcoin-chart .apexcharts-grid line,
#etherium-chart .apexcharts-grid line,
#dashcoin-chart .apexcharts-grid line,
#btc-chart .apexcharts-grid line,
#eth-chart .apexcharts-grid line,
#glm-chart .apexcharts-grid line,
#dash-chart .apexcharts-grid line,
#lite-chart .apexcharts-grid line,
#ripple-chart .apexcharts-grid line,
#eos-chart .apexcharts-grid line,
#bytecoin-chart .apexcharts-grid line,
#iota-chart .apexcharts-grid line,
#monero-chart .apexcharts-grid line {
  @apply stroke-transparent;
}

/* End:: Crypto Marketcap */
/* Start:: Loader */
#loader {
  @apply fixed w-full h-full bg-white dark:bg-bodybg flex justify-center items-center z-[9999] start-0 top-0;
}

/* End:: Loader */
/* Start:: Offcanvas body padding*/
@media (min-width: 992px) {
  body {
    @apply overflow-auto pe-0;
  }
}
/* end:: Offcanvas body padding*/
/* start:: kanban*/
@media (min-width: 1400px) {
  .kanban-board {
    @apply w-[370px];
  }
}
[class^=ri-],
[class*=" ri-"] {
  @apply inline-flex;
}

.badge-task {
  @apply bg-[rgba(0,0,0,0.2)];
}

/* end:: kanban*/
[class=dark] .bg-dark .h1,
[class=dark] .bg-dark .h2,
[class=dark] .bg-dark .h3,
[class=dark] .bg-dark .h4,
[class=dark] .bg-dark .h5,
[class=dark] .bg-dark .h6,
[class=dark] .bg-dark h1,
[class=dark] .bg-dark h2,
[class=dark] .bg-dark h3,
[class=dark] .bg-dark h4,
[class=dark] .bg-dark h5,
[class=dark] .bg-dark h6,
[class=dark] .card-bg-dark .h1,
[class=dark] .card-bg-dark .h2,
[class=dark] .card-bg-dark .h3,
[class=dark] .card-bg-dark .h4,
[class=dark] .card-bg-dark .h5,
[class=dark] .card-bg-dark .h6,
[class=dark] .card-bg-dark h1,
[class=dark] .card-bg-dark h2,
[class=dark] .card-bg-dark h3,
[class=dark] .card-bg-dark h4,
[class=dark] .card-bg-dark h5,
[class=dark] .card-bg-dark h6 {
  @apply text-customwhite !important;
}

[class=dark] .bg-dark .text-fixed-white {
  @apply text-white !important;
}

[class=dark] .bg-dark.box,
[class=dark] .card-bg-dark.box {
  @apply text-customwhite !important;
}

[class=dark] .bg-dark.box .card-body,
[class=dark] .bg-dark.box .card-footer,
[class=dark] .card-bg-dark.box .card-body,
[class=dark] .card-bg-dark.box .card-footer {
  @apply text-customwhite !important;
}

[class=dark] .choices[data-type*=select-one] .choices__button {
  @apply invert-[1];
}

[class=dark] .apexcharts-tooltip * {
  @apply text-defaulttextcolor;
}

[class=dark] #circle-custom .apexcharts-legend.apx-legend-position-left {
  @apply bg-white dark:bg-bodybg;
}

[class=dark] .navbar-nav .nav-link.active,
[class=dark] .navbar-nav .nav-link.show {
  @apply text-white;
}

.d-inline-table {
  @apply inline-table;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-medium !important;
}

.box {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

[class=light][data-header-styles=transparent] .page-header-breadcrumb h4,
[class=light][data-default-header-styles=transparent] .page-header-breadcrumb h4 {
  @apply text-defaulttextcolor !important;
}

[class=light][data-header-styles=transparent] .app-header .main-header-container .form-control,
[class=light][data-default-header-styles=transparent] .app-header .main-header-container .form-control {
  @apply bg-white text-defaulttextcolor shadow-[0px_2px_0px_rgba(118,138,254,0.03)];
}
[class=light][data-header-styles=transparent] .app-header .main-header-container .form-control:focus,
[class=light][data-default-header-styles=transparent] .app-header .main-header-container .form-control:focus {
  @apply shadow-[0px_2px_0px_rgba(118,138,254,0.03)];
}

[class=light] .page-header-breadcrumb h4 {
  @apply text-white !important;
}

[data-header-styles=dark] .header-link.dropdown-toggle .user-name,
[data-header-styles=color] .header-link.dropdown-toggle .user-name,
[data-header-styles=gradient] .header-link.dropdown-toggle .user-name {
  @apply text-white !important;
}

[data-default-header-styles=transparent] .page-header-breadcrumb,
[data-default-header-styles=light] .page-header-breadcrumb,
[data-default-header-styles=dark] .page-header-breadcrumb,
[data-default-header-styles=color] .page-header-breadcrumb,
[data-default-header-styles=gradient] .page-header-breadcrumb {
  @apply mt-0 -mb-8 mx-0;
}

.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label::after {
  @apply bg-transparent !important;
}

.form-floating > .form-control-plaintext ~ label::after,
.form-floating > .form-control:focus ~ label::after,
.form-floating > .form-control:not(:placeholder-shown) ~ label::after,
.form-floating > .form-select ~ label::after {
  @apply bg-transparent !important;
}

.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  @apply text-textmuted dark:text-textmuted/50 font-normal;
}

.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  @apply text-textmuted dark:text-textmuted/50 font-normal;
}

.dropdown-item-text {
  @apply text-defaulttextcolor !important;
}

.blockquote-footer {
  @apply text-defaulttextcolor !important;
}

hr {
  @apply border-defaultborder opacity-100;
}

/* Start:: rtl  */
[dir=rtl] .dropdown-menu {
  --bs-position: start;
}
[dir=rtl] .dropdown-menu-end {
  --bs-position: end;
}
[dir=rtl] .notification .notification-body .transform-arrow {
  @apply rotate-180;
}
[dir=rtl] .notification .notification-body .bi-skip-backward::before {
  @apply content-[""];
}
[dir=rtl] .notification .notification-body .bi-skip-forward::before {
  @apply content-[""];
}

/* End:: rtl  */
/* Start:: reviews */
.testimonialSwiper01 {
  @apply pt-0 pb-16 px-0;
}

.swiper-pagination-custom {
  @apply flex justify-center items-center px-0 py-5;
}

.swiper-pagination-custom .swiper-pagination-bullet {
  @apply w-7 h-7 overflow-hidden rounded-[50%] !important;
}

.swiper-pagination-custom .swiper-pagination-bullet img {
  @apply w-full h-full object-cover;
}

.swiper-pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active img {
  @apply rounded-[50%] border-[3px] border-solid border-primary;
}

.testimonialSwiper01 .swiper-slide {
  scale: 0.8;
}

.testimonialSwiper01 .swiper-slide.swiper-slide-active {
  @apply opacity-100 max-w-[500px];
  scale: 1;
}
.testimonialSwiper01 .swiper-slide.swiper-slide-active .review-quote {
  @apply text-[rgba(255,255,255,0.2)];
}

.review-quote {
  @apply z-0;
}

.testimonialSwiper01 .swiper-slide.swiper-slide-active .card {
  @apply bg-primary text-customwhite;
}
.testimonialSwiper01 .swiper-slide.swiper-slide-active .card .card-body {
  @apply text-white;
}
.testimonialSwiper01 .swiper-slide.swiper-slide-active .card h6 {
  @apply text-white !important;
}

.review-quote {
  @apply text-primary/50 opacity-100 absolute text-5xl start-auto end-[3px] top-[7.25rem] !important;
}
.review-quote.primary {
  @apply text-primary/40;
}
.review-quote.secondary {
  @apply text-secondary/40;
}
.review-quote.success {
  @apply text-success/40;
}
.review-quote.warning {
  @apply text-warning/40;
}

.reviews-container .box {
  @apply bg-[rgba(255,255,255,0.05)] shadow-none mb-0;
}

.review-style-2 .testimonialSwiperService {
  @apply pt-0 pb-7 px-0 !important;
}
.review-style-2 .testimonialSwiperService .swiper-pagination-bullet.swiper-pagination-bullet-active {
  @apply bg-primary/10 !important;
}

/* End:: reviews */
/* Start:: sortable js */
ul.sortable-list li,
ol.sortable-list li {
  @apply bg-primary/[0.05] border border-primary/[0.05] font-medium mx-0 my-[3px] rounded-[0.3rem] border-solid;
}
ul.sortable-list li.filtered,
ol.sortable-list li.filtered {
  @apply bg-danger text-white;
}
ul.sortable-list li.selected,
ol.sortable-list li.selected {
  @apply bg-primary border text-white border-solid border-primary;
}
ul.sortable-list .list-group-item + .list-group-item,
ol.sortable-list .list-group-item + .list-group-item {
  @apply border-t;
}
ul#shared-right li, ul#cloning-right li, ul#disabling-sorting-right li,
ol#shared-right li,
ol#cloning-right li,
ol#disabling-sorting-right li {
  @apply bg-secondary/[0.05] border border-secondary/[0.05] border-solid;
}
ul .handle,
ol .handle {
  @apply cursor-grab;
}

.grid-square {
  @apply w-[100px] h-[100px] inline-block bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 me-6 mb-6 p-5 rounded-lg border-solid;
}

.nested-sortable,
.nested-1,
.nested-2,
.nested-3 {
  @apply mt-[5px];
}
.nested-sortable .list-group-item + .list-group-item,
.nested-1 .list-group-item + .list-group-item,
.nested-2 .list-group-item + .list-group-item,
.nested-3 .list-group-item + .list-group-item {
  @apply border-t;
}

.nested-1,
.nested-2,
.nested-3 {
  @apply bg-primary/[0.05] border border-primary/[0.05] font-medium border-solid;
}

/* End:: sortable js */
/* Start:: search results */
.search-result-input .form-control {
  @apply ps-9;
}

.search-tab.tab-style-6 .nav-item .nav-link {
  @apply bg-primary/10;
}
.search-tab.tab-style-6 .nav-item .nav-link.active {
  @apply bg-primary;
}

.search-result-icon {
  @apply z-[5];
}

.searched-item-main-link:hover {
  @apply text-primary underline;
}

.glightbox.card.search-images-card:hover:after {
  @apply content-none;
}

.avatar.avatar-search {
  @apply h-[120px] w-[inherit];
}

.search-images-card {
  @apply transition-all duration-[0.4s];
}
.search-images-card:hover {
  @apply shadow-[0px_4px_16px_black1] transition-all duration-[0.4s];
}

/* End:: search results */
.ad-gallery {
  @apply hidden;
}

@media (max-width: 767.98px) {
  .swiper-related-jobs .swiper-button-next,
  .swiper-related-jobs .swiper-button-prev {
    @apply hidden;
  }
}
/* Start:: Full Canendar */
@media (min-width: 1400px) {
  .column-list {
    @apply gap-x-2;
    -moz-column-count: 2;
    column-count: 2;
    column-gap: 8px;
  }
}
/* End:: Full Canendar */
/* Start:: Crypto Charts */
#btc-chart .apexcharts-canvas,
#btc-chart .apexcharts-svg,
#bytecoin-chart .apexcharts-canvas,
#bytecoin-chart .apexcharts-svg,
#dash-chart .apexcharts-canvas,
#dash-chart .apexcharts-svg,
#dash-price-graph .apexcharts-canvas,
#dash-price-graph .apexcharts-svg,
#eos-chart .apexcharts-canvas,
#eos-chart .apexcharts-svg,
#eth-chart .apexcharts-canvas,
#eth-chart .apexcharts-svg,
#glm-chart .apexcharts-canvas,
#glm-chart .apexcharts-svg,
#iota-chart .apexcharts-canvas,
#iota-chart .apexcharts-svg,
#monero-chart .apexcharts-canvas,
#monero-chart .apexcharts-svg,
#ripple-chart .apexcharts-canvas,
#ripple-chart .apexcharts-svg {
  @apply w-[120px] !important;
}

/* End:: Crypto Charts */
/* Start:: Print */
@media print {
  * {
    @apply shadow-none;
    text-shadow: none !important;
  }
  *::before,
  *::after {
    text-shadow: none !important;
    @apply shadow-none;
  }
  a:not(.btn) {
    @apply underline;
  }
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  pre {
    @apply whitespace-pre-wrap border border-defaultborder break-inside-avoid border-solid;
  }
  blockquote {
    @apply border border-defaultborder break-inside-avoid border-solid;
  }
  thead {
    @apply table-header-group;
  }
  tr,
  img {
    @apply break-inside-avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    @apply break-after-avoid;
  }
  @page {
    size: a4;
  }
  body,
  .container {
    @apply min-w-[992px] !important;
  }
  .table,
  .text-wrap table {
    @apply border-collapse;
  }
  .table td,
  .text-wrap table td,
  .table th,
  .text-wrap table th {
    @apply bg-white dark:bg-bodybg !important;
  }
  .table-bordered th,
  .text-wrap table th,
  .table-bordered td,
  .text-wrap table td {
    @apply border border-defaultborder border-solid;
  }
  .app-sidebar,
  .app-content .page-header-breadcrumb,
  .app-header,
  .footer {
    @apply hidden !important;
  }
  .main-content.app-content {
    @apply pt-2.5 !important;
  }
}
/* End:: Print */
.loader-disable {
  @apply hidden !important;
}

.card-img,
.card-img-bottom,
.card-img-top {
  @apply w-full;
}

@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    @apply max-w-[1140px] mx-auto;
  }
}
@media (min-width: 1400px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    @apply max-w-[1320px] mx-auto !important;
  }
}
/* End:: custom */
[type=text],
input:where(:not([type])),
[type=email],
[type=url],
[type=password],
[type=number],
[type=date],
[type=datetime-local],
[type=month],
[type=search],
[type=tel],
[type=time],
[type=week],
[multiple],
textarea,
select {
  @apply border-inputborder dark:border-defaultborder/10 rounded-sm dark:bg-bodybg !important;
}

.waves-effect {
  @apply inline-flex !important;
}

.box .footer-card-icon {
  @apply w-[60px] h-[60px];
}

.box .card-text {
  font-size: 0.813rem;
}

.box.overlay-card .box-footer {
  @apply border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box.overlay-card {
  @apply relative;
  @apply before:bg-black/[0.25] before:absolute before:rounded-[0.3rem] before:inset-0;
}

.progress-bar-striped {
  @apply bg-custom-gradient bg-custom-size;
}

code {
  @apply text-[#d63384] text-sm;
}

.simplebar-scrollbar:before {
  @apply bg-gray-400 dark:bg-light !important;
}

[dir=rtl] .breadcrumb-item + .breadcrumb-item {
  @apply before:inline-flex before:rotate-180;
}

.activity-feed li {
  @apply mb-[1.25rem];
}

.ratio > * {
  @apply absolute top-0 left-0 w-full h-full;
}

.invoice-quantity-container input {
  @apply w-[50px] !important;
}

.search-images-card img {
  @apply rounded-t-sm;
}

.ti-img-thumbnail {
  @apply p-1 rounded-md border border-defaultborder dark:border-defaultborder/10 max-w-full h-auto dark:bg-bodybg2 mx-auto;
}

.ti-img-thumbnail-rounded {
  @apply p-1 rounded-full border border-defaultborder dark:border-defaultborder/10 max-w-full h-auto dark:bg-bodybg2 mx-auto;
}

#responsive-overlay {
  @apply transition-all duration-100 fixed inset-0 z-[49] invisible bg-opacity-0;
}
#responsive-overlay.active {
  @apply bg-opacity-50 dark:bg-opacity-80 visible;
}

.container,
.container-fluid {
  @apply w-full px-[calc(1.5rem*0.5)] mx-auto;
}

.container {
  @apply sm:max-w-[540px] md:max-w-[720px] lg:max-w-[960px] xl:max-w-[1140px] xxl:max-w-[1320px];
}

@media (min-width: 576px) {
  .container,
  .container-sm {
    @apply max-w-[540px] mx-auto;
  }
}
@media (min-width: 768px) {
  .container,
  .container-md,
  .container-sm {
    @apply max-w-[720px] mx-auto;
  }
}
@media (min-width: 992px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm {
    @apply max-w-[960px] mx-auto;
  }
}
@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    @apply max-w-[1140px] mx-auto;
  }
}
@media (min-width: 1400px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    @apply max-w-[1320px] mx-auto;
  }
}
a {
  @apply cursor-pointer;
}

#hs-select-temporary {
  @apply w-full;
}

code[class*=language-] {
  @apply p-4 !important;
}

#switcher-body [type=radio] {
  @apply border-[#e2e8f0] dark:border-white/30 !important;
}

@media (max-width: 576px) {
  .header-element.header-country .ti-dropdown-menu {
    @apply w-full start-0 end-auto !important;
  }
}
.fe-arrow-left {
  @apply rtl:before:content-[""];
}

.fe-arrow-right {
  @apply rtl:before:content-[""];
}

#navbar-collapse-basic1,
#navbar-collapse-basic2 {
  @apply h-full !important;
}

.page-item:not(:first-child) .page-link {
  @apply ms-[calc(var(--bs-width)*-1)];
}

.main-sidebar::-webkit-scrollbar {
  height: 0px;
}

[dir=rtl] #contact-phone {
  @apply dir-rtl;
}

@media (min-width: 576px) {
  #folders-close-btn {
    display: none;
  }
}
.hire-list li {
  @apply mb-[1.68rem];
}

[data-width=boxed] .offer-item-img {
  display: none;
}

.testimonialSwiper01 {
  @apply pb-[4rem] !important;
}

.testimonialSwiper01 .swiper-slide.swiper-slide-active .box {
  @apply bg-primary text-white !important;
}
.testimonialSwiper01 .swiper-slide.swiper-slide-active .box-body {
  @apply text-white !important;
}
.testimonialSwiper01 .swiper-slide.swiper-slide-active div {
  @apply text-white !important;
}

#hs-custom-backdrop-modal-backdrop {
  @apply z-[100] !important;
}

[dir=rtl] .choices[data-type*=select-multiple] .choices__button, [dir=rtl] .choices[data-type*=text] .choices__button {
  inset-inline-start: 17px;
}
[dir=rtl] .ql-editor ol {
  padding-right: 1.5em;
  padding-left: 0px;
}
[dir=rtl] .ql-editor li > .ql-ui:before {
  margin-right: -1.5em;
  margin-left: 0.3em;
  text-align: left;
}

.choices[data-type*=select-one].is-open::after {
  @apply border-b-transparent !important;
}

.noUi-handle {
  @apply border border-defaultborder dark:border-defaultborder/10 !important;
}

.noUi-handle:after, .noUi-handle:before {
  @apply bg-defaultborder dark:bg-defaultborder/10 !important;
}

div:where(.swal2-container) img:where(.swal2-image) {
  @apply max-w-[90%] !important;
}

.choices__input {
  @apply p-0 !important;
}

.form-check-input:focus {
  @apply border-primary !important;
}

.card-img {
  @apply rounded-sm !important;
}

.choices__list input {
  @apply ps-[5px] !important;
}

.apexcharts-menu .apexcharts-menu-item {
  @apply hover:bg-light !important;
}

@media (max-width: 335px) {
  .main-chart-wrapper .chat-content {
    @apply max-h-[calc(100vh-19.5rem)];
  }
}
.page-header-breadcrumb h1 {
  @apply leading-none;
}

.swiper-button-next:after, .swiper-button-prev:after {
  @apply text-[12px] !important;
}

@media (max-width: 550px) {
  #rangearea-basic .apexcharts-toolbar, #rangearea-combo .apexcharts-toolbar {
    @apply translate-y-[19px];
  }
}
button.gridjs-sort-asc {
  @apply dark:invert-[1] !important;
}

button.gridjs-sort-desc {
  @apply dark:invert-[1] !important;
}

@media (min-width: 480px) {
  [dir=rtl] .tabulator .tabulator-footer .tabulator-page-size {
    @apply pe-[2rem] ps-3 !important;
  }
}

.todo-handle {
  @apply cursor-move !important;
}

#file-manager-storage .apexcharts-pie line, #file-manager-storage .apexcharts-pie circle {
  @apply stroke-transparent !important;
}

.tagify.form-control {
  @apply p-0 !important;
}

input[type=color i]::-webkit-color-swatch-wrapper {
  @apply p-0 !important;
}

.ql-bubble .ql-tooltip-editor input[type=text] {
  @apply bg-black !important;
}
.ql-bubble .ql-tooltip-editor input[type=text]::placeholder {
  @apply text-white !important;
}

.choices__list--dropdown input {
  @apply py-2 !important;
}

.choices__list--dropdown, .choices__list[aria-expanded] {
  @apply dark:shadow-none !important;
}

.vr {
  @apply inline-block self-stretch w-[1px] min-h-[1em] bg-defaultborder dark:bg-defaultborder/10;
}

[dir=rtl] #chart-10, [dir=rtl] #chart-11, [dir=rtl] #chart-12, [dir=rtl] #chart-13 {
  @apply end-0 !important;
}

[dir=rtl] .ts-wrapper.single .ts-control {
  @apply after:left-[15px] after:right-auto !important;
}

[dir=rtl] .ts-control .item, [dir=rtl] .ts-control input {
  @apply ms-2;
}
[dir=rtl] .form-select {
  @apply pr-[0.75rem] !important;
}
[dir=rtl] th.gridjs-th .gridjs-th-content {
  @apply float-right !important;
}
[dir=rtl] .gridjs-pagination .gridjs-summary {
  @apply float-right !important;
}

#column-rotated-labels .apexcharts-xaxis {
  @apply translate-y-[-43px];
}

.tabulator .tabulator-footer .tabulator-footer-contents {
  @apply flex-wrap gap-2 !important;
}

[data-width=boxed] .error-bg {
  @apply w-full !important;
}

.page-header-breadcrumb .breadcrumb {
  @apply mb-[0.35rem] !important;
}

.TASK-kanban-board .simplebar-scrollbar {
  @apply dark:before:bg-defaultborder/50 !important;
}

.select2-selection__choice__remove {
  @apply border-s-0 !important;
}

/* Start:: dashboard_styles */
/* New Styles */
#referrals-chart .apexcharts-pie text, #job-acceptance .apexcharts-pie text, #portfolio .apexcharts-pie text, #patients-chart .apexcharts-pie text, #follow-on-device .apexcharts-pie text {
  @apply fill-defaulttextcolor !important;
}

.top-categories .table th, .top-categories .table td {
  @apply py-[0.83rem] px-[0.75rem] !important;
}

ul.analytics-activity li {
  @apply mb-[1.17rem];
}

#orders .apexcharts-pie line, #orders .apexcharts-pie circle {
  @apply stroke-transparent !important;
}
#orders .apexcharts-pie text {
  @apply fill-defaulttextcolor dark:fill-defaulttextcolor/60 !important;
}
#orders .apexcharts-legend {
  @apply px-9 py-0 !important;
}

#circlechart .apexcharts-datalabels-group text, #recent-orders .apexcharts-datalabels-group text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 fill-defaulttextcolor dark:fill-defaulttextcolor/80 !important;
}

.sales-country-list {
  @apply mb-0;
}
.sales-country-list li {
  @apply mb-[1.05rem];
}
.sales-country-list li:last-child {
  @apply mb-0;
}

.analytics-visitors-countries li {
  @apply mb-[1.4rem];
}

#chart-21, #chart-22, #chart-23, #chart-24 {
  @apply mb-[-7px];
}

.top-category-name {
  @apply relative ps-4;
}
.top-category-name:before {
  @apply content-[""] absolute w-2.5 h-2.5 rounded-[50%] border-2 border-solid border-primary -start-0.5 top-1.5;
}
.top-category-name.one:before {
  @apply border-2 border-solid border-primary;
}
.top-category-name.two:before {
  @apply border-primarytint1color border-2 border-solid;
}
.top-category-name.three:before {
  @apply border-primarytint2color border-2 border-solid;
}
.top-category-name.four:before {
  @apply border-primarytint3color border-2 border-solid;
}
.top-category-name.five:before {
  @apply border-2 border-solid border-warning;
}

.box.main-dashboard-banner {
  @apply z-[1] shadow-none border-0;
}
.box.main-dashboard-banner:before {
  @apply content-[""] absolute w-full h-full bg-[linear-gradient(to_right_top,#ff9794,#fd8dac,#e58cc6,#bf7cdc,#6f78f4)] z-[-1];
}
.box.main-dashboard-banner:after {
  @apply content-[""] absolute w-full h-full bg-[url(../public/assets/images/media/media-87.png)] bg-cover bg-center bg-repeat z-[-1] opacity-10;
}
.box.main-dashboard-banner img {
  @apply absolute w-[200px] h-[200px] bottom-[-30px] end-2.5;
}

.box.main-dashboard-banner.main-dashboard-banner2:before {
  @apply bg-primary bg-none;
}
.box.main-dashboard-banner.main-dashboard-banner2:after {
  @apply bg-[url(../public/assets/images/media/media-87.png)];
}
.box.main-dashboard-banner.main-dashboard-banner2 img {
  @apply w-[170px] h-[170px] bottom-[-9px];
}

.recent-activity-list {
  @apply relative mb-0 before:absolute before:w-[1px] before:bg-defaultborder before:dark:bg-defaultborder/10 before:h-[82%] before:start-[65px] before:top-3;
}
.recent-activity-list li {
  @apply relative mb-[1.1rem] ps-[5.75rem];
}
.recent-activity-list li .activity-time {
  @apply absolute start-0 top-0.5;
}
.recent-activity-list li:nth-child(1) {
  @apply before:bg-primary dark:before:bg-primary before:shadow-[0px_0px_0px_4px_rgba(var(--primary-rgb),0.15)];
}
.recent-activity-list li:nth-child(2) {
  @apply before:bg-primarytint1color dark:before:bg-primarytint1color before:shadow-[0px_0px_0px_4px_rgba(227,84,212,0.15)];
}
.recent-activity-list li:nth-child(3) {
  @apply before:bg-primarytint2color dark:before:bg-primarytint2color before:shadow-[0px_0px_0px_4px_rgba(255,93,159,0.15)];
}
.recent-activity-list li:nth-child(4) {
  @apply before:bg-primarytint3color dark:before:bg-primarytint3color before:shadow-[0px_0px_0px_4px_rgba(255,142,111,0.15)];
}
.recent-activity-list li:nth-child(5) {
  @apply before:bg-secondary dark:before:bg-secondary before:shadow-[0px_0px_0px_4px_rgba(158,92,247,0.15)];
}
.recent-activity-list li:nth-child(6) {
  @apply before:bg-warning dark:before:bg-warning before:shadow-[0px_0px_0px_4px_rgba(158,92,247,0.15)];
}
.recent-activity-list li:before {
  @apply absolute content-[""] w-[7px] h-[7px] bg-white dark:bg-bodybg rounded-[50%] start-[62px] top-[7px];
}
.recent-activity-list li:last-child {
  @apply mb-0;
}

.main-content-card {
  @apply relative z-[1] overflow-hidden border border-defaultborder dark:border-defaultborder/10 border-solid;
}
.main-content-card:before {
  content: "";
  @apply content-[""] absolute w-full h-full bg-[url(../public/assets/images/media/media-89.png)] bg-center bg-repeat z-[-1] opacity-60 bg-cover inset-0;
}

[class=dark] .main-content-card:before {
  @apply invert-[1] opacity-0;
}

.offer-card {
  @apply z-[1] bg-[url(../public/assets/images/media/backgrounds/1.jpg)] relative overflow-hidden bg-cover bg-bottom before:content-[""] before:absolute before:w-full before:h-full before:bg-primary/70 before:z-[-1];
}

.offer-card-details .offer-item-img {
  @apply absolute h-48 -end-0.5 bottom-[18px];
}

@media (max-width: 380px) {
  .offer-card-details .offer-item-img {
    @apply hidden;
  }
}
.order-content {
  @apply mb-[-25px];
}

.ecommerce-recent-activity {
  @apply relative overflow-hidden before:absolute before:content-[""] before:w-px before:bg-transparent before:border before:border-defaultborder dark:before:border-defaultborder/10 before:h-full before:border-dashed before:start-[0.8rem] before:top-[1.4375rem];
}

.ecommerce-recent-activity li {
  @apply pb-[1.485rem];
}

#revenue-report {
  @apply pt-[6.1rem];
}

#profit-report {
  @apply absolute w-full z-[1] bottom-0 inset-x-0;
}

.task-list-tab li {
  @apply mb-[1.55rem] last:mb-0;
}

#Leads-overview .apexcharts-yaxis .apexcharts-text {
  @apply fill-[#b6c5d3];
}

.timeline-widget-list {
  @apply mb-2 last:mb-0;
}

.courses-instructors li {
  @apply mb-[1.16rem];
}

.schedule-list li {
  @apply mb-[1.4rem];
}

.schedule-list li:last-child,
.courses-instructors li:last-child {
  @apply mb-0;
}

.market-cap-list li {
  @apply mb-[1.1rem] last:mb-0;
}

.nft-banner-card {
  @apply z-[1] before:content-[""] before:absolute before:w-full before:h-full before:bg-[url(../public/assets/images/nft-images/35.jpg)] before:bg-cover before:bg-center before:bg-no-repeat before:z-[-1] before:rounded-lg before:inset-0 after:content-[""] after:absolute after:z-[-1] after:w-full after:h-full after:bg-[linear-gradient(to_right,rgba(0,0,0,0.5)_30%,transparent)] after:bg-cover after:bg-center after:bg-no-repeat after:rounded-xl after:inset-0;
}

.nft-main-banner-image {
  @apply h-48 rounded-[50%];
}

.nft-auction-time {
  @apply absolute text-[11px] z-[1] px-2 py-[5px] rounded-br-[15px] rtl:rounded-bl-[15px] rtl:rounded-br-none rtl:rounded-tl-none start-0 top-0 rounded-tl-sm rtl:rounded-tr-sm;
}

.nft-like-badge {
  @apply absolute bg-[rgba(0,0,0,0.5)] rounded-none end-0 bottom-0;
}

#nft-collections-slide {
  @apply rtl:dir-ltr;
}

.bid-amt svg {
  @apply w-[11px] h-[17px] mb-1;
}

.bg-crypto-balance {
  @apply relative z-[1] before:content-[""] before:absolute before:bg-[url(../public/assets/images/media/media-88.jpg)] before:z-[-1] before:w-full before:h-full before:opacity-[0.15] before:bg-cover before:bg-bottom before:rounded-md before:-scale-y-100 before:start-0 before:top-0 after:content-[""] after:absolute after:z-[-1] after:h-full after:w-full after:bg-[url(../public/assets/images/media/media-73.png)] after:bg-no-repeat after:opacity-[0.07] after:-scale-x-100 after:end-[0.15rem] after:top-0;
}

.box.main-dashboard-banner.project-dashboard-banner:before {
  @apply bg-primary bg-none;
}
.box.main-dashboard-banner.project-dashboard-banner:after {
  @apply bg-[url(../public/assets/images/media/media-82.jpg)] opacity-[0.35];
}
.box.main-dashboard-banner.project-dashboard-banner img {
  @apply w-[197px] h-[168px] end-[-5px] saturate-[0.9] opacity-95 -bottom-1;
}

.daily-tasks-time {
  @apply w-[4.5rem] bg-light text-center text-[13px] p-1 rounded-[7px];
}

.box.course-main {
  @apply z-[1];
}
.box.course-main:before {
  @apply content-[""] absolute w-full h-full bg-cover bg-repeat z-[-1] bg-bottom opacity-20 bg-[url(../public/assets/images/media/media-80.jpg)] -scale-x-100;
}
.box.course-main:after {
  @apply content-[""] absolute w-full h-full bg-cover bg-repeat z-[-1] bg-[rgb(0_0_0_/_6%)] -scale-x-100 end-0;
}

.Upcoming-courses-schedule > .list-item {
  @apply mb-5;
}
.Upcoming-courses-schedule > .list-item:last-child {
  @apply mb-0;
}
.Upcoming-courses-schedule > .list-item:nth-child(2) .course-schedule::before {
  @apply bg-primary;
}
.Upcoming-courses-schedule > .list-item:nth-child(3) .course-schedule::before {
  @apply bg-primarytint1color;
}
.Upcoming-courses-schedule > .list-item:nth-child(4) .course-schedule::before {
  @apply bg-primarytint2color;
}
.Upcoming-courses-schedule > .list-item:nth-child(5) .course-schedule::before {
  @apply bg-primarytint3color;
}
.Upcoming-courses-schedule > .list-item:nth-child(6) .course-schedule::before {
  @apply bg-secondary;
}

.course-schedule {
  @apply relative;
}
.course-schedule::before {
  @apply content-[""] absolute start-[-15px] h-[96%] w-[0.2rem] rounded-md bg-info top-0;
}

.top-categories li {
  @apply mb-[1.5rem];
}

.svg-icon-med {
  @apply w-20 h-20 me-[-1.6875rem] bg-[rgba(0,0,0,0.3)] mb-[-22px] p-[1.1875rem] rounded-[50%];
}
.svg-icon-med.opacity-1 {
  @apply opacity-70 !important;
}
.svg-icon-med.text-primary {
  @apply bg-primary/20;
}
.svg-icon-med.text-primarytint1color {
  @apply bg-primarytint1color/20;
}
.svg-icon-med.text-primarytint2color {
  @apply bg-primarytint2color/20;
}
.svg-icon-med.text-primarytint3color {
  @apply bg-primarytint3color/20;
}

.med-banner-card {
  @apply relative overflow-hidden bg-primary z-0 !important;
}
.med-banner-card::before {
  @apply content-[""] absolute w-full h-full bg-[url(../public/assets/images/media/media-72.jpg)] bg-cover bg-no-repeat z-[-1] opacity-25 start-0 top-0;
}
.med-banner-card:after {
  @apply content-[""] absolute z-[-1] h-44 w-44 bg-white opacity-5 rounded-[50%] end-[5.15rem] top-5;
}

.med-banner-img {
  @apply h-44 absolute content-[""] mt-0 end-[42px] top-[5px];
}

.upcoming-shedule .sh-link {
  @apply min-w-[70px] max-w-[70px] transition-all ease-linear duration-[0.3s] bg-light rounded-[3px] text-center p-[0.6rem] flex flex-col;
}

.upcoming-shedule .sh-shedule-container li {
  @apply mb-[1.4rem] last:mb-0;
}

.pos-category .box.active,
.pos-category .box:hover {
  @apply border border-primary/30 bg-primary/[0.05] border-solid;
}

.stretched-link {
  @apply after:absolute after:inset-0 after:z-[1];
}

@media (min-width: 1400px) {
  .list-wrapper .card-item {
    @apply w-[25%] px-3;
  }
}
@media (min-width: 576px) and (max-width: 1400px) {
  .list-wrapper .card-item {
    @apply w-[50%] px-3;
  }
}
@media (max-width: 576px) {
  .list-wrapper .card-item {
    @apply w-[100%] px-3;
  }
}
.pos-category .box {
  @apply border border-solid border-transparent;
}

.pos-category .box .categorymenu-icon {
  @apply bg-primary/40;
}

.pos-category .box .avatar svg {
  @apply w-[4.25rem] h-[4.25rem] fill-primary;
}

.pos-card-image img {
  @apply h-[10.5rem] w-40 relative mt-[-54px];
}

.categories-arrow {
  @apply w-7 h-7 bg-white dark:bg-bodybg flex justify-center items-center cursor-pointer shadow-[0px_6px_16px_2px_rgba(0,0,0,0.05)] rounded-[50%];
}

.categories-arrow i {
  @apply text-base text-defaulttextcolor;
}

[dir=rtl] .categories-arrow,
[dir=rtl] .table-icon i {
  @apply -scale-x-100;
}
[dir=rtl] .podcast-banner:before {
  @apply scale-x-100;
}

.box.out-of-stock {
  @apply opacity-[0.65];
}

.order-summ.product-quantity-container {
  @apply w-28;
}

.podcast-banner {
  @apply relative z-[1] before:content-[""] before:absolute before:w-full before:h-full before:bg-[url("../public/assets/images/media/media-70.jpg")] before:bg-cover before:bg-center before:bg-no-repeat before:z-[-1] before:rounded-lg before:-scale-x-100 before:inset-0;
}

.podcast-banner:after {
  @apply content-[""] absolute z-[-1] w-full h-full bg-gradient-to-br from-primary to-secondary bg-cover bg-center bg-no-repeat opacity-75 rounded-lg inset-0;
}

.podcasters {
  @apply mb-0;
}

.podcasters li {
  @apply mb-4 last:mb-0;
}

.podcast-playing-now {
  @apply w-24 h-24 flex justify-center mx-auto my-0;
}

.podcast-playing-now img {
  @apply rounded-xl;
}

.podcast-playing-progress {
  @apply w-full;
}

.podcast-playing-progress .progress-custom .progress-bar:after {
  @apply content-[""] w-3 h-3 shadow-[0_0.313rem_0.313rem_primary/20] bg-white absolute end-[-0.375rem] border-primary rounded-[50%] border-4 border-solid -top-1;
}

.podcast-recently-played-list {
  @apply mb-0;
}

.podcast-recently-played-list li {
  @apply mb-4 last:mb-0;
}

.bg-playing-image {
  @apply bg-[url(../public/assets/images/podcast/5.jpg)] bg-cover w-full bg-no-repeat overflow-hidden text-white relative z-0 rounded-lg;
}
.bg-playing-image:before {
  @apply content-[""] absolute w-full h-full z-[-1] bg-primary opacity-70 start-0 top-0;
}

.school-card svg {
  @apply w-[2.95rem] h-[2.95rem];
}

.school-activity-list {
  @apply relative mb-0;
}
.school-activity-list:before {
  @apply absolute content-[""] w-px bg-defaultborder dark:bg-defaultborder/10 h-[85%] start-[18px] top-3;
}
.school-activity-list li {
  @apply relative mb-[1.05rem] ps-11;
}
.school-activity-list li .activity-time {
  @apply absolute start-0 top-0.5;
}
.school-activity-list li:nth-child(1) {
  @apply before:bg-primary before:shadow-[0px_0px_0px_4px_rgba(var(--primary-rgb),0.15)] !important;
}
.school-activity-list li:nth-child(2) {
  @apply before:bg-primarytint1color before:shadow-[0px_0px_0px_4px_rgba(227,84,212,0.15)] !important;
}
.school-activity-list li:nth-child(3) {
  @apply before:bg-primarytint2color before:shadow-[0px_0px_0px_4px_rgba(255,93,159,0.15)] !important;
}
.school-activity-list li:nth-child(4) {
  @apply before:bg-primarytint3color before:shadow-[0px_0px_0px_4px_rgba(255,142,111,0.15)] !important;
}
.school-activity-list li:nth-child(5) {
  @apply before:bg-secondary before:shadow-[0px_0px_0px_4px_rgba(158,92,247,0.15)] !important;
}
.school-activity-list li:nth-child(6) {
  @apply before:bg-warning before:shadow-[0px_0px_0px_4px_rgba(255,198,88,0.15)] !important;
}
.school-activity-list li:before {
  @apply absolute content-[""] w-[7px] h-[7px] bg-white dark:bg-bodybg rounded-[50%] start-[15px] top-[7px];
}
.school-activity-list li:last-child {
  @apply mb-0;
}

.personal-favourite {
  @apply mb-0;
}

.personal-favourite li {
  @apply mb-6;
}

.personal-favourite li:last-child {
  @apply mb-0;
}

.social-cards::after {
  @apply content-[""] absolute end-[-18px] text-light text-[100px] leading-none font-medium z-[-1] opacity-[0.09] -top-8 font-remix;
}

.social-cards {
  @apply z-[1] overflow-hidden;
}

.social-cards.insta::after {
  @apply content-[""] text-primary;
}

.social-cards.linkedin::after {
  @apply content-[""] text-info;
}

.social-cards.fb::after {
  @apply content-[""] text-primary;
}

.social-cards.twit::after {
  @apply content-[""] text-dark;
}

.social-cards.youtube::after {
  @apply content-[""] text-danger;
}

.social-cards.msgr::after {
  @apply content-[""] text-secondary;
}

.stock-sparkline-charts {
  @apply bottom-[-11px] absolute end-0;
}

#stockCap .apexcharts-selection-rect {
  @apply fill-[rgba(0,0,0,0.4)] dark:fill-[255,255,255,0.4];
}

.crm-card {
  @apply relative overflow-hidden z-0 after:content-[""] after:absolute after:z-[-1] after:h-full after:w-full after:end-[-1.85rem] after:bg-[url(../public/assets/images/media/media-73.png)] after:bg-no-repeat after:opacity-[0.075] after:-scale-x-100;
}

@media (max-width: 575.98px) {
  #crm-revenue-analytics .apexcharts-canvas .apexcharts-title-text {
    @apply text-[0.71rem];
  }
}
.buy-crypto .choices .choices__inner {
  @apply pb-[7.5px];
}

.crypto-input .choices__inner {
  @apply rounded-tl-none rounded-bl-none rtl:rounded-tr-none rtl:rounded-br-none rtl:rounded-tl-md rtl:rounded-bl-md !important;
}

#buy-crypto2 .choices,
#sell-crypto2 .choices {
  @apply overflow-visible;
}

@media (min-width: 1400px) {
  .banner10-img {
    @apply absolute h-28 end-[10px] top-[39px];
  }
}
[dir=rtl] #chart-21 .apexcharts-canvas, [dir=rtl] #chart-22 .apexcharts-canvas, [dir=rtl] #chart-23 .apexcharts-canvas, [dir=rtl] #chart-24 .apexcharts-canvas {
  @apply -scale-x-100;
}
[dir=rtl] #chart-21 .apexcharts-tooltip, [dir=rtl] #chart-22 .apexcharts-tooltip, [dir=rtl] #chart-23 .apexcharts-tooltip, [dir=rtl] #chart-24 .apexcharts-tooltip {
  @apply -scale-x-100;
}

.card-headertabs.nav-tabs-header .nav-item .nav-link {
  @apply bg-light;
}

.card-headertabs.nav-tabs-header .nav-item .nav-link.active {
  @apply bg-primary/10;
}

[data-width=boxed] .events-width {
  @apply w-[200px];
}
[data-width=boxed] .events-width .timeline-widget-content {
  @apply overflow-hidden text-ellipsis whitespace-nowrap;
}

[class=dark] .xrp-logo {
  @apply invert-[1];
}

#balanceAnalysis .apexcharts-radialbar text, #monthly-target .apexcharts-radialbar text {
  @apply fill-defaulttextcolor !important;
}

@media (max-width: 767.98px) {
  .med-banner-img {
    @apply hidden;
  }
  .med-banner-card:after {
    @apply content-none;
  }
}
@media (min-width: 1400px) and (max-width: 1600px) {
  .offer-card-details .offer-item-img {
    @apply hidden;
  }
}
#bitcoin-change .apexcharts-svg, #bitcoin-change .apexcharts-canvas, #etherium-change .apexcharts-svg, #etherium-change .apexcharts-canvas, #tether-change .apexcharts-svg, #tether-change .apexcharts-canvas, #solana-change .apexcharts-svg, #solana-change .apexcharts-canvas, #cardano-change .apexcharts-svg, #cardano-change .apexcharts-canvas, #binance-change .apexcharts-svg, #binance-change .apexcharts-canvas {
  @apply w-[120px] !important;
}

#revenue-stats .apexcharts-pie text {
  @apply fill-defaulttextcolor dark:fill-defaulttextcolor/80 text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

/* New Styles */
/* End:: dashboard_styles */
#gender-chart .apexcharts-canvas {
  @apply w-full !important;
}

/* Start:: error */
.error-bg {
  @apply relative w-full h-full bg-[rgba(255,255,255,0.77)] min-h-[100vh];
}
.error-bg:before {
  @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/svg/pattern-2.svg)] bg-no-repeat bg-cover bg-center opacity-[0.15] inset-0;
}

.error-page {
  @apply absolute w-full min-h-screen flex justify-center items-center bg-cover bg-no-repeat bg-center;
}

.error-text {
  @apply text-[11rem] font-medium leading-none;
}

@media (max-width: 575.98px) {
  .error-text {
    @apply text-5xl;
  }
}
[class=dark] .error-bg {
  @apply bg-[rgba(0,0,0,0.4)];
}

/* End:: error */
/* Start:: header */
/* Start::app-header */
.app-header {
  @apply max-w-full h-[4.25rem] z-[100] bg-white dark:bg-bodybg fixed transition-all duration-[0.1s] ease-[ease] shadow-[0px_0px_16px_0px_rgba(0,0,0,0.05)] top-0 inset-x-0 border-b border-b-headerbordercolor dark:border-b-headerbordercolor/10;
}
.app-header #mainHeaderProfile {
  @apply text-headerprimecolor;
}

@media (min-width: 992px) {
  .app-header {
    @apply ps-0; /* Remove left padding since we're using horizontal navigation */
  }
}
.header-icon-badge {
  @apply absolute text-[0.625rem] px-1 py-[0.15rem] end-[0.45rem] top-0 leading-none !important;
}

.header-icon-pulse {
  @apply absolute w-1.5 h-1.5 end-[18px] top-[4px] rounded-full;
}

.header-profile-dropdown {
  @apply min-w-[11rem];
}

/* Horizontal Navigation Styles */
.main-content {
  @apply ps-0; /* Remove left padding for horizontal layout */
}

@media (min-width: 992px) {
  .main-content {
    @apply ps-0; /* Ensure no left padding on desktop */
  }
}
/* End::app-header */
/* Start::main-header-container */
.main-header-container {
  @apply flex items-center justify-between h-full px-4;
  /* Updated header layout styles for new design */
  /* Time display styling - GMT section */
  /* Balance display styling */
  /* Profile icon styling - Logout section */
  /* Sportsbook center positioning */
}
.main-header-container .header-content-left,
.main-header-container .header-content-right {
  @apply flex items-stretch my-auto;
}
.main-header-container .header-content-right {
  @apply ps-0 !important;
}
.main-header-container .header-element {
  @apply flex items-center;
}
.main-header-container .header-center-left {
  @apply flex items-center;
}
.main-header-container .header-center-right {
  @apply flex items-center space-x-6;
}
.main-header-container .time-display {
  @apply flex items-center;
}
.main-header-container .balance-display {
  @apply flex items-center;
}
.main-header-container .profile-icon {
  @apply flex items-center;
}
.main-header-container .sportsbook-selector {
  @apply flex items-center justify-center;
}

.header-link {
  @apply flex items-center px-2 py-0;
}
.header-link:hover .header-link-icon, .header-link.show .header-link-icon {
  @apply text-headerprimecolor;
}

.header-link-icon {
  @apply text-base text-headerprimecolor transition-all duration-[ease] delay-[0.05s] relative w-8 h-8 border border-headerbordercolor dark:border-headerbordercolor/10 p-[0.4rem] rounded-[0.3rem] border-solid;
}

.dropdown-toggle {
  @apply no-underline after:content-none;
}

.main-profile-user .dropdown-menu {
  @apply w-52;
}
.main-profile-user .dropdown-item {
  @apply font-normal text-[0.813rem] text-defaulttextcolor h-[2.375rem] flex items-center px-4 py-5 hover:text-primary;
}
.main-profile-user .dropdown-item i {
  @apply text-primary opacity-100;
}

.main-header-dropdown {
  @apply top-[0.9375rem] shadow-defaultshadow;
}
.main-header-dropdown li {
  @apply border-b-defaultborder dark:border-b-defaultborder/10 border-b border-solid last:border-b-0;
}
.main-header-dropdown .dropdown-item {
  @apply px-4 py-[0.6rem] last:border-b-0;
}

.cart-dropdown .main-header-dropdown {
  @apply w-[25rem];
}

.country-selector .main-header-dropdown {
  @apply min-w-[12rem];
}

.notifications-dropdown .main-header-dropdown {
  @apply w-[21rem];
}

.header-profile-dropdown.main-header-dropdown li {
  @apply border-b-defaultborder border-b-0 border-solid;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(6rem);
  }
  100% {
    opacity: 1;
  }
  0% {
    opacity: 0;
    transform: translateY(6rem);
  }
}
[dir=rtl] .sidemenu-toggle .open-toggle {
  @apply rotate-180;
}

[dir=rtl] .app-header .dropdown-menu-end {
  --bs-position: end;
}

/* End::main-header-container */
/* Start::Header-dropdown */
.header-cart-remove i,
.dropdown-item-close1 i {
  @apply text-textmuted dark:text-textmuted/50 opacity-60;
}

.header-cart-remove:hover i,
.dropdown-item-close1:hover i {
  @apply text-danger opacity-100;
}

@media (max-width: 575.98px) {
  .header-element.dropdown {
    position: initial;
  }
}
/* End::Header-dropdown */
/* Start::header-search */
.auto-complete-search input.header-search-bar {
  @apply rounded-[0.3rem];
}

.header-search {
  @apply relative;
}

.auto-complete-search .header-search-bar {
  @apply relative min-w-[20rem] border border-defaultborder bg-transparent ps-9 pe-3 py-1.5 border-solid focus:border focus:border-defaultborder focus:bg-transparent focus:border-solid !important;
}

.header-search-icon {
  @apply absolute text-textmuted dark:text-textmuted/50 start-3 top-2;
}

/* End::header-search */
/* Start::header-country-selector */
.country-selector .header-link img {
  @apply w-7 h-7;
}

/* End::header-country-selector */
/* Start:header dropdowns scroll */
#header-notification-scroll,
#header-cart-items-scroll {
  @apply max-h-80;
}

/* End:header dropdowns scroll */
/* Start::header badge pulse */
.pulse {
  @apply block cursor-pointer animate-[pulse-secondary_2s_infinite];
}

.pulse.pulse-secondary {
  @apply shadow-[0_0_0_rgba(var(--primary-tint2-rgb),0.4)];
}

@-webkit-keyframes pulse-secondary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0.4);
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(var(--primary-tint2-rgb), 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0);
  }
}
@keyframes pulse-secondary {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0.4);
    box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0.4);
  }
  70% {
    -moz-box-shadow: 0 0 0 10px rgba(var(--primary-tint2-rgb), 0);
    box-shadow: 0 0 0 10px rgba(var(--primary-tint2-rgb), 0);
  }
  100% {
    -moz-box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0);
    box-shadow: 0 0 0 0 rgba(var(--primary-tint2-rgb), 0);
  }
}
/* End::header badge pulse */
/* Start::Header theme-mode icon style */
[class=light] .layout-setting .dark-layout {
  @apply hidden;
}

[class=light] .layout-setting .light-layout {
  @apply block;
}

.layout-setting .dark-layout {
  @apply hidden;
}

.layout-setting .light-layout {
  @apply block;
}

[class=dark] .layout-setting .light-layout {
  @apply hidden;
}

[class=dark] .layout-setting .dark-layout {
  @apply block;
}

/* End::Header theme-mode icon style */
/* Start::Header fullscreen responsive */
@media (max-width: 767.98px) {
  .header-element.header-fullscreen {
    @apply hidden;
  }
}
/* End::Header fullscreen responsive */
/* Start::Responsive header dropdowns */
@media (max-width: 575.98px) {
  .app-header .dropdown-menu {
    @apply w-full;
  }
}
/* End::Responsive header dropdowns */
/* Start::toggle */
.animated-arrow.hor-toggle {
  @apply text-center w-8 text-[1.2rem] relative me-4 mx-0 my-[0.3125rem];
}

.animated-arrow {
  @apply absolute z-[102] transition-all duration-[0.05s] ease-[ease-in-out] cursor-pointer ms-2 me-0 mt-1.5 mb-0 p-[0.3125rem] start-0 top-0;
}

.animated-arrow.hor-toggle span {
  @apply align-middle;
}

.animated-arrow span {
  @apply cursor-pointer h-[0.075rem] w-3 absolute block content-[""] transition-all duration-[0.05s] ease-[ease-in-out] before:top-[-0.375rem] before:w-[1.4375rem] after:bottom-[-0.375rem] after:w-[15px] bg-textmuted dark:bg-textmuted/50;
}

.animated-arrow span:before,
.animated-arrow span:after {
  @apply transition-all duration-[0.05s] ease-[ease-in-out] cursor-pointer h-[0.075rem] w-4 absolute block content-[""];
}

.animated-arrow span:before,
.animated-arrow span:after {
  @apply bg-textmuted dark:bg-textmuted/50;
}

/* End::toggle */
/* Start::header notification dropdown */
.header-notification-text {
  @apply max-w-[14.5rem];
}

/* End::header notification dropdown */
/* Start::header cart dropdown */
.header-cart-text {
  @apply max-w-[13rem];
}

/* End::header cart dropdown */
@media (max-width: 1199.98px) {
  .header-search-bar {
    @apply w-44;
  }
}
@media (max-width: 575.98px) {
  .main-header-container .main-header-dropdown {
    @apply top-[-0.0625rem] rounded-[0_0_defaultradius_defaultradius];
  }
}
@media (max-width: 575.98px) {
  .main-header-container .header-element .header-link {
    @apply px-[0.3rem] py-2;
  }
}
/* Start:: cart color indicator */
.text-cart-headset {
  @apply text-[#19719e];
}

.text-cart-handbag {
  @apply text-[#de8cb2];
}

.text-cart-alaramclock {
  @apply text-[#06a7ef];
}

.text-cart-sweatshirt {
  @apply text-[#decac1];
}

.text-cart-smartwatch {
  @apply text-[#fb6c67];
}

.header-profile-dropdown {
  @apply min-w-[11rem] !important;
}

@media (min-width: 575.98px) {
  .header-content-right .header-element .ti-dropdown-menu {
    @apply top-[19px] !important;
  }
}
@media (max-width: 575.98px) {
  .app-header .ti-dropdown-menu {
    @apply w-full !important;
  }
  .app-header .main-header-dropdown {
    @apply top-[11px] !important;
  }
}
/* End:: cart color indicator */
/* End:: header */
/* Start:: plugins */
/* Start:Choices JS */
.choices__inner {
  @apply bg-formcontrolbg text-defaultsize border border-inputborder dark:border-defaultborder/10 min-h-[auto] leading-[1.6] px-3 py-1.5 rounded-[0.35rem] border-solid !important;
}

.choices__list--single {
  @apply ps-0 pe-4 py-0 !important;
}

.choices[data-type*=select-one]::after {
  @apply text-textmuted dark:text-textmuted/50 !important;
}

.is-open .choices__list--dropdown, .is-open .choices__list[aria-expanded] {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.choices[data-type*=select-one] .choices__input {
  @apply p-2.5 border-b-defaultborder bg-white dark:bg-bodybg text-defaulttextcolor border-b border-solid;
}

.choices__list--dropdown {
  @apply hidden;
}

.choices[data-type*=select-one]::after {
  @apply border-t-textmuted dark:border-t-textmuted/[0.5] !important;
}

.choices__input {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.choices__list--dropdown.is-active {
  @apply block;
}

.choices__list--dropdown,
.choices__list[aria-expanded] {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 z-[1] rounded-t-none rounded-b-md border-solid;
}

.choices[data-type*=select-one].is-open::after {
  @apply border-s-transparent !important;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
  @apply bg-primary text-white !important;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
  @apply bg-primary text-white !important;
}

.choices__list--dropdown .choices__item--selectable.is-selected {
  @apply text-primary !important;
}
.choices__list--dropdown .choices__item--selectable.is-selected.is-highlighted {
  @apply text-white !important;
}

.is-open .choices__list--dropdown, .is-open .choices__list[aria-expanded] {
  @apply dark:border-defaultborder/10;
}

.choices__list--dropdown .choices__list {
  @apply max-h-60 !important;
}

.choices[data-type*=select-one]::after {
  @apply border-[textmuted_transparent_transparent] dark:border-[textmuted/50_transparent_transparent] !important;
}

.choices__input {
  @apply bg-transparent text-black !important;
}

.choices__list--multiple .choices__item {
  @apply bg-primary border border-solid border-primary !important;
}

.choices[data-type*=select-multiple] .choices__button,
.choices[data-type*=text] .choices__button {
  @apply border-s-[rgba(255,255,255,0.5)] border-s border-solid !important;
}

.choices__list--multiple .choices__item {
  @apply mb-[3px] rounded-sm px-2.5 pt-[0.025rem] pb-[0.2rem] !important;
}

.choices__list--single .choices__item {
  @apply text-defaulttextcolor !important;
}

.choices__input {
  @apply mb-0 !important;
}

.form-control-select-sm .choices__inner {
  @apply p-[0.275rem] !important;
}

.choices[data-type*=select-one].is-open::after {
  @apply mt-[-0.156rem] !important;
}

[class^=ri-],
[class*=" ri-"] {
  @apply inline-flex;
}

.choices__list--dropdown .choices__item--selectable::after,
.choices__list[aria-expanded] .choices__item--selectable::after {
  @apply hidden !important;
}

@media (min-width: 640px) {
  .choices__list--dropdown .choices__item--selectable,
  .choices__list[aria-expanded] .choices__item--selectable {
    @apply pe-0 !important;
  }
}
.choices__heading {
  @apply border-b-defaultborder text-textmuted dark:text-textmuted/50 border-b border-solid;
}

[dir=rtl] .choices[data-type*=select-one]::after {
  @apply start-[0.7188rem] end-[inherit] rtl:end-[0.7188rem] rtl:start-[inherit] opacity-50;
}
[dir=rtl] .choices[data-type*=select-one] .choices__button {
  @apply ms-[1.5625rem] me-[inherit] start-0 end-[inherit];
}
[dir=rtl][class=dark] .choices[data-type*=select-one] .choices__button {
  @apply invert-[1];
}
[dir=rtl] .choices[data-type*=select-multiple] .choices__button,
[dir=rtl] .choices[data-type*=text] .choices__button {
  @apply -ms-1 me-2 my-0 ps-[inherit] pe-4 border-e-[#008fa1] border-x-0 border-solid;
}

.choices__list--dropdown,
.choices__list[aria-expanded] {
  @apply shadow-[rgba(149,157,165,0.2)_0_0.5rem_1.5rem] !important;
}

[class=dark] .choices[data-type*=select-one] .choices__button {
  @apply invert-[1];
}

/* End:Choices JS */
/* Start:Apex Charts */
#donut-pattern .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label, #donut-pattern .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  @apply fill-defaulttextcolor !important;
}

.apexcharts-xaxistooltip-text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

#radialbar-multiple .apexcharts-text.apexcharts-datalabel-label {
  @apply dark:fill-white;
}

#radialbar-multiple .apexcharts-radialbar .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  @apply fill-defaulttextcolor;
}

.apexcharts-gridline {
  @apply stroke-defaultborder dark:stroke-defaultborder/10 !important;
}

.apexcharts-xaxis line,
.apexcharts-grid-borders line {
  @apply stroke-defaultborder dark:stroke-defaultborder/10 !important;
}

.apexcharts-title-text {
  @apply fill-textmuted dark:text-textmuted/50;
}

.apexcharts-title-text {
  @apply dark:fill-defaulttextcolor/70;
}

.apexcharts-menu-item {
  @apply text-[11px];
}

.apexcharts-xaxistooltip,
.apexcharts-yaxistooltip {
  @apply text-defaulttextcolor rounded-md bg-white dark:bg-bodybg border border-defaultborder shadow-defaultshadow border-solid;
}

.apexcharts-xaxistooltip-bottom:before {
  @apply border-b-defaultborder;
}

.apexcharts-menu {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.apexcharts-yaxistooltip-left:before {
  @apply border-s-defaultborder;
}

#marketCap .apexcharts-canvas line {
  @apply stroke-defaultborder;
}

.apexcharts-legend {
  @apply px-5 py-0 !important;
}

.apexcharts-tooltip {
  @apply shadow-none !important;
}

.apexcharts-tooltip-marker {
  @apply me-2.5;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply border border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg border-solid !important;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  @apply bg-white dark:bg-bodybg border-b-defaultborder dark:border-b-defaultborder/10 border-b border-solid !important;
}

.apexcharts-xaxis line,
.apexcharts-grid-borders line {
  @apply stroke-defaultborder;
}

.apexcharts-radialbar-track.apexcharts-track path {
  @apply stroke-light;
}

.apexcharts-selection-rect {
  @apply fill-black dark:fill-white;
}

.apexcharts-menu {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 rounded-md border-solid !important;
}

.apexcharts-menu-item {
  @apply p-1.5 !important;
}

.apexcharts-theme-light .apexcharts-menu-item:hover {
  @apply bg-light !important;
}

.apexcharts-inner.apexcharts-graphical line.apexcharts-xaxis-tick {
  @apply stroke-transparent !important;
}

#column-rotated-labels .apexcharts-xaxis-texts-g {
  @apply translate-y-10;
}

#chart-year,
#chart-quarter {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 border-solid !important;
}

.apexcharts-bar-series.apexcharts-plot-series .apexcharts-series .apexcharts-bar-area {
  @apply stroke-transparent !important;
}

.apexcharts-treemap .apexcharts-series.apexcharts-treemap-series rect {
  @apply stroke-customwhite !important;
}

.apexcharts-pie line, .apexcharts-pie circle {
  @apply stroke-transparent !important;
}

.apexcharts-pie line, .apexcharts-pie circle {
  @apply stroke-defaultborder dark:stroke-defaultborder/10 !important;
}

.apexcharts-series.apexcharts-pie-series .apexcharts-pie-area {
  @apply stroke-white dark:stroke-bodybg !important;
}

.apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  @apply fill-defaulttextcolor;
}

.apexcharts-radialbar-hollow {
  @apply fill-white dark:fill-bodybg;
}

.apexcharts-radar-series.apexcharts-plot-series polygon,
.apexcharts-radar-series.apexcharts-plot-series line {
  @apply stroke-defaultborder dark:stroke-defaultborder/10;
}

.apexcharts-pie line,
.apexcharts-pie circle {
  @apply stroke-defaultborder;
}

.apexcharts-pie text {
  @apply fill-white !important;
}

.apexcharts-canvas .apexcharts-toolbar {
  @apply z-[1];
}

.apexcharts-subtitle-text {
  @apply fill-textmuted dark:text-textmuted/50;
}

#polararea-basic .apexcharts-pie text {
  @apply fill-black;
}

.apexcharts-pie .apexcharts-datalabels rect {
  @apply fill-transparent;
}

[dir=rtl] .apexcharts-canvas {
  @apply dir-ltr;
}

.apexcharts-boxPlot-area {
  @apply stroke-defaulttextcolor !important;
}

.apexcharts-gridline {
  @apply stroke-defaultborder;
}

.apexcharts-legend-text {
  @apply font-medium font-defaultfont !important;
}

.echart-charts canvas {
  @apply w-full !important;
}
.echart-charts div:first-child {
  @apply w-full !important;
}

/* End:Apex Charts */
/* Start:Full Calendar */
.fc-event-selected,
.fc-event:focus {
  @apply shadow-none !important;
}

.fc-daygrid-event {
  @apply p-1;
}

.fc-daygrid-event .fc-event-title {
  @apply text-[13px] font-light;
}

.fc-h-event.bg-primary-transparent .fc-event-title,
.fc-h-event.bg-primary-transparent .fc-event-time {
  @apply text-primary;
}
.fc-h-event.bg-secondary-transparent .fc-event-title,
.fc-h-event.bg-secondary-transparent .fc-event-time {
  @apply text-secondary;
}
.fc-h-event.bg-warning-transparent .fc-event-title,
.fc-h-event.bg-warning-transparent .fc-event-time {
  @apply text-warning;
}
.fc-h-event.bg-info-transparent .fc-event-title,
.fc-h-event.bg-info-transparent .fc-event-time {
  @apply text-info;
}
.fc-h-event.bg-success-transparent .fc-event-title,
.fc-h-event.bg-success-transparent .fc-event-time {
  @apply text-success;
}
.fc-h-event.bg-danger-transparent .fc-event-title,
.fc-h-event.bg-danger-transparent .fc-event-time {
  @apply text-danger;
}

.fc-h-event {
  @apply bg-primary/10;
}
.fc-h-event .fc-event-title {
  @apply text-primary;
}

.fc-theme-standard .fc-scrollgrid.fc-scrollgrid-liquid {
  @apply border-t-defaultborder dark:border-defaultborder/10 border-e-0 border-t border-solid !important;
}

.fc .fc-scrollgrid-section-footer > *,
.fc .fc-scrollgrid-section-header > * {
  @apply border-b-0 !important;
}

.fc-daygrid-block-event .fc-event-time,
.fc-daygrid-block-event .fc-event-title {
  @apply px-1 py-0 !important;
}

.fc .fc-button-primary {
  @apply bg-primary border-primary !important;
}

.fc .fc-non-business {
  @apply bg-white dark:bg-bodybg !important;
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
  @apply text-white bg-primary opacity-90 border-primary;
}

.fc .fc-button-primary:focus,
.fc .fc-button-primary:not(:disabled).fc-button-active:focus,
.fc .fc-button-primary:not(:disabled):active:focus {
  @apply shadow-none !important;
}

.fc-theme-standard td,
.fc-theme-standard th {
  @apply border border-defaultborder dark:border-defaultborder/10 border-t-0 border-solid;
}

.fc-list-table td,
.fc-list-table th {
  @apply border-x-0 !important;
}

.fc .fc-daygrid-day.fc-day-today {
  @apply bg-primary/10 !important;
}

.fc-theme-standard .fc-list {
  @apply border border-defaultborder dark:border-defaultborder/10 border-solid !important;
}

.fc .fc-list-event:hover td {
  @apply bg-light !important;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
  @apply shadow-none !important;
}

.fc-theme-standard .fc-list-day-cushion {
  @apply bg-light !important;
}

.fc .fc-list-sticky .fc-list-day > * {
  @apply z-[9];
}

.fc-theme-standard .fc-scrollgrid {
  @apply border border-defaultborder border-solid !important;
}

.fc-theme-bootstrap5 .fc-list,
.fc-theme-bootstrap5 .fc-scrollgrid,
.fc-theme-bootstrap5 td,
.fc-theme-bootstrap5 th {
  @apply border border-defaultborder border-solid !important;
}

@media (max-width: 420px) {
  .fc-scroller.fc-scroller-liquid {
    @apply overflow-scroll !important;
  }
}
@media (max-width: 380px) {
  .fc .fc-daygrid-day-bottom {
    @apply text-[0.75em] px-[3px] py-0 !important;
  }
  .fc .fc-daygrid-more-link {
    @apply z-[99] !important;
  }
}
@media (max-width: 767.98px) {
  .fc .fc-toolbar {
    @apply block !important;
  }
  .fc-toolbar-chunk {
    @apply mt-2;
  }
}
.fc-daygrid-block-event {
  @apply text-white border-0 !important;
}
.fc-daygrid-block-event .fc-list-event-dot {
  @apply border-white !important;
}
.fc-daygrid-block-event:hover {
  @apply text-black !important;
}
.fc-daygrid-block-event:hover .fc-list-event-dot {
  @apply border-black !important;
}

/* End:Full Calendar */
/* Start:Pickers */
[dir=rtl] .flatpickr-input {
  @apply text-start;
}

.flatpickr-calendar {
  @apply bg-white dark:bg-bodybg shadow-defaultshadow border border-defaultborder dark:border-defaultborder/10 text-defaultsize border-solid !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply text-black/90 fill-black/90;
}

.rangeMode .flatpickr-day {
  @apply mt-1 !important;
}

.flatpickr-day.selected.startRange, .flatpickr-day.startRange.startRange, .flatpickr-day.endRange.startRange {
  @apply rounded-[50px_0_0_50px] !important;
}

.flatpickr-day.selected.endRange, .flatpickr-day.startRange.endRange, .flatpickr-day.endRange.endRange {
  @apply rounded-[0_50px_50px_0] !important;
}

.flatpickr-monthDropdown-months,
.numInput {
  @apply text-black !important;
}

.flatpickr-day.today.inRange {
  @apply text-primary !important;
}

.dayContainer {
  @apply p-1 !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  @apply bg-white dark:bg-bodybg text-[0.813rem] !important;
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
  @apply w-5 h-5 fill-primary p-1 !important;
}

.flatpickr-day.inRange {
  @apply shadow-none !important;
}

.flatpickr-calendar.open {
  @apply z-[99] !important;
}

.flatpickr-calendar.hasTime.open {
  @apply z-[999] !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  @apply rounded-md border border-primary/30 border-dashed !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  @apply bg-transparent !important;
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after,
.flatpickr-calendar.arrowBottom:before {
  @apply border-t-textmuted dark:border-t-textmuted/50 !important;
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  @apply border-b-textmuted dark:border-b-textmuted/50 !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  @apply shadow-none !important;
}

.flatpickr-day {
  @apply text-defaulttextcolor font-medium !important;
}
.flatpickr-day.nextMonthDay, .flatpickr-day.prevMonthDay {
  @apply opacity-50 !important;
}

.flatpickr-day.today {
  @apply bg-primary text-white border-primary !important;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  @apply bg-primary text-white border-primary !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply bg-defaultbackground dark:bg-bodybg2 border-defaultborder dark:border-defaultborder/10 !important;
}

.flatpickr-day.today:hover {
  @apply bg-primary text-white border-primary !important;
}

.flatpickr-calendar.hasTime .flatpickr-time {
  @apply border-t-defaultborder dark:border-t-defaultborder/10 border-t border-solid !important;
}

.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
  @apply border-b-defaultborder dark:border-b-defaultborder/10 !important;
}

.flatpickr-calendar.arrowBottom:after,
.flatpickr-calendar.arrowBottom:before {
  @apply border-t-defaultborder dark:border-t-defaultborder/10 !important;
}

.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
  @apply bg-bodybg !important;
}

.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  @apply text-textmuted dark:text-textmuted/50 !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
  @apply start-0 !important;
}

.flatpickr-months,
.flatpickr-weekdays {
  @apply bg-primary/10 !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply text-primary fill-primary pt-[0.313rem] pb-0 px-[0.313rem] top-0 !important;
}

@media (min-width: 420px) {
  .flatpickr-time .flatpickr-am-pm {
    @apply ps-2 pe-[1.875rem] py-0;
  }
}
.flatpickr-weekdays {
  @apply border-b-defaultborder dark:border-b-defaultborder/10 border-b border-solid !important;
}

.numInputWrapper span.arrowUp {
  @apply top-[-0.125rem] !important;
}

.flatpickr-current-month .numInputWrapper {
  @apply w-14 !important;
}

.flatpickr-calendar.hasTime {
  @apply w-auto;
}

[dir=rtl] .flatpickr-months .numInputWrapper span {
  @apply end-0 start-[inherit] !important;
}

@media (max-width: 575.98) {
  .flatpickr-calendar {
    @apply w-[250px] !important;
  }
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
  @apply ms-0 me-9 -mt-px mb-0 !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month input.cur-year {
  @apply text-sm font-medium ps-[5px] pe-[0.5ch] py-0 !important;
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
  @apply stroke-primary;
}

.flatpickr-day {
  @apply rounded-md !important;
}

.numInputWrapper:hover {
  @apply bg-transparent !important;
}

.numInputWrapper span {
  @apply border-0 !important;
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  @apply border-b-primary !important;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply fill-white !important;
}

.numInputWrapper span:hover {
  @apply bg-transparent !important;
}

.numInputWrapper span.arrowUp:after {
  @apply border-b-primary/50 border-b-4 border-x-4 border-x-transparent border-solid top-3/4 !important;
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  @apply border-t-primary !important;
}

.numInputWrapper span.arrowDown:after {
  @apply border-t-primary/50 border-t-4 border-x-4 border-x-transparent border-solid top-[15%] !important;
}

span.flatpickr-weekday {
  @apply text-primary/80 font-bold !important;
}

.flatpickr-months .flatpickr-month {
  @apply text-primary fill-primary !important;
}

.flatpickr-monthDropdown-months,
.numInput {
  @apply text-primary !important;
}

.pcr-app {
  @apply bg-white dark:bg-bodybg !important;
}

.pcr-app .pcr-interaction .pcr-result {
  @apply text-defaulttextcolor bg-bodybg !important;
}

.custom-container button,
.custom-container1 button,
.custom-container2 button {
  @apply hidden;
}

.pcr-app[data-theme=classic] .pcr-selection .pcr-color-preview {
  @apply me-[0.75em] !important;
}

.pcr-app[data-theme=classic] .pcr-selection .pcr-color-chooser,
.pcr-app[data-theme=classic] .pcr-selection .pcr-color-opacity {
  @apply ms-[0.75em] !important;
}

.flatpickr-weekwrapper .flatpickr-weeks {
  @apply shadow-[1px_0_0_defaultborder] !important;
}

/* End:Pickers */
/* Start:noUi Slider */
.noUi-horizontal .noUi-handle {
  @apply w-4 h-4 end-[-0.063rem] top-[-0.375rem] !important;
}

.noUi-handle:after,
.noUi-handle:before {
  @apply h-[0.35rem] w-px start-[0.3rem] top-1 !important;
}

.noUi-handle:after {
  @apply start-[0.45rem] !important;
}

.noUi-horizontal {
  @apply h-[0.35rem] !important;
}

.noUi-vertical {
  @apply w-[0.35rem] !important;
}

.noUi-vertical .noUi-handle {
  @apply w-4 h-4 !important;
}

.noUi-target {
  @apply bg-bodybg border border-defaultborder shadow-defaultshadow border-solid !important;
}

.noUi-handle {
  @apply border border-defaultborder bg-white dark:bg-bodybg shadow-defaultshadow border-solid !important;
}

#result {
  @apply border border-defaultborder border-solid !important;
}

.noUi-handle:after,
.noUi-handle:before {
  @apply bg-black/30 !important;
}

.noUi-marker {
  @apply absolute bg-defaultborder dark:bg-defaultborder/10 !important;
}

.noUi-tooltip {
  @apply border border-defaultborder dark:border-defaultborder/10 rounded-md bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 text-defaultsize leading-none px-3 py-1 border-solid !important;
}

#slider-fit {
  @apply px-4 py-0;
}

.noUi-tooltip {
  @apply bg-defaultborder dark:bg-defaultborder/10;
}

.noUi-connect {
  @apply bg-primary !important;
}

.noUi-vertical .noUi-handle {
  @apply bottom-[-0.275rem] end-[-0.375rem] !important;
}

#primary1-colored-slider .noUi-connect {
  @apply bg-primary/10 !important;
}

#primary2-colored-slider .noUi-connect {
  @apply bg-primary/20 !important;
}

#primary3-colored-slider .noUi-connect {
  @apply bg-primary/30 !important;
}

#secondary-colored-slider .noUi-connect {
  @apply bg-secondary !important;
}

#warning-colored-slider .noUi-connect {
  @apply bg-warning !important;
}

#info-colored-slider .noUi-connect {
  @apply bg-info !important;
}

#success-colored-slider .noUi-connect {
  @apply bg-success !important;
}

#danger-colored-slider .noUi-connect {
  @apply bg-danger !important;
}

#slider-round {
  @apply h-2.5 !important;
}
#slider-round .noUi-handle {
  @apply h-[1.125rem] w-[1.125rem] top-[-0.313rem] end-[-0.563rem] bg-primary border border-customwhite rounded-[50px] border-solid;
}
#slider-round .noUi-handle:before, #slider-round .noUi-handle:after {
  @apply hidden !important;
}

#slider-square {
  @apply rounded-none !important;
}
#slider-square .noUi-handle {
  @apply shadow-none bg-primary h-[1.125rem] w-[1.125rem] top-[-0.45rem] end-[-0.563rem] rounded-none border-0 !important;
}
#slider-square .noUi-handle:before, #slider-square .noUi-handle:after {
  @apply hidden !important;
}

#color1,
#color2,
#color3 {
  @apply inline-block h-[12.5rem] m-2.5 !important;
}

#colorpicker {
  @apply h-60 w-[19.375rem] border border-defaultborder mx-auto my-0 p-2.5 border-solid !important;
}

#result {
  @apply h-[6.25rem] w-[6.25rem] inline-block align-top text-gray5 bg-gray5 border shadow-[0_0_0.625rem] ms-16 me-0 my-[4.25rem] border-solid border-white;
}

#color1 .noUi-connect {
  @apply bg-danger !important;
}

#color2 .noUi-connect {
  @apply bg-secondary !important;
}

#color3 .noUi-connect {
  @apply bg-primary !important;
}

#slider-hide .noUi-tooltip {
  @apply hidden;
}

#slider-hide .noUi-active .noUi-tooltip {
  @apply block;
}

.c-1-color {
  @apply bg-secondary !important;
}

.c-2-color {
  @apply bg-warning !important;
}

.c-3-color {
  @apply bg-info !important;
}

.c-4-color {
  @apply bg-danger !important;
}

#slider-toggle {
  @apply h-[3.125rem];
}

#slider-toggle.off .noUi-handle {
  @apply border-success !important;
}

/* End:noUi Slider */
/* Start::Gallery */
.glightbox {
  @apply overflow-hidden;
}

@media (min-width: 769px) {
  .gslide-image img {
    @apply rounded-md;
  }
  .glightbox-clean .gclose,
  .glightbox-clean .gnext,
  .glightbox-clean .gprev {
    @apply bg-[rgba(255,255,255,0.05)] w-10 h-10 p-[0.75rem];
  }
}
/* End::Gallery */
/* Start::Calendar */
#external-events .fc-event {
  @apply cursor-move text-xs mt-0 mb-[0.4rem] mx-0 px-3 py-1.5 rounded-[0.35rem];
}

#calendar-container {
  @apply relative z-[1];
}

#calendar {
  @apply max-w-[68.75rem] mx-auto my-5;
}

/* End::Calendar */
/* Start::Leaflet Maps */
#map,
#map1,
#map-popup,
#map-custom-icon,
#interactive-map {
  @apply h-[18.75rem] z-10 relative;
}

/* End::Leaflet Maps */
/* Start::Vector Maps */
.jvm-zoom-btn {
  @apply bg-light text-defaulttextcolor border border-defaultborder text-xl border-solid;
}

.jvm-zoom-btn {
  @apply bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 border border-defaultborder dark:border-defaultborder/10 text-[20px] !important;
}

#vector-map,
#marker-map,
#marker-image-map,
#lines-map,
#us-map,
#canada-map,
#spain-map,
#russia-map {
  @apply h-[21.875rem];
}

.jvm-tooltip {
  @apply bg-[#1a1c1e];
}

#vector-map #jvm-regions-group path,
#marker-map #jvm-regions-group path,
#marker-image-map #jvm-regions-group path,
#lines-map #jvm-regions-group path,
#users-map #jvm-regions-group path {
  @apply fill-dark/[0.05] dark:bg-bodybg2;
}

.jvm-zoom-btn {
  @apply flex items-center justify-center w-5 h-5 leading-5 !important;
}

.jvm-zoom-btn.jvm-zoomin {
  @apply top-[5px] !important;
}

#jvm-markers-labels-group text {
  @apply fill-textmuted dark:fill-textmuted/50;
}

#jvm-markers-group image {
  @apply translate-x-[-10px];
}

/* End::Vector Maps */
/* Start::Google Maps */
#google-map,
#google-map-overlay,
#map-layers,
#map-markers,
#streetview-map,
#map-geofencing {
  @apply h-[18.75rem];
}

.google-map-overlay {
  @apply block text-center text-white text-xl leading-[0.875rem] opacity-80 bg-primary rounded-md shadow-[0.125rem_0.125rem_0.625rem_black/30] px-1 py-0 border-[solid] border-primary;
  text-shadow: 0.063rem 0.063rem 0.063rem gray6;
}

.google-overlay_arrow {
  @apply ms-[-1rem] w-0 h-0 absolute start-2/4;
}

.google-overlay_arrow.above {
  @apply bottom-[-0.938rem] border-s-[0.938rem] border-e-[1rem] border-t-[1rem] border-t-[#336699] border-x-transparent border-solid;
}

.google-overlay_arrow.below {
  @apply top-[-0.938rem] border-b-[1rem] border-b-[#336699] border-x-[1rem] border-x-transparent border-solid;
}

/* End::Google Maps */
/* Start::Apex Charts */
.content-wrapper {
  @apply w-full;
}

.apexcharts-svg,
.apexcharts-canvas {
  @apply w-full;
}

.apexcharts-yaxistooltip-text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.apexcharts-xaxistooltip, .apexcharts-yaxistooltip {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.apexcharts-canvas ::-webkit-scrollbar-thumb {
  @apply bg-defaultborder dark:bg-defaultborder/10 !important;
}

#pie-basic .apexcharts-canvas,
#donut-update .apexcharts-canvas,
#pie-monochrome .apexcharts-canvas,
#donut-gradient .apexcharts-canvas,
#donut-pattern .apexcharts-canvas,
#pie-image .apexcharts-canvas,
#polararea-basic .apexcharts-canvas,
#polararea-monochrome .apexcharts-canvas {
  @apply mx-auto my-0;
}

.apexcharts-legend-text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 ms-[-0.625rem] ps-[0.9375rem] !important;
}

.apexcharts-text.apexcharts-yaxis-label tspan,
.apexcharts-text.apexcharts-xaxis-label tspan {
  @apply fill-textmuted dark:fill-textmuted/50;
}

.apexcharts-canvas .apexcharts-series.apexcharts-heatmap-series rect {
  @apply stroke-customwhite;
}

.apexcharts-canvas .apexcharts-series-markers.apexcharts-series-bubble circle {
  @apply stroke-customwhite;
}

.apexcharts-yaxis .apexcharts-text {
  @apply fill-textmuted dark:fill-textmuted/50;
}

/* End::Apex Charts */
/* Start::Chartjs Charts */
.chartjs-chart {
  @apply max-h-[18.75rem];
}

/* Start::Chartjs Charts */
/* Start::Apex Column Charts */
#chart-year,
#chart-quarter {
  @apply w-[96%] max-w-[48%] shadow-none bg-white dark:bg-bodybg dark:border-defaultborder/10 border ps-0 pt-5 border-solid border-[#ddd];
}

#chart-year {
  @apply float-left relative transition-[1s] duration-[ease] ease-[transform] z-[3];
}

#chart-year.chart-quarter-activated {
  @apply translate-x-0 transition-[1s] duration-[ease] ease-[transform];
}

#chart-quarter {
  @apply float-left relative z-[-2] transition-[1s] duration-[ease] ease-[transform];
}

#chart-quarter.active {
  @apply transition-[1.1s] duration-[ease-in-out] ease-[transform] translate-x-0 z-[1];
}

@media screen and (min-width: 480px) {
  #chart-year {
    @apply translate-x-2/4;
  }
  #chart-quarter {
    @apply -translate-x-2/4;
  }
}
/* End::Apex Column Charts */
/* Start::ECharts */
.echart-charts {
  @apply h-80;
}

/* End::ECharts */
/* Start::Simplebar */
.box .card-body.p-0 .simplebar-track {
  @apply end-0;
}

.box .simplebar-track {
  @apply end-[-18px];
}

.simplebar-scrollbar:before {
  @apply bg-gray4 w-1.5 rounded-[0.3rem] end-0;
}

.simplebar-track.simplebar-horizontal {
  @apply hidden;
}

.simplebar-track.simplebar-vertical {
  @apply w-2;
}

/* End::Simplebar */
/* Start::dropzone */
.dropzone {
  @apply border-defaultborder bg-transparent dark:border-defaultborder/10 border-2 border-dashed !important;
}

.dropzone .dz-message .dz-button {
  @apply text-xl text-defaulttextcolor !important;
}

.dropzone .dz-preview:hover {
  @apply z-[8] !important;
}

.dropzone .dz-preview {
  @apply rounded-[1.25rem];
}

.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-progress {
  @apply z-10 !important;
}

.dropzone .dz-preview .dz-details {
  @apply z-[-9] !important;
}

.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
  @apply z-[48] !important;
}

/* End::dropzone */
/* Start::filepond */
.filepond--drop-label {
  @apply text-defaulttextcolor rounded-[0.3rem] !important;
}

.filepond--credits {
  @apply hidden;
}

.filepond--panel-root {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 border-2 border-dashed !important;
}

.filepond--drop-label.filepond--drop-label label {
  @apply text-defaultsize p-[1.5em];
}

.filepond--root {
  @apply mb-0 !important;
}

.filepond--file {
  @apply bg-primary !important;
}

.single-fileupload {
  @apply w-32 h-32 mx-auto my-0 !important;
}

/* End::filepond */
/* Start:: quill editor */
.ql-container.ql-bubble .ql-editor {
  @apply overflow-y-auto !important;
}

.ql-bubble .ql-editor blockquote {
  @apply border-s-bodybg border-s-4 border-solid !important;
}

.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  @apply border border-defaultborder dark:border-defaultborder/10 border-solid !important;
}

.ql-snow .ql-tooltip input[type=text] {
  @apply dark:bg-bodybg2;
}

.ql-snow .ql-picker {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.ql-snow .ql-stroke,
.ql-snow .ql-stroke.ql-fill {
  @apply stroke-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.ql-snow .ql-fill {
  @apply fill-defaulttextcolor !important;
}

.ql-toolbar.ql-snow + .ql-container.ql-snow {
  @apply border-t-0 !important;
}

.ql-snow .ql-picker-options .ql-picker-item {
  @apply py-0 !important;
}

.ql-editor {
  @apply min-h-[15.62rem] overflow-visible !important;
}

.ql-snow .ql-formats {
  @apply border border-defaultborder dark:border-defaultborder/10 rounded-lg border-solid;
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  @apply border-defaultborder dark:border-defaultborder/10 rounded-lg !important;
}

.ql-snow .ql-picker-options {
  @apply bg-white dark:bg-bodybg !important;
}

.ql-snow .ql-tooltip {
  @apply bg-white dark:bg-bodybg border border-defaultborder shadow-defaultshadow text-defaulttextcolor border-solid !important;
}

.ql-snow .ql-tooltip input[type=text] {
  @apply border border-defaultborder bg-bodybg text-defaulttextcolor border-solid outline-0 !important;
}

.ql-snow .ql-tooltip {
  @apply z-[100] translate-x-[12.5rem] !important;
}

.ql-toolbar.ql-snow {
  @apply rounded-[0.3rem_0.3rem_0_0] !important;
}

.ql-snow .ql-picker-label {
  @apply ps-2 pe-0.5 !important;
}

.ql-snow .ql-formats .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  @apply start-auto end-0 !important;
}

.ql-container {
  @apply rounded-[0_0_0.3rem_0.3rem] !important;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  @apply text-defaulttextcolor !important;
}

.ql-editor {
  @apply text-start !important;
}
.ql-editor .ql-align-right {
  @apply text-end !important;
}

.ql-container {
  @apply font-defaultfont !important;
}

.ql-snow .ql-editor {
  @apply p-5;
}

.ql-bubble {
  @apply border border-defaultborder rounded-md border-solid dark:border-defaultborder/10 !important;
}

.ql-editor li:not(.ql-direction-rtl)::before {
  @apply ms-[-1.5em] text-start me-[0.3em] !important;
}

.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  @apply ps-[1.5em] !important;
}

.ql-toolbar.ql-snow .ql-formats {
  @apply m-1;
}

[dir=rtl] .ql-tooltip.ql-editing {
  @apply left-0 top-0 !important;
}

[dir=rtl] .ql-bubble .ql-toolbar .ql-formats:first-child {
  @apply me-3;
}

[dir=rtl] .ql-bubble .ql-toolbar .ql-formats {
  @apply ms-3 me-0 my-2;
}

/* end:: quill editor */
/* Start:: select2 */
[dir=rtl] .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  @apply left-[inherit] -right-[3px] !important;
}
[dir=rtl] .select2-container--default .select2-selection--multiple .select2-selection__choice__display {
  @apply ps-[inherit] pe-[5px] !important;
}

.select2-selection__rendered span, .select2-results__option span {
  @apply mb-[2px];
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  @apply px-1 py-0.5 !important;
}

.select2-dropdown {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
  @apply ps-[5px] !important;
}

.select2.select2-container {
  @apply w-full !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  @apply text-defaulttextcolor leading-[2.33rem] border border-inputborder dark:border-defaultborder/10 rounded-md ps-3 pe-5 border-solid !important;
}

.select2-container--default .select2-selection--single {
  @apply bg-formcontrolbg border-inputborder rounded-md border-0 border-solid !important;
}

.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__arrow {
  @apply h-[2.37rem] !important;
}

.select2-dropdown {
  @apply bg-white dark:bg-bodybg border border-inputborder rounded-md border-solid !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  @apply border border-inputborder rounded-md border-solid !important;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  @apply bg-bodybg text-defaulttextcolor !important;
}

.select2-results__option--selectable {
  @apply text-[0.813rem];
}

.select2-container--default .select2-results__option--selected {
  @apply bg-primary text-white !important;
}
.select2-container--default .select2-results__option--selected.select2-results__option--highlighted {
  @apply bg-primary text-white !important;
}

.select2-search__field {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor !important;
}
.select2-search__field:focus-visible {
  @apply outline-0 !important;
}

.select2-container--default .select2-selection--multiple {
  @apply bg-formcontrolbg border border-inputborder dark:border-defaultborder/10 rounded-md border-solid !important;
}

.select2-container .select2-selection--multiple {
  @apply min-h-[2.25rem] !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  @apply bg-primary text-white border rounded-sm mt-1.5 px-[18px] py-0 border-solid border-primary !important;
}

.select2-selection--multiple .select2-search__field {
  @apply bg-transparent !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  @apply text-white top-[-0.5rem] -start-[1px] font-medium text-lg border-e-[rgba(255,255,255,0.1)] border-e border-solid !important;
}

.select2-selection--multiple .select2-selection__choice__display {
  @apply text-xs !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:focus {
  @apply bg-primary !important;
}

.select2-results__option span img,
.select2-selection__rendered span img {
  @apply w-[1.45rem] h-[1.45rem] shadow-defaultshadow me-1 rounded-[1.25rem];
}

.select2-container .select2-search--inline .select2-search__field {
  @apply mt-2 !important;
}

.select2-container--disabled.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--disabled .select2-selection.select2-selection--multiple {
  @apply bg-bodybg !important;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
  @apply font-normal h-5 text-[1.5625rem] w-5 absolute end-2.5 !important;
}

.select2-selection__clear {
  @apply text-textmuted dark:text-textmuted/50;
}

.select2-dropdown {
  @apply z-10 !important;
}

[dir=rtl] .select2-container--default .select2-selection--single .select2-selection__clear {
  @apply ms-5 ps-0 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  @apply end-2.5 !important;
}

.select2-container--default[dir=rtl] .select2-selection--single .select2-selection__arrow {
  @apply start-2.5 end-auto;
}

.select2-container--open .select2-dropdown--above {
  @apply overflow-hidden rounded-br-none rounded-bl-none;
}

.select2-container--open .select2-dropdown--below {
  @apply overflow-hidden rounded-t-none;
}

/* End:: select2 */
/* Start:: grid js tables */
.gridjs-table {
  @apply w-full;
}

table.gridjs-table {
  @apply text-start text-[0.813rem] font-medium !important;
}

.gridjs-wrapper {
  @apply shadow-none rounded-none !important;
}
.gridjs-wrapper:nth-last-of-type(2) {
  @apply rounded-none !important;
}

.gridjs-container {
  @apply text-defaulttextcolor !important;
}

th.gridjs-th {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor/80 text-defaulttextcolor p-3 border-solid text-start !important;
}

.gridjs-sort-neutral {
  @apply dark:invert-[1];
}

td.gridjs-td {
  @apply border border-defaultborder dark:border-defaultborder/10 dark:text-defaulttextcolor/80 p-3 border-solid !important;
}

th.gridjs-th .gridjs-th-content {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.gridjs-tbody,
td.gridjs-td {
  @apply bg-white dark:bg-bodybg !important;
}

.gridjs-footer {
  @apply bg-white dark:bg-bodybg shadow-none pt-3 pb-0 px-0 rounded-none border-y-0 border-transparent !important;
}

.gridjs-pagination {
  @apply text-defaulttextcolor !important;
}

.gridjs-pagination .gridjs-pages button:first-child {
  @apply rounded-bl-md rounded-tl-md rounded-tr-none rounded-br-none !important;
}

.gridjs-pagination .gridjs-pages button:last-child {
  @apply rounded-br-md rounded-tr-md rounded-tl-none rounded-bl-none !important;
}

.gridjs-footer {
  @apply px-0 !important;
}

@media (max-width: 575.98px) {
  .gridjs-search-input {
    @apply w-[12.5rem] !important;
  }
}
[dir=rtl] .gridjs-pagination .gridjs-pages button:first-child {
  @apply rounded-br-md rounded-tr-md rounded-tl-none rounded-bl-none !important;
}
[dir=rtl] .gridjs-pagination .gridjs-pages button:last-child {
  @apply rounded-bl-md rounded-tl-md rounded-tr-none rounded-br-none !important;
}

.gridjs-pagination .gridjs-pages button:disabled,
.gridjs-pagination .gridjs-pages button:hover:disabled,
.gridjs-pagination .gridjs-pages button[disabled] {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor !important;
}

.gridjs-pagination .gridjs-pages button {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor px-3 py-[0.375] border-solid focus:shadow-none focus:me-0 !important;
}

.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
  @apply font-semibold bg-primary text-white !important;
}

.gridjs-pagination .gridjs-pages {
  @apply float-end !important;
}

input.gridjs-input {
  @apply bg-white dark:bg-bodybg border border-defaultborder rounded-md text-defaultsize leading-[1.6] text-defaulttextcolor px-3 py-1.5 border-solid focus:shadow-none focus:border focus:border-solid focus:border-primary !important;
}

button.gridjs-sort {
  @apply h-5 w-2.5 float-end !important;
}

th.gridjs-th-fixed {
  @apply bg-light !important;
}

#grid-header-fixed .gridjs-wrapper {
  @apply border-t-defaultborder border-b-defaultborder dark:border-defaultborder/10 border-t border-solid border-b !important;
}
#grid-header-fixed .gridjs-container .gridjs-wrapper .gridjs-thead .gridjs-tr th {
  @apply -top-px !important;
}

/* End:: grid js tables */
/* Start:: bootstrap5 datatables */
.dataTables_wrapper .dataTables_paginate {
  @apply text-end m-0;
}

.dataTables_wrapper .dataTables_paginate .pagination {
  @apply mb-0 justify-end;
}

div.dataTables_scrollBody > table#datatable-basic {
  @apply mb-1.5 !important;
}

.dataTables_filter {
  @apply text-end !important;
}

.dataTables_info {
  @apply pt-2.5;
}

table.dataTable > thead .sorting:before,
table.dataTable > thead .sorting_asc:before,
table.dataTable > thead .sorting_asc_disabled:before,
table.dataTable > thead .sorting_desc:before,
table.dataTable > thead .sorting_desc_disabled:before {
  @apply content-[""] absolute text-[0.5rem] end-[0.8rem] top-[0.813rem] font-bootstrap !important;
}

table.dataTable > thead .sorting:after,
table.dataTable > thead .sorting_asc:after,
table.dataTable > thead .sorting_asc_disabled:after,
table.dataTable > thead .sorting_desc:after,
table.dataTable > thead .sorting_desc_disabled:after {
  @apply content-[""] absolute text-[0.5rem] end-[0.8rem] top-5 font-bootstrap !important;
}

table.dataTable > thead .sorting,
table.dataTable > thead .sorting_asc,
table.dataTable > thead .sorting_desc,
table.dataTable > thead .sorting_asc_disabled,
table.dataTable > thead .sorting_desc_disabled {
  @apply cursor-pointer relative;
}

table.dataTable > thead .sorting:before,
table.dataTable > thead .sorting:after,
table.dataTable > thead .sorting_asc:before,
table.dataTable > thead .sorting_asc:after,
table.dataTable > thead .sorting_desc:before,
table.dataTable > thead .sorting_desc:after,
table.dataTable > thead .sorting_asc_disabled:before,
table.dataTable > thead .sorting_asc_disabled:after,
table.dataTable > thead .sorting_desc_disabled:before,
table.dataTable > thead .sorting_desc_disabled:after {
  @apply absolute block opacity-20;
}

[dir=rtl] table.dataTable thead > tr > th.sorting:before, [dir=rtl] table.dataTable thead > tr > th.sorting:after, [dir=rtl] table.dataTable thead > tr > th.sorting_asc:before, [dir=rtl] table.dataTable thead > tr > th.sorting_asc:after, [dir=rtl] table.dataTable thead > tr > th.sorting_desc:before, [dir=rtl] table.dataTable thead > tr > th.sorting_desc:after, [dir=rtl] table.dataTable thead > tr > th.sorting_asc_disabled:before, [dir=rtl] table.dataTable thead > tr > th.sorting_asc_disabled:after, [dir=rtl] table.dataTable thead > tr > th.sorting_desc_disabled:before, [dir=rtl] table.dataTable thead > tr > th.sorting_desc_disabled:after, [dir=rtl] table.dataTable thead > tr > td.sorting:before, [dir=rtl] table.dataTable thead > tr > td.sorting:after, [dir=rtl] table.dataTable thead > tr > td.sorting_asc:before, [dir=rtl] table.dataTable thead > tr > td.sorting_asc:after, [dir=rtl] table.dataTable thead > tr > td.sorting_desc:before, [dir=rtl] table.dataTable thead > tr > td.sorting_desc:after, [dir=rtl] table.dataTable thead > tr > td.sorting_asc_disabled:before, [dir=rtl] table.dataTable thead > tr > td.sorting_asc_disabled:after, [dir=rtl] table.dataTable thead > tr > td.sorting_desc_disabled:before, [dir=rtl] table.dataTable thead > tr > td.sorting_desc_disabled:after {
  @apply start-2.5 end-auto;
}

table.dataTable > thead .sorting_asc:before,
table.dataTable > thead .sorting_desc:after {
  @apply opacity-80;
}

div.dataTables_wrapper div.dataTables_length select {
  @apply w-auto inline-block mx-1 my-0;
}

.dataTables_wrapper .dataTables_scrollHead table.dataTable {
  @apply mb-0;
}
.dataTables_wrapper .dataTables_scrollBody #datatable-basic {
  @apply mt-[-3px] border-t-transparent;
}
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting:before,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting:after,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_asc:before,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_asc:after,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_desc:before,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_desc:after,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_asc_disabled:before,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_asc_disabled:after,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_desc_disabled:before,
.dataTables_wrapper .dataTables_scrollBody table.dataTable > thead .sorting_desc_disabled:after {
  @apply hidden;
}

div.dt-button-info {
  @apply bg-white dark:bg-bodybg border border-defaultborder border-solid !important;
}
div.dt-button-info h2 {
  @apply bg-white dark:bg-bodybg border-b-defaultborder border-b border-solid !important;
}

@media (max-width: 767.98px) {
  .data-table-btn {
    @apply mt-[0.5625rem] mb-2 mx-0 !important;
  }
  .dataTables_length,
  .dataTables_filter,
  .dataTables_info {
    @apply text-center;
  }
  .dataTables_filter {
    @apply mt-2;
  }
  .dataTables_paginate .pagination {
    @apply justify-center !important;
  }
  .dataTables_info {
    @apply pt-0 pb-2;
  }
  div.dtr-modal div.dtr-modal-display {
    @apply w-[95%] h-[95%] bg-white dark:bg-bodybg border border-defaultborder shadow-defaultshadow border-solid !important;
  }
  div.dataTables_wrapper div.dataTables_length,
  div.dataTables_wrapper div.dataTables_filter,
  div.dataTables_wrapper div.dataTables_info,
  div.dataTables_wrapper div.dataTables_paginate {
    @apply text-center !important;
  }
}
@media (max-width: 575.98px) {
  .dataTables_paginate .pagination .paginate_button .page-link {
    @apply text-[0.625rem] px-2 py-1;
  }
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control {
  @apply ps-[1.875rem] !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  @apply text-white shadow-[0_0_0.2em_primary] bg-primary border-[0.15em] border-solid border-white !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  @apply content-["-"] bg-success !important;
}

table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  @apply pt-0 !important;
}

table.dataTable > tbody > tr.child ul.dtr-details > li {
  @apply border-b-defaultborder border-b border-solid !important;
}

table.dataTable > tbody > tr.child span.dtr-title {
  @apply min-w-[4.688rem] !important;
}

div.dtr-modal div.dtr-modal-close {
  @apply border-defaultborder text-2xl bg-transparent border-0 border-solid top-0 !important;
}

div.dtr-modal div.dtr-modal-background {
  @apply bg-[rgba(0,0,0,0.3)] !important;
}

.dtr-modal-content h2 {
  @apply text-sm font-semibold !important;
}

.dt-button {
  @apply text-[0.8125rem] shadow-none font-medium bg-primary text-white px-3 py-1.5 rounded-[0.3rem] border-0 !important;
}

.dt-buttons {
  @apply float-left !important;
}

table.dataTable thead > tr > th.sorting,
table.dataTable thead > tr > th.sorting_asc,
table.dataTable thead > tr > th.sorting_desc,
table.dataTable thead > tr > th.sorting_asc_disabled,
table.dataTable thead > tr > th.sorting_desc_disabled,
table.dataTable thead > tr > td.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting_asc_disabled,
table.dataTable thead > tr > td.sorting_desc_disabled {
  @apply pe-[1.625rem];
}

table.dataTable thead th,
table.dataTable thead td,
table.dataTable tfoot th,
table.dataTable tfoot td {
  @apply text-start !important;
}

table.table-bordered.dataTable th:first-child,
table.table-bordered.dataTable th:first-child,
table.table-bordered.dataTable td:first-child,
table.table-bordered.dataTable td:first-child {
  @apply border-s !important;
}

table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
  @apply border-e !important;
}

div.dataTables_wrapper div.dataTables_filter#scroll-vertical_filter input {
  @apply me-[0.2em];
}

[dir=rtl] div.dtr-modal div.dtr-modal-close {
  @apply start-1.5 end-auto;
}
[dir=rtl] div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {
  @apply ps-0 pe-[initial];
}
[dir=rtl] div.dataTables_wrapper div.dataTables_filter#scroll-vertical_filter input {
  @apply ms-[0.2em] me-2;
}
[dir=rtl] div.dataTables_wrapper div.dataTables_filter input {
  @apply ms-0 me-2;
}
[dir=rtl] table.table-bordered.dataTable th:first-child,
[dir=rtl] table.table-bordered.dataTable th:first-child,
[dir=rtl] table.table-bordered.dataTable td:first-child,
[dir=rtl] table.table-bordered.dataTable td:first-child {
  @apply border-s-0 border-e;
}
[dir=rtl] table.table-bordered.dataTable th:last-child,
[dir=rtl] table.table-bordered.dataTable th:last-child,
[dir=rtl] table.table-bordered.dataTable td:last-child,
[dir=rtl] table.table-bordered.dataTable td:last-child {
  @apply border-s;
}
[dir=rtl] div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {
  @apply pe-0;
}
[dir=rtl] div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {
  @apply ps-0;
}
[dir=rtl] .dt-buttons {
  @apply float-right !important;
}

table.dataTable > tbody > tr.selected > * {
  @apply bg-primary/10 text-defaulttextcolor shadow-none;
}

@media (min-width: 768px) {
  .data-table-btn {
    @apply absolute z-[1] start-[12.5rem];
  }
}
#file-export_wrapper .dt-buttons button {
  @apply m-1;
}

.data-table-btn {
  @apply mt-[-0.4375rem] mb-0 mx-0;
}

/* End:: bootstrap5 datatables */
/* Start:: sweet alerts */
div:where(.swal2-container) input:where(.swal2-input), div:where(.swal2-container) input:where(.swal2-file), div:where(.swal2-container) textarea:where(.swal2-textarea) {
  @apply border border-defaultborder border-solid !important;
}

.swal2-container .swal2-title {
  @apply text-[1.15rem] pt-8 pb-2 px-8;
}
.swal2-container .swal2-footer {
  @apply pt-6 pb-0 px-6;
}
.swal2-container .swal2-popup {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor pt-0 pb-8 px-0;
}
.swal2-container .swal2-center > .swal2-popup {
  @apply bg-cover bg-center bg-no-repeat;
}
.swal2-container .swal2-actions {
  @apply mt-4 mb-0 mx-auto;
}
.swal2-container .swal2-styled.swal2-confirm {
  @apply shadow-none text-[0.8125rem] m-0 px-3 py-[0.375em] rounded-[0.3rem] bg-primary text-white !important;
}
.swal2-container .swal2-confirm {
  @apply shadow-none text-[0.8125rem] m-0 px-3 py-[0.375em] rounded-[0.3rem] bg-primary text-white !important;
}
.swal2-container .swal2-styled.swal2-confirm:focus,
.swal2-container .swal2-styled.swal2-cancel:focus,
.swal2-container .swal2-styled.swal2-deny:focus {
  @apply shadow-none;
}
.swal2-container .swal2-footer {
  @apply border-t-defaultborder border-t dark:border-defaultborder/10 border-solid;
}
.swal2-container .swal2-icon {
  @apply w-16 h-16 mt-8 mb-0 mx-auto;
}
.swal2-container .swal2-icon.swal2-question {
  @apply text-success border-success;
}
.swal2-container .swal2-icon.swal2-error {
  @apply text-danger border-danger;
}
.swal2-container .swal2-icon.swal2-info {
  @apply text-info border-info;
}
.swal2-container .swal2-icon.swal2-warning {
  @apply text-warning border-warning;
}
.swal2-container .swal2-icon .swal2-icon-content {
  @apply text-5xl;
}
.swal2-container .swal2-image {
  @apply rounded-md;
}
.swal2-container .swal2-html-container {
  @apply text-[0.8rem] text-textmuted dark:text-textmuted/50 mt-0 mb-[0.3rem] mx-[1.6rem];
}
.swal2-container .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
  @apply start-[1em];
}
.swal2-container .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  @apply w-[2em] h-[0.3em] bg-danger top-[1.9em];
}
.swal2-container .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
  @apply end-[1em] top-[1.875rem];
}
.swal2-container .swal2-close:focus {
  @apply shadow-none;
}
.swal2-container .swal2-deny,
.swal2-container .swal2-cancel {
  @apply ms-2.5;
}
.swal2-container .swal2-close {
  @apply text-[2rem] text-textmuted dark:text-textmuted/50;
}
.swal2-container .swal2-close:hover {
  @apply text-primary;
}
.swal2-container .swal2-styled.swal2-deny {
  @apply bg-danger shadow-none text-[0.8125rem] px-3 py-[0.375em] rounded-[0.3rem];
}
.swal2-container .swal2-styled.swal2-cancel {
  @apply bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 shadow-none text-[0.8125rem] px-3 py-[0.375em] rounded-[0.3rem] !important;
}
.swal2-container .swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {
  @apply w-[2em] end-[0.45em] top-[2.05em];
}
.swal2-container .swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
  @apply w-[1.2625em] start-[0.9125em] top-[2.375em];
}
.swal2-container .swal2-file:focus,
.swal2-container .swal2-input:focus,
.swal2-container .swal2-textarea:focus {
  @apply border border-defaultborder shadow-none border-solid;
}

[dir=rtl] .swal2-container .swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
  @apply start-[1.9125em];
}

/* End:: sweet alerts */
/* Start:: swiper js */
.swiper {
  @apply rounded-md;
}

[dir=rtl] .swiper {
  @apply dir-ltr;
}
[dir=rtl] .swiper-backface-hidden .swiper-slide {
  @apply dir-rtl;
}

.swiper-slide img {
  @apply block w-full h-full object-cover;
}

.swiper-button-next,
.swiper-button-prev {
  @apply w-[1.563rem] h-[1.563rem] text-white bg-[rgba(255,255,255,0.3)] rounded-md !important;
}

.swiper-horizontal1 .swiper-slide {
  @apply h-auto;
}

.swiper-pagination-bullet {
  @apply w-5 h-1 rounded-md bg-white !important;
}

.swiper-pagination-bullet-active {
  @apply bg-white !important;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  @apply bg-success !important;
}

.swiper-pagination {
  @apply text-white;
}
.swiper-pagination.swiper-pagination-fraction {
  @apply text-white;
}

.custom-pagination .swiper-pagination-bullet {
  @apply w-6 h-6 bg-[rgba(255,255,255,0.3)] text-white opacity-100 p-[0.188rem] !important;
}
.custom-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  @apply bg-white text-black !important;
}

.swiper-scrollbar {
  @apply bg-[rgba(255,255,255,0.3)];
}

.swiper-scrollbar-drag {
  @apply bg-white;
}

.swiper.vertical {
  @apply h-[21.875rem];
}

.swiper-preview {
  @apply h-4/5 w-full;
}

.swiper-view {
  @apply h-1/5 box-border px-0 py-2.5;
}

.swiper-view .swiper-slide {
  @apply w-3/12 h-full opacity-40;
}

.swiper-view .swiper-slide-thumb-active {
  @apply opacity-100;
}

.swiper-preview .swiper-wrapper {
  @apply mb-[0.65rem];
}

.swiper-thumbs .swiper-slide img {
  @apply rounded-md;
}

/* End:: swiper js */
/* Start:: prism js */
pre[class*=language-]:after,
pre[class*=language-]:before {
  @apply hidden !important;
}

pre[class*=language-] > code {
  @apply dark:border-defaultborder/10 shadow-none bg-light border border-defaultborder ps-4 p-0 rounded-md bg-none whitespace-pre-wrap border-s border-solid !important;
}

:not(pre) > code[class*=language-],
pre[class*=language-] {
  @apply bg-light max-h-[400px] overflow-y-hidden rounded-md mb-0 ps-4 p-0 border border-defaultborder dark:border-defaultborder/10 !important;
}

code[class*=language-],
pre[class*=language-] {
  @apply text-defaulttextcolor text-[0.82rem] !important;
}

pre[class*=language-] {
  @apply m-0 !important;
}

code[class*=language-] {
  @apply p-4 !important;
}

.prism-toggle {
  @apply m-1;
}

[dir=rtl] pre[class*=language-] > code {
  @apply text-right border-s-0;
}

/* End:: prism js */
/* Start:: Draggable Cards */
#draggable-left .card,
#draggable-right .card {
  @apply touch-none;
}

/* End:: Draggable Cards */
/* Start:: Rater Js */
.star-rating {
  @apply touch-none;
}

.star-rating .star-value {
  @apply touch-none;
}

/* End:: Rater Js */
/* Start:: Emoji Picker */
.fg-emoji-picker {
  @apply top-auto bottom-[120px] !important;
}

@media (max-width: 420px) {
  .fg-emoji-picker {
    @apply start-[13px] bottom-[136px] !important;
  }
}
.fg-emoji-picker-search input {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor border-b-defaultborder p-2.5 border-b border-solid !important;
}

.fg-emoji-picker-grid > li {
  @apply flex-[0_0_calc(100%_/_7)] h-[38px] !important;
}

.fg-emoji-picker-grid > li:hover {
  @apply bg-light rounded-md !important;
}

.fg-emoji-picker .fg-emoji-picker-all-categories {
  @apply h-72 !important;
}

.fg-emoji-picker * {
  @apply text-defaulttextcolor fill-defaulttextcolor font-defaultfont !important;
}

.fg-emoji-picker-categories li.active {
  @apply bg-primary/10 !important;
}

.fg-emoji-picker-categories {
  @apply bg-white dark:bg-bodybg !important;
}

.fg-emoji-picker-categories ul {
  @apply border-b-defaultborder border-b border-solid dark:border-defaultborder/10 !important;
}

a.fg-emoji-picker-close-button {
  @apply bg-light !important;
}

.fg-emoji-picker-search svg {
  @apply w-[45px] h-[39px] border-s-defaultborder fill-defaulttextcolor border-b-defaulttextcolor p-2.5 border-s border-solid border-b dark:border-defaultborder/10 end-0 top-0 !important;
}

.fg-emoji-picker {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 border-solid !important;
}

.fg-emoji-picker-grid > li {
  @apply bg-white dark:bg-bodybg !important;
}

.fg-emoji-picker-categories a:hover {
  @apply bg-primary/10 !important;
}

.fg-emoji-picker .fg-emoji-picker-category {
  @apply pt-0 !important;
}

.fg-emoji-picker-search {
  @apply h-[38px] !important;
}

/* End:: Emoji Picker */
/* Start:: Plyr */
.plyr__control--overlaid {
  @apply bg-primary !important;
}

.audio-control .plyr__controls__item svg {
  @apply fill-defaulttextcolor opacity-50 !important;
}

.plyr__controls__item.plyr__time--current {
  @apply text-defaulttextcolor opacity-50 !important;
}

.plyr--full-ui input[type=range] {
  @apply text-primary !important;
}

.plyr--video .plyr__control:focus-visible,
.plyr--video .plyr__control:hover,
.plyr--video .plyr__control[aria-expanded=true],
.plyr--audio .plyr__control:focus-visible,
.plyr--audio .plyr__control:hover,
.plyr--audio .plyr__control[aria-expanded=true],
.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]:before {
  @apply bg-primary text-white !important;
}

.plyr--audio .plyr__controls {
  @apply bg-white dark:bg-bodybg !important;
}

/* End:: Plyr */
/* Start:: Shepherd JS */
.shepherd-modal-overlay-container.shepherd-modal-is-visible {
  @apply opacity-[0.15] !important;
}

.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
  @apply text-black !important;
}

.shepherd-button {
  @apply bg-success text-white px-4 py-2 !important;
}

.shepherd-element {
  @apply bg-white dark:bg-bodybg !important;
}

.shepherd-header {
  @apply bg-light p-2 !important;
}

.shepherd-title {
  @apply font-medium !important;
}

.shepherd-text {
  @apply text-[13px] !important;
}

.shepherd-title,
.shepherd-text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.shepherd-element.shepherd-has-title[data-popper-placement^=bottom] > .shepherd-arrow:before {
  @apply bg-light border border-inputborder dark:border-defaultborder/10 border-solid !important;
}

.shepherd-arrow:before {
  @apply bg-white dark:bg-bodybg !important;
}

/* End:: Shepherd JS */
/* Start:: Auto Complete */
.autoComplete_wrapper > ul {
  @apply z-10 rtl:dir-ltr !important;
}

.autoComplete_wrapper > input {
  @apply focus:shadow-none !important;
}

.autoComplete_wrapper > input {
  @apply border-inputborder dark:border-defaultborder/10 text-defaulttextcolor bg-formcontrolbg text-sm font-normal leading-normal bg-none h-[inherit] px-[0.85rem] py-2 rounded-[0.35rem] !important;
}
.autoComplete_wrapper > input:focus {
  @apply border-primary/50 shadow-[0_0_4px_primary/50] !important;
}
.autoComplete_wrapper > input:focus::placeholder {
  @apply p-0 !important;
}
.autoComplete_wrapper > input::placeholder {
  @apply text-[0.8rem] font-normal opacity-60 text-defaulttextcolor;
}
.autoComplete_wrapper > input::placeholder:focus {
  @apply text-[0.8rem] p-0 !important;
}

.autoComplete_wrapper > input {
  @apply focus:shadow-none !important;
}

.autoComplete_wrapper > ul {
  @apply shadow-defaultshadow bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 mt-0 border-solid !important;
}
.autoComplete_wrapper > ul .no_result {
  @apply p-2 !important;
}
.autoComplete_wrapper > ul > li {
  @apply text-[0.85rem] bg-white dark:bg-bodybg text-defaulttextcolor !important;
}
.autoComplete_wrapper > ul > li:hover {
  @apply bg-primary/10 text-defaulttextcolor !important;
}
.autoComplete_wrapper > ul > li mark {
  @apply bg-transparent text-orangemain font-[bold] p-0 !important;
}

#autoComplete_list_2 p {
  @apply mb-0 p-2 !important;
}

.autoComplete_wrapper > input {
  @apply w-auto !important;
}

/* End:: Auto Complete */
/* Start:: intl-tel-input */
@media (min-width: 367px) {
  .telephone-input-btn {
    @apply ms-2;
  }
}
@media (max-width: 366.98px) {
  .telephone-input-btn {
    @apply mt-2;
  }
}
.hide {
  @apply hidden;
}

input.error {
  @apply border border-solid border-danger;
}

#error-msg {
  @apply text-danger;
}

#valid-msg {
  @apply text-success;
}

.iti__search-input:focus-visible {
  outline: none;
}

[dir=rtl] .selected-dial-code-input .iti--allow-dropdown input.iti__tel-input, [dir=rtl] .selected-dial-code-input .iti--allow-dropdown input.iti__tel-input[type=tel], [dir=rtl] .selected-dial-code-input .iti--allow-dropdown input.iti__tel-input[type=text], [dir=rtl] .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input, [dir=rtl] .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input[type=tel], [dir=rtl] .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input[type=text] {
  @apply ps-1.5 pe-[71px] !important;
}

.selected-dial-code-input .iti--allow-dropdown input.iti__tel-input, .selected-dial-code-input .iti--allow-dropdown input.iti__tel-input[type=tel], .selected-dial-code-input .iti--allow-dropdown input.iti__tel-input[type=text], .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input, .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input[type=tel], .selected-dial-code-input .iti--show-selected-dial-code input.iti__tel-input[type=text] {
  @apply ps-[71px] pe-1.5 !important;
}

.iti--allow-dropdown input.iti__tel-input, .iti--allow-dropdown input.iti__tel-input[type=text], .iti--allow-dropdown input.iti__tel-input[type=tel], .iti--show-selected-dial-code input.iti__tel-input, .iti--show-selected-dial-code input.iti__tel-input[type=text], .iti--show-selected-dial-code input.iti__tel-input[type=tel] {
  @apply pe-[6px] ps-[52px] rtl:ps-[6px] rtl:pe-[52px] ml-0 !important;
}

.iti__search-input {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.iti__tel-input::placeholder {
  @apply rtl:text-end !important;
}

.iti__search-input + .iti__country-list {
  @apply border-t-defaultborder border-t border-solid border-defaultborder dark:border-defaultborder/10 !important;
}

.iti--inline-dropdown .iti__dropdown-content {
  @apply border border-defaultborder shadow-defaultshadow border-solid dark:border-defaultborder/10 !important;
}

.iti__dropdown-content {
  @apply bg-white dark:bg-bodybg !important;
}

.iti--show-selected-dial-code .iti__tel-input {
  @apply ps-[71px] pe-[6px] !important;
}

.iti__country.iti__highlight {
  @apply bg-primary/10 !important;
}

.iti--show-selected-dial-code .iti__selected-flag {
  @apply bg-light !important;
}

/* End:: intl-tel-input */
/* Start:: Tagify JS */
.choices__heading {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.tagify {
  --tag-inset-shadow-size: 0 !important;
}

.tagify__tag {
  @apply bg-light;
}

.tagify.tagify--mix.form-control {
  @apply p-2;
}

.tagify__tag > div::before {
  @apply shadow-[0_0_0_var(--tag-inset-shadow-size)_var(--gray-3)_inset] !important;
}

.tagify:hover:not(.tagify--focus):not(.tagify--invalid) {
  @apply border-inputborder dark:border-defaultborder/10 !important;
}

.tagify--empty .tagify__input::before {
  @apply static contents !important;
}

.tagify {
  @apply border-inputborder dark:border-defaultborder/10 !important;
}
.tagify.tagify--focus {
  @apply border-primary/50 shadow-[0_0_4px_primary/50];
}

.tagify__tag {
  @apply ms-[7px] me-0 my-[7px];
}

.tagify__tag > div {
  @apply text-[13px];
}

.tagify__input {
  @apply leading-[1.85rem] m-[3px] border-inputborder dark:border-defaultborder/10 !important;
}

.tagify:hover:not(.tagify--focus):not(.tagify--invalid) {
  @apply border-inputborder dark:border-defaultborder/10 !important;
}

.tagify {
  --tags-disabled-bg: var(--gray-3) !important;
  --tag-bg: var(--gray-3) !important;
  --tag-hover: rgba(var(--primary-rgb), 0.15) !important;
  --tag-text-color: var(--default-text-color) !important;
  --tag-text-color--edit: var(--default-text-color) !important;
  --tag-invalid-color: rgba(var(--danger-rgb), 0.5) !important;
  --tag-invalid-bg: rgba(var(--danger-rgb), 0.2) !important;
  --tag-remove-bg: rgba(var(--danger-rgb), 0.1) !important;
  --tag-remove-btn-color: var(--default-text-color) !important;
  --tag-remove-btn-bg--hover: rgba(var(--danger-rgb), 0.3) !important;
  --tag-pad: 0.2em 0.5em !important;
}

.tagify__tag__removeBtn {
  @apply mx-1 my-0;
}

.tagify__tag:focus div::before,
.tagify__tag:hover:not([readonly]) div::before {
  --tag-bg-inset: 0px !important;
}

.tagify__tag-text {
  @apply p-[3px];
}

.tags-look {
  @apply border border-defaultborder bg-white dark:bg-bodybg border-solid;
}
.tags-look .tagify__dropdown__item {
  @apply inline-block align-middle border border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg text-[0.85em] text-defaulttextcolor dark:text-defaulttextcolor/80 transition-[0s] m-[0.2em] px-[0.5em] py-[0.3em] rounded-[3px] border-solid;
}

.tags-look .tagify__dropdown__item--active {
  @apply text-white bg-primary;
}

.tags-look .tagify__dropdown__item:hover {
  @apply bg-primary text-white border-primary !important;
}

.tags-look .tagify__dropdown__item--hidden {
  @apply max-w-0 max-h-[initial] whitespace-nowrap indent-[-20px] mx-0 my-[0.2em] px-0 py-[0.3em] border-0;
}

.tagify__dropdown {
  @apply border-t-primary border-t border-solid border-defaultborder dark:border-defaultborder/10 !important;
}
.tagify__dropdown .tagify__dropdown__item {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}

.tagify--select .tagify__tag {
  @apply m-[3px] !important;
}

.tagify__dropdown__item--active {
  @apply text-white !important;
}

.dual-listbox__buttons {
  @apply sm:mt-0 mt-2 !important;
}

.tagify__dropdown__item:active {
  @apply text-white !important;
}

/* Suggestions items */
.tagify__tag .tagify__tag__avatar-wrap {
  @apply w-[22px] h-[22px] whitespace-normal transition-[0.12s] duration-[ease-out] align-middle me-[5px] rounded-[50%];
}

.tagify__tag img {
  @apply w-full align-top;
}

.tagify__dropdown.users-list .tagify__dropdown__item {
  @apply grid grid-cols-[auto_1fr] gap-[0_1em];
  grid-template-areas: "avatar name" "avatar email";
}

.tagify__dropdown.users-list header.tagify__dropdown__item {
  grid-template-areas: "add remove-tags" "remaning .";
}

.tagify__dropdown.users-list .tagify__dropdown__item:hover .tagify__dropdown__item__avatar-wrap {
  @apply scale-[1.2];
}

.tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  @apply w-9 h-9 overflow-hidden bg-light transition-[0.1s] duration-[ease-out] rounded-[50%];
  grid-area: avatar;
}

.tagify__dropdown.users-list img {
  @apply w-full align-top;
}

.tagify__dropdown.users-list header.tagify__dropdown__item > div,
.tagify__dropdown.users-list .tagify__dropdown__item strong {
  @apply w-full self-center;
  grid-area: name;
}

.tagify__dropdown.users-list span {
  @apply w-full text-[0.9em] opacity-60;
  grid-area: email;
}

.tagify__dropdown.users-list .tagify__dropdown__item__addAll {
  @apply border-b-defaultborder gap-0 border-b border-solid border-defaultborder dark:border-defaultborder/10;
}

.tagify__dropdown.users-list .remove-all-tags {
  @apply text-[0.8em] justify-self-end select-none px-[0.3em] py-[0.2em] rounded-[3px] hover:text-white hover:bg-orangemain;
  grid-area: remove-tags;
}

/* Tags items */
#users-list .tagify__tag {
  @apply whitespace-nowrap;
}

#users-list .tagify__tag img {
  @apply w-full align-top pointer-events-none;
}

#users-list .tagify__tag:hover .tagify__tag__avatar-wrap {
  @apply translate-x-[-10%] scale-[1.6];
}

#users-list .tagify__tag .tagify__tag__avatar-wrap {
  @apply w-4 h-4 whitespace-normal bg-light transition-[0.12s] duration-[ease-out] me-[5px] rounded-[50%];
}

.users-list .tagify__dropdown__itemsGroup:empty {
  @apply hidden;
}

.users-list .tagify__dropdown__itemsGroup::before {
  @apply content-[attr(data-title)] inline-block text-[0.9em] mt-[var(--tagify-dd-item-pad)] me-[var(--tagify-dd-item-pad)] mb-[var(--tagify-dd-item-pad)] ms-[var(--tagify-dd-item-pad)] italic rounded-md bg-success text-white font-semibold px-1.5 py-1;
}

.users-list .tagify__dropdown__itemsGroup:not(:first-of-type) {
  @apply border-t-defaultborder border-t border-solid border-defaultborder dark:border-defaultborder/10;
}

.tagify__dropdown__wrapper {
  @apply shadow-none bg-white dark:bg-bodybg border-0 !important;
}

.tagify__dropdown {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 shadow-defaultshadow rounded-md p-1 border-b-0 border-solid !important;
}

.tagify__dropdown__item--active {
  @apply bg-primary text-white !important;
}

.remove-all-tags {
  @apply text-white;
}

.tagify--mix .tagify__input {
  @apply m-0 p-0 !important;
}

.tagify__input::before {
  @apply text-defaulttextcolor !important;
}

/* End:: Tagify JS */
/* Start:: Dual List Box */
.dual-listbox .dual-listbox__search {
  @apply border-inputborder text-defaulttextcolor dark:border-defaultborder/10 bg-formcontrolbg text-sm font-normal leading-normal px-[0.85rem] py-2 rounded-[0.35rem];
  outline: none;
}
.dual-listbox .dual-listbox__search::-webkit-input-placeholder {
  @apply text-defaulttextcolor opacity-30;
}

.dual-listbox .dragging {
  @apply bg-light !important;
}
.dual-listbox .drop-above {
  @apply border-t-defaultborder dark:border-defaultborder/10 border-t border-solid !important;
}

.dual-listbox .dual-listbox__title {
  @apply border-s-defaultborder border-e-defaultborder border-t-defaultborder dark:border-defaultborder/10 font-medium border-s border-solid border-e border-t !important;
}

.dual-listbox .dual-listbox__available,
.dual-listbox .dual-listbox__selected {
  @apply border border-defaultborder border-solid dark:border-defaultborder/10 w-auto !important;
}

.dual-listbox .dual-listbox__item {
  @apply border-b-defaultborder border-b border-solid dark:border-b-defaultborder/10 !important;
}

.dual-listbox .dual-listbox__button {
  @apply bg-primary rounded-md !important;
}

.dual-listbox .dual-listbox__item.dual-listbox__item--selected {
  @apply bg-primary/10 !important;
}

/* End:: Dual List Box */
/* Start:: Toastify */
.toastify-right {
  @apply end-[15px] !important;
}

.toast-close {
  @apply ps-[15px] pe-[5px] py-0 !important;
}

[dir=rtl] .toast-close {
  @apply ps-[5px] pe-[15px] py-0 !important;
}

[dir=rtl] .toastify-right {
  @apply right-[inherit] !important;
}

/* End:: Toastify */
/* Start:: intl-tel-input */
.iti__flag {
  @apply bg-[url(../public/assets/images/flags/intel-input/flags.png)] !important;
}

/* End:: intl-tel-input */
/* End:: plugins */
.app-header .autoComplete_wrapper > input::placeholder {
  @apply text-headerprimecolor !important;
}

.autoComplete_wrapper > input::placeholder {
  @apply text-defaulttextcolor opacity-60 !important;
}

.filepond.circular-filepond .filepond--panel-root {
  @apply rounded-full !important;
}

.circular-filepond.filepond--root[data-style-panel-layout~=circle] .filepond--file [data-align*=left] {
  @apply start-[40%];
}

.circular-filepond.filepond--root[data-style-panel-layout~=circle] {
  @apply w-32 h-32 my-0 mx-auto !important;
}

/* Start Datatable  Styles */
.tabulator .tabulator-header .tabulator-col {
  @apply bg-white border-gray-200 dark:bg-bodybg dark:border-white/10 !important;
}

.tabulator .tabulator-header .tabulator-headers .tabulator-col .tabulator-col-content {
  @apply px-6 py-3 !important;
}

.tabulator .tabulator-row .tabulator-cell {
  @apply px-6 py-3 !important;
}

.tabulator .tabulator-row.tabulator-row-even {
  @apply bg-gray-100 dark:bg-black/20 !important;
}

.tabulator-row.tabulator-selectable:hover {
  @apply bg-gray-100 dark:bg-black/20 !important;
}

.tabulator .tabulator-row {
  @apply border-t border-gray-200 dark:border-white/10 !important;
}

.tabulator .tabulator-header {
  @apply border-0 bg-white dark:text-white dark:bg-bodybg !important;
}

.tabulator {
  @apply border-0 bg-transparent !important;
}

.tabulator-row .tabulator-cell {
  @apply border-gray-200 dark:border-white/10 !important;
}

.tabulator .tabulator-row .tabulator-cell:nth-child(9) {
  @apply border-e-0 !important;
}

.tabulator .tabulator-header .tabulator-col:nth-child(9) {
  @apply border-e-0 !important;
}

.tabulator .tabulator-footer {
  @apply border-gray-200 dark:text-defaulttextcolor/80 dark:border-white/10 !important;
}

.gridjs-wrapper {
  @apply border-0 border-transparent !important;
}

[dir=rtl] .tabulator-footer-contents {
  @apply dir-rtl !important;
}

.tabulator .tabulator-footer {
  @apply bg-transparent font-medium !important;
}

.tabulator .tabulator-tableholder .tabulator-table {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 bg-transparent !important;
}

.tabulator-row {
  @apply bg-transparent !important;
}

.tabulator .tabulator-footer .tabulator-paginator {
  @apply dark:text-white !important;
}

select.tabulator-page-size {
  @apply bg-[length:1rem_1rem] !important;
}

.tabulator .tabulator-footer .tabulator-page-size {
  @apply py-1 ps-3 pe-8 leading-[1.6] text-xs border-gray-200 dark:border-white/10 bg-transparent rounded-md !important;
}

.tabulator .tabulator-footer .tabulator-page {
  @apply py-1 px-3 rounded-md border-gray-200 dark:border-white/10 text-defaulttextcolor dark:text-defaulttextcolor/80 bg-transparent !important;
}

.tabulator .tabulator-footer .tabulator-page.active {
  @apply text-primary !important;
}

.tabulator .tabulator-footer .tabulator-page:not(.disabled):hover {
  @apply text-primary bg-primary/30 !important;
}

select.tabulator-page-size {
  @apply filter !important;
}
select.tabulator-page-size option {
  @apply dark:bg-bodybg border-gray-200 dark:border-white/10 !important;
}

.tabulator .tabulator-row .tabulator-cell.tabulator-row-handle {
  @apply px-0 !important;
}

.sortable-data select {
  @apply border-inherit rounded-sm bg-[length:1rem_1rem] !important;
}
.sortable-data .choices {
  @apply mb-0 !important;
}

.tabulator .tabulator-col-resize-handle:last-child {
  @apply hidden !important;
}

.tabulator-row .tabulator-cell.tabulator-editing {
  @apply border-0 border-gray-200 dark:border-white/10 !important;
}

.tabulator .tabulator-footer .tabulator-footer-contents {
  @apply sm:flex-row flex-col space-y-2 sm:space-y-0 p-4 !important;
}

.tabulator .tabulator-footer .tabulator-page-size {
  @apply sm:ps-3 ps-1 sm:pe-8 pe-4 !important;
}

.tabulator .tabulator-footer .tabulator-page {
  @apply sm:px-3 px-1 !important;
}

.tabulator .tabulator-footer .tabulator-paginator label {
  @apply hidden !important;
}

[dir=rtl] .tabulator .tabulator-footer .tabulator-paginator {
  @apply text-left !important;
}

@media screen and (max-width: 1024px) {
  .tabulator-col,
  .tabulator-cell {
    @apply w-60 !important;
  }
}
.tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=ascending] .tabulator-col-content .tabulator-col-sorter {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}

.tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=ascending] .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {
  @apply border-b-gray-200 dark:border-b-white/10;
}

.tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=descending] .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {
  @apply border-t-gray-200 dark:border-t-white/10;
}

.tabulator .tabulator-footer .tabulator-page-size {
  @apply block mb-2 w-full sm:inline-block sm:mb-0 sm:w-auto !important;
}

.sortable-data .choices__list--dropdown .choices__item--selectable, .sortable-data .choices__list[aria-expanded] .choices__item--selectable {
  @apply px-3 !important;
}

/* End Datatable  Styles */
.ts-control {
  @apply shadow-none border-defaultborder dark:border-b-defaultborder/10 !important;
}

.ts-wrapper.multi .ts-control [data-value] {
  @apply bg-none !important;
}

.ts-wrapper.multi .ts-control > div {
  @apply bg-primary text-white border-primary !important;
}

.ts-wrapper.single .ts-control {
  @apply bg-none opacity-80 !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  @apply bg-primary !important;
}

.select2-selection__rendered span, .select2-results__option span {
  @apply flex items-center;
}

.ts-control, .ts-wrapper.single.input-active .ts-control {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.ts-dropdown {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.ts-dropdown .active {
  @apply bg-primary text-white !important;
}

.noUi-target {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 !important;
}

/* Start:: ribbons */
.ribbon {
  @apply w-[80px] h-[80px] overflow-hidden absolute z-[1];
}

.ribbon {
  @apply before:absolute before:-z-[1] before:block after:absolute after:-z-[1] after:block;
}

.ribbon span {
  @apply absolute block w-[120px] p-[4px] text-[12px] z-[2] shadow-[0_5px_10px_rgba(0,0,0,0.1)] text-white uppercase text-center;
}

.ribbon.ribbon-primary {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-primary span {
  @apply bg-primary;
}

.ribbon.ribbon-secondary {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-secondary span {
  @apply bg-secondary;
}

.ribbon.ribbon-warning {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-warning span {
  @apply bg-warning;
}

.ribbon.ribbon-info {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-info span {
  @apply bg-info;
}

.ribbon.ribbon-success {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-success span {
  @apply bg-success;
}

.ribbon.ribbon-danger {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-danger span {
  @apply bg-danger;
}

.ribbon.ribbon-orange {
  @apply before:border-[3px] before:border-black/60 after:border-[3px] after:border-black/60;
}
.ribbon.ribbon-orange span {
  @apply bg-orangemain;
}

/* top left */
.ribbon-top-left {
  @apply -top-[7px] -start-[7px];
}

.ribbon-top-left {
  @apply before:border-t-transparent border-s-transparent after:border-t-transparent after:border-s-transparent;
}

.ribbon-top-left {
  @apply before:top-0 before:end-0 before:z-[1];
}

.ribbon-top-left {
  @apply after:bottom-0 after:start-0 after:z-[1];
}

.ribbon-top-left span {
  @apply -end-[12px] top-[20px] rotate-[-45deg];
}

[dir=rtl] .ribbon-top-left span {
  @apply rotate-45;
}

/* top left */
/* top right */
.ribbon-top-right {
  @apply top-[-7px] -end-[7px];
}

.ribbon-top-right {
  @apply before:border-t-transparent before:border-e-transparent after:border-t-transparent after:border-e-transparent;
}

.ribbon-top-right {
  @apply before:top-0 before:start-0 before:z-[1];
}

.ribbon-top-right {
  @apply after:bottom-0 after:end-0 after:z-[1];
}

.ribbon-top-right span {
  @apply -start-[12px] top-[20px] rotate-45;
}

[dir=rtl] .ribbon-top-right span {
  @apply rotate-[315deg];
}

/* top right */
/* bottom right */
.ribbon-bottom-right {
  @apply -bottom-[7px] -end-[7px];
}

.ribbon-bottom-right {
  @apply before:border-b-transparent before:border-e-transparent after:border-b-transparent after:border-e-transparent;
}

.ribbon-bottom-right {
  @apply before:bottom-0 before:start-0 before:z-[1];
}

.ribbon-bottom-right {
  @apply after:top-0 after:end-0 after:z-[1];
}

.ribbon-bottom-right span {
  @apply -start-[12px] bottom-[20px] -rotate-[225deg];
}

[dir=rtl] .ribbon-bottom-right span {
  @apply rotate-[225deg];
}

/* bottom right */
/* bottom left */
.ribbon-bottom-left {
  @apply -bottom-[7px] -start-[7px];
}

.ribbon-bottom-left {
  @apply before:border-b-transparent before:border-s-transparent after:border-b-transparent after:border-s-transparent;
}

.ribbon-bottom-left {
  @apply before:bottom-0 before:end-0 before:z-[1];
}

.ribbon-bottom-left {
  @apply after:top-0 after:start-0 after:z-[1];
}

.ribbon-bottom-left span {
  @apply -end-[12px] bottom-[20px] rotate-[225deg];
}

[dir=rtl] .ribbon-bottom-left span {
  @apply -rotate-[225deg];
}

/* bottom left */
/* ribbon 2 */
.ribbon-2 {
  @apply inline-block text-white absolute;
}
.ribbon-2.ribbon-primary {
  @apply bg-gradient-to-tr from-primary to-secondary before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-secondary {
  @apply bg-gradient-to-tr from-secondary to-[#7289FF] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-success {
  @apply bg-gradient-to-tr from-success to-[#009CA4] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-info {
  @apply bg-gradient-to-tr from-info to-[#4990E1] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-warning {
  @apply bg-gradient-to-tr from-warning to-[#9EA53C] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-danger {
  @apply bg-gradient-to-tr from-danger to-[#DE4980] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-orange {
  @apply bg-gradient-to-tr from-orangemain to-[#E5647E] before:border-t-[10px] before:border-black/80;
}
.ribbon-2.ribbon-left {
  @apply py-[5px] pe-[40px] ps-[20px] top-[10px] -start-[10px] after:border-e-[12px] after:border-e-white after:dark:border-e-bodybg after:border-t-[14px] after:border-t-transparent after:border-b-[16px]
    after:border-b-transparent after:end-0 after:top-0 before:border-s-[10px] before:border-s-transparent before:start-0 before:-bottom-[10px];
}
.ribbon-2.ribbon-right {
  @apply py-[5px] ps-[40px] pe-[20px] top-[10px] -end-[10px] after:border-s-[12px] after:border-s-white after:dark:border-s-bodybg after:border-t-[14px] after:border-t-transparent after:border-b-[16px]
    after:border-b-transparent after:start-0 after:top-0 before:border-e-[10px] before:border-e-transparent before:end-0 before:-bottom-[10px];
}

.ribbon-2 {
  @apply after:w-0 after:h-0 after:absolute;
}

.ribbon-2 {
  @apply before:h-0 before:w-0 before:absolute;
}

/* ribbon 2 */
/* ribbon 3 */
.ribbon-3 {
  @apply absolute -top-[8px] text-white after:absolute after:w-0 after:h-0 after:border-s-[16px] after:border-s-transparent after:border-e-[17px] after:border-e-transparent after:z-[0];
}
.ribbon-3.top-left {
  @apply rounded-ss-[10px] start-[10px];
}
.ribbon-3.top-left span {
  @apply after:-end-[7px] after:rounded-t-none after:rounded-se-[50px] after:rounded-b-none after:rounded-s-none;
}
.ribbon-3.top-right {
  @apply rounded-se-[10px] end-[10px];
}
.ribbon-3.top-right span {
  @apply after:-start-[7px] after:rounded-ss-[50px] after:rounded-e-none after:rounded-b-none after:rounded-s-none;
}
.ribbon-3 span {
  @apply relative block text-center text-[13px] leading-none p-[10px] z-[6] w-[33px] after:absolute after:h-[7px] after:w-[7px] after:top-0;
}
.ribbon-3.ribbon-success {
  @apply bg-success after:border-t-[10px] after:border-t-success;
}
.ribbon-3.ribbon-success span {
  @apply after:bg-[#0d9b80];
}
.ribbon-3.ribbon-primary {
  @apply bg-primary after:border-t-[10px] after:border-t-primary;
}
.ribbon-3.ribbon-primary span {
  @apply after:bg-primary;
}
.ribbon-3.ribbon-secondary {
  @apply bg-secondary after:border-t-[10px] after:border-t-secondary;
}
.ribbon-3.ribbon-secondary span {
  @apply after:bg-[#a017d1];
}
.ribbon-3.ribbon-warning {
  @apply bg-warning after:border-t-[10px] after:border-t-warning;
}
.ribbon-3.ribbon-warning span {
  @apply after:bg-[#d98415];
}
.ribbon-3.ribbon-info {
  @apply bg-info after:border-t-[10px] after:border-t-info;
}
.ribbon-3.ribbon-info span {
  @apply after:bg-[#148fc7];
}
.ribbon-3.ribbon-danger {
  @apply bg-danger after:border-t-[10px] after:border-t-danger;
}
.ribbon-3.ribbon-danger span {
  @apply after:bg-danger;
}

/* ribbon 3 */
/* ribbon 4 */
.ribbon-4 {
  @apply absolute top-0 text-white w-[30px] shadow-[0px_4px_16px_rgba(0,0,0,0.1)] after:absolute after:w-0 after:h-0 after:z-[6] after:border-b-[8px] after:border-b-transparent;
}
.ribbon-4 span {
  @apply relative block text-center text-[12px] leading-none py-[12px] px-[2px] z-[6];
}
.ribbon-4.top-left {
  @apply start-[10px] after:start-0;
}
.ribbon-4.top-right {
  @apply end-[10px] after:end-0;
}
.ribbon-4.ribbon-primary {
  @apply after:border-s-[15px] after:border-s-primary after:border-e-[15px] after:border-e-primary;
}
.ribbon-4.ribbon-primary span {
  @apply bg-primary;
}
.ribbon-4.ribbon-secondary {
  @apply after:border-s-[15px] after:border-s-secondary after:border-e-[15px] after:border-e-secondary;
}
.ribbon-4.ribbon-secondary span {
  @apply bg-secondary;
}
.ribbon-4.ribbon-warning {
  @apply after:border-s-[15px] after:border-s-warning after:border-e-[15px] after:border-e-warning;
}
.ribbon-4.ribbon-warning span {
  @apply bg-warning;
}
.ribbon-4.ribbon-info {
  @apply after:border-s-[15px] after:border-s-info after:border-e-[15px] after:border-e-info;
}
.ribbon-4.ribbon-info span {
  @apply bg-info;
}
.ribbon-4.ribbon-success {
  @apply after:border-s-[15px] after:border-s-success after:border-e-[15px] after:border-e-success;
}
.ribbon-4.ribbon-success span {
  @apply bg-success;
}
.ribbon-4.ribbon-danger {
  @apply after:border-s-[15px] after:border-s-danger after:border-e-[15px] after:border-e-danger;
}
.ribbon-4.ribbon-danger span {
  @apply bg-danger;
}

/* ribbon 4 */
/* ribbon 5 */
.ribbon-5 {
  @apply absolute w-[90px] h-[90px] text-white text-[12px] p-[5px] font-semibold flex items-end justify-center shadow-[1px_1px_16px_rgba(0,0,0,0.2)];
}
.ribbon-5.ribbon-primary {
  @apply bg-primary;
}
.ribbon-5.ribbon-secondary {
  @apply bg-secondary;
}
.ribbon-5.ribbon-warning {
  @apply bg-warning;
}
.ribbon-5.ribbon-info {
  @apply bg-info;
}
.ribbon-5.ribbon-success {
  @apply bg-success;
}
.ribbon-5.ribbon-danger {
  @apply bg-danger;
}
.ribbon-5.ribbon-dark {
  @apply bg-dark;
}
.ribbon-5.ribbon-orange {
  @apply bg-orangemain;
}
.ribbon-5.top-left {
  @apply -top-[2.8125rem] -start-[2.8125rem] rotate-[315deg];
}
.ribbon-5.top-right {
  @apply -top-[2.8125rem] -end-[2.8125rem] rotate-45;
}
.ribbon-5.bottom-left {
  @apply -bottom-[2.8125rem] -start-[2.8125rem] rotate-[225deg];
}
.ribbon-5.bottom-right {
  @apply -bottom-[2.8125rem] -end-[2.8125rem] rotate-[135deg];
}

[dir=rtl] .ribbon-5.top-left {
  @apply -rotate-[315deg];
}
[dir=rtl] .ribbon-5.top-right {
  @apply -rotate-45;
}
[dir=rtl] .ribbon-5.bottom-left {
  @apply -rotate-[225deg];
}
[dir=rtl] .ribbon-5.bottom-right {
  @apply -rotate-[135deg];
}

/* ribbon 5 */
/* ribbon-6 */
.ribbon-6 {
  @apply text-white py-[2px] px-[8px] absolute top-[10px] z-[6] text-[13px] shadow-[1px_1px_16px_rgba(0,0,0,0.2)];
}
.ribbon-6.ribbon-primary {
  @apply bg-primary;
}
.ribbon-6.ribbon-primary.ribbon-left {
  @apply after:border-s-[12px] after:border-s-primary;
}
.ribbon-6.ribbon-primary.ribbon-right {
  @apply after:border-e-[12px] after:border-e-primary;
}
.ribbon-6.ribbon-secondary {
  @apply bg-secondary;
}
.ribbon-6.ribbon-secondary.ribbon-left {
  @apply after:border-s-[12px] after:border-s-secondary;
}
.ribbon-6.ribbon-secondary.ribbon-right {
  @apply after:border-e-[12px] after:border-e-secondary;
}
.ribbon-6.ribbon-warning {
  @apply bg-warning;
}
.ribbon-6.ribbon-warning.ribbon-left {
  @apply after:border-s-[12px] after:border-s-warning;
}
.ribbon-6.ribbon-warning.ribbon-right {
  @apply after:border-e-[12px] after:border-e-warning;
}
.ribbon-6.ribbon-info {
  @apply bg-info;
}
.ribbon-6.ribbon-info.ribbon-left {
  @apply after:border-s-[12px] after:border-s-info;
}
.ribbon-6.ribbon-info.ribbon-right {
  @apply after:border-e-[12px] after:border-e-info;
}
.ribbon-6.ribbon-success {
  @apply bg-success;
}
.ribbon-6.ribbon-success.ribbon-left {
  @apply after:border-s-[12px] after:border-s-success;
}
.ribbon-6.ribbon-success.ribbon-right {
  @apply after:border-e-[12px] after:border-e-success;
}
.ribbon-6.ribbon-danger {
  @apply bg-danger;
}
.ribbon-6.ribbon-danger.ribbon-left {
  @apply after:border-s-[12px] after:border-s-danger;
}
.ribbon-6.ribbon-danger.ribbon-right {
  @apply after:border-e-[12px] after:border-e-danger;
}
.ribbon-6.ribbon-dark {
  @apply bg-dark;
}
.ribbon-6.ribbon-dark.ribbon-left {
  @apply after:border-s-[12px] after:border-s-dark;
}
.ribbon-6.ribbon-dark.ribbon-right {
  @apply after:border-e-[12px] after:border-e-dark;
}
.ribbon-6.ribbon-orange {
  @apply bg-orangemain;
}
.ribbon-6.ribbon-orange.ribbon-left {
  @apply after:border-s-[12px] after:border-s-orangemain;
}
.ribbon-6.ribbon-orange.ribbon-right {
  @apply after:border-e-[12px] after:border-e-orangemain;
}
.ribbon-6.ribbon-left {
  @apply start-0 after:absolute after:top-0 after:bottom-0 after:-end-[12px] after:border-t-[12px] after:border-t-transparent after:border-b-[11px] after:border-b-transparent after:w-0;
}
.ribbon-6.ribbon-right {
  @apply end-0 after:absolute after:top-0 after:bottom-0 after:-start-[12px] after:border-b-[12px] after:border-b-transparent after:border-t-[11px] after:border-t-transparent after:w-0;
}

/* ribbon-6 */
/* End:: ribbons */
/* Start:: widgets */
.widget-card {
  @apply relative bg-[url(../public/assets/images/media/media-25.jpg)] z-[1] py-[3.15rem];
}
.widget-card:before {
  @apply content-[""] absolute bg-primary/80 w-full h-full text-white z-[-1] start-0 top-0;
}

.widget-card2 {
  @apply relative bg-[url(../public/assets/images/media/media-35.jpg)] z-[1] py-[3.735rem];
}
.widget-card2:before {
  @apply relative content-[""] absolute bg-primary/80 w-full h-full text-white z-[-1] start-0 top-0;
}

.visit-gender.male:before {
  @apply bg-primary;
}

.visit-gender.female:before {
  @apply bg-secondary;
}

.visit-gender {
  @apply relative;
}
.visit-gender::before {
  @apply absolute content-[""] w-3 h-1.5 start-[-30px] rounded-[0.1rem] top-[7px];
}

#chart-10,
#chart-11,
#chart-12,
#chart-13 {
  @apply absolute top-[-0.5rem] end-4;
}

.widgets-task-list {
  @apply relative mb-0 list-none;
}
.widgets-task-list li {
  @apply relative mb-[14px];
}
.widgets-task-list li:last-child {
  @apply mb-0;
}
.widgets-task-list li:before {
  @apply content-[""] absolute w-[9px] h-[9px] bg-white dark:bg-bodybg start-[-27px] rounded-[50%] top-[7px];
}
.widgets-task-list li:nth-child(1) {
  @apply before:shadow-[0_0_4px_2px_rgba(92,103,247,0.5)] before:border-[2px] before:border-solid before:border-primary;
}
.widgets-task-list li:nth-child(2) {
  @apply before:shadow-[0_0_4px_2px_rgba(227,84,212,0.5)] before:border-[2px] before:border-solid before:border-primarytint1color;
}
.widgets-task-list li:nth-child(3) {
  @apply before:shadow-[0_0_4px_2px_rgba(255,93,159,0.5)] before:border-[2px] before:border-solid before:border-primarytint2color;
}
.widgets-task-list li:nth-child(4) {
  @apply before:shadow-[0_0_4px_2px_rgba(255,142,111,0.5)] before:border-[2px] before:border-solid before:border-primarytint3color;
}
.widgets-task-list li:nth-child(5) {
  @apply before:shadow-[0_0_4px_2px_rgba(158,92,247,0.5)] before:border-[2px] before:border-solid before:border-secondary;
}
.widgets-task-list li:nth-child(6) {
  @apply before:shadow-[0_0_4px_2px_rgba(33,206,158,0.5)] before:border-[2px] before:border-solid before:border-success;
}
.widgets-task-list:before {
  @apply content-[""] absolute h-[90%] border-s-defaultborder dark:border-defaultborder/10 border-s border-dashed start-[9px] top-1 bottom-0;
}

#activecustomers .apexcharts-text.apexcharts-datalabel-label {
  @apply fill-textmuted dark:text-textmuted/50;
}

.widget-circle-chart {
  @apply h-[218px];
}

/* End:: widgets */
/* Custom Theme Classes - Replacing Xtentra Vendor Classes */
.custom-theme-colors {
  /* Custom color palette for theme switching */
  --primary-color: var(--primary);
  --secondary-color: var(--secondary);
  --success-color: var(--success);
  --warning-color: var(--warning);
  --danger-color: var(--danger);
  --info-color: var(--info);
  --golden-color: var(--golden);
}

.custom-container {
  /* Custom container styling */
  background: rgb(var(--background));
  border: 1px solid rgb(var(--defaultborder));
  border-radius: 0.5rem;
  padding: 1rem;
}

.custom-container-primary {
  /* Primary themed container */
  background: rgb(var(--primary)/0.1);
  border: 1px solid rgb(var(--primary)/0.2);
  color: rgb(var(--primary));
}

.custom-container-secondary {
  /* Secondary themed container */
  background: rgb(var(--secondary)/0.1);
  border: 1px solid rgb(var(--secondary)/0.2);
  color: rgb(var(--secondary));
}

.custom-container-background {
  /* Background themed container */
  background: rgb(var(--bodybg));
  border: 1px solid rgb(var(--defaultborder));
  color: rgb(var(--defaulttextcolor));
}

/* Golden button theme consistency */
.btn-golden,
.custom-golden-btn {
  background: var(--golden-button);
  border: 1px solid var(--golden-button);
  color: white;
  box-shadow: var(--golden-button-shadow);
}
.btn-golden:hover,
.custom-golden-btn:hover {
  box-shadow: var(--golden-button-hover-shadow);
  transform: translateY(-1px);
}

/* Ensure custom theme takes precedence over vendor styles */
.custom-theme-override {
  /* Force custom theme colors */
  --bs-primary: var(--primary) !important;
  --bs-secondary: var(--secondary) !important;
  --bs-success: var(--success) !important;
  --bs-warning: var(--warning) !important;
  --bs-danger: var(--danger) !important;
  --bs-info: var(--info) !important;
}

/* MENU-STYLES - Simplified to horizontal only */
/* Start:: horizontal */
[data-nav-layout=horizontal] .sidemenu-layout-styles {
  @apply hidden;
}
@media (min-width: 992px) {
  [data-nav-layout=horizontal] {
    /* horizontal arrows */
  }
  [data-nav-layout=horizontal][class=light] .app-sidebar .side-menu__item.active, [data-nav-layout=horizontal][class=light] .app-sidebar .side-menu__item:hover, [data-nav-layout=horizontal][class=dark] .app-sidebar .side-menu__item.active, [data-nav-layout=horizontal][class=dark] .app-sidebar .side-menu__item:hover {
    @apply bg-transparent;
  }
  [data-nav-layout=horizontal] .app-sidebar {
    @apply shadow-defaultshadow;
  }
  [data-nav-layout=horizontal] .app-sidebar .side-menu__icon {
    @apply text-[0.9rem] w-[0.9rem] h-[0.9rem];
  }
  [data-nav-layout=horizontal] .main-menu {
    @apply flex transition-all duration-[0.5s] ease-[ease] flex-nowrap !important;
  }
  [data-nav-layout=horizontal] .main-menu-container {
    @apply inline-flex;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 .side-menu__item, [data-nav-layout=horizontal] .app-sidebar .slide-menu.child2 .side-menu__item {
    @apply px-4 py-[0.45rem];
  }
  [data-nav-layout=horizontal] .side-menu__label .badge {
    @apply hidden;
  }
  [data-nav-layout=horizontal] .mega-menu {
    @apply columns-3;
  }
  [data-nav-layout=horizontal] .app-header {
    @apply z-[104] shadow-none ps-0 !important;
  }
  [data-nav-layout=horizontal] .app-header .main-header-container .header-element.header-search {
    @apply ms-5;
  }
  [data-nav-layout=horizontal] .app-content {
    @apply min-h-[calc(100vh_-_10.5rem)] ms-0 mt-[7rem];
  }
  [data-nav-layout=horizontal] .app-content > .container-fluid {
    @apply w-[94%] mx-auto px-[7px];
  }
  [data-nav-layout=horizontal] .app-content {
    @apply ms-0 !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .main-sidebar,
  [data-nav-layout=horizontal] .app-sidebar .simplebar-mask {
    @apply overflow-visible;
  }
  [data-nav-layout=horizontal] .app-sidebar .main-menu > .slide {
    @apply mx-[0.1875rem] my-0;
  }
  [data-nav-layout=horizontal] .app-sidebar .main-sidebar {
    @apply shadow-none;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 .side-menu__item:before, [data-nav-layout=horizontal] .app-sidebar .slide-menu.child2 .side-menu__item:before, [data-nav-layout=horizontal] .app-sidebar .slide-menu.child3 .side-menu__item:before {
    @apply hidden;
  }
  [data-nav-layout=horizontal] .simplebar-offset {
    position: inherit !important;
  }
  [data-nav-layout=horizontal] .simplebar-wrapper {
    @apply w-full;
  }
  [data-nav-layout=horizontal] .main-sidebar .simplebar-vertical {
    @apply invisible !important;
  }
  [data-nav-layout=horizontal] .main-sidebar,
  [data-nav-layout=horizontal] .main-header-container {
    @apply w-[94%] mx-auto my-0;
  }
  [data-nav-layout=horizontal] .horizontal-logo {
    @apply block px-0 py-2;
  }
}
@media (min-width: 992px) and (min-width: 992px) {
  [data-nav-layout=horizontal] .horizontal-logo .header-logo img {
    @apply h-[1.7rem] leading-[1.7rem];
  }
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .desktop-logo {
    @apply block;
  }
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .desktop-dark,
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .desktop-white,
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .toggle-logo,
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .toggle-white,
  [data-nav-layout=horizontal] .horizontal-logo .header-logo .toggle-dark {
    @apply hidden;
  }
}
@media (min-width: 992px) {
  [data-nav-layout=horizontal] .main-header-container .sidemenu-toggle {
    @apply hidden;
  }
  [data-nav-layout=horizontal] .app-sidebar {
    @apply w-full h-auto border-b-menubordercolor border-b border-solid top-[4.25rem] dark:border-defaultborder/10 !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 li,
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child2 li,
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child3 li {
    @apply ps-2;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 .side-menu__item:before,
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child2 .side-menu__item:before,
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child3 .side-menu__item:before {
    @apply top-4;
  }
  [data-nav-layout=horizontal] .app-sidebar .simplebar-content {
    @apply overflow-hidden p-0 !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .simplebar-content-wrapper {
    @apply overflow-visible h-auto;
  }
  [data-nav-layout=horizontal] .app-sidebar .main-sidebar {
    @apply p-0;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child1 {
    @apply px-0 py-[0.1875rem] start-6;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child2, [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child3 {
    @apply px-0 py-[0.1875rem] end-full;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.active {
    @apply inset-x-auto !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 {
    @apply absolute !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .side-menu__item {
    @apply w-full flex p-[0.92rem] rounded-none;
  }
  [data-nav-layout=horizontal] .app-sidebar .side-menu__angle {
    @apply block end-1;
  }
  [data-nav-layout=horizontal] .app-sidebar .side-menu__icon {
    @apply me-1 mb-0;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child1 {
    @apply bg-white dark:bg-bodybg min-w-[12rem] top-full !important;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide {
    @apply p-0;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 {
    @apply rounded-[0_0_0.25rem_0.25rem];
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 .slide.has-sub, [data-nav-layout=horizontal] .app-sidebar .slide-menu.child1 .slide {
    @apply w-full flex px-[0.1875rem] py-0;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide-menu.child2, [data-nav-layout=horizontal] .app-sidebar .slide-menu.child3 {
    @apply rounded-md;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide, [data-nav-layout=horizontal] .app-sidebar .slide.has-sub {
    position: static;
  }
  [data-nav-layout=horizontal] .app-sidebar .slide .slide-menu.child1, [data-nav-layout=horizontal] .app-sidebar .slide .slide-menu.child2, [data-nav-layout=horizontal] .app-sidebar .slide .slide-menu.child3, [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child1, [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child2, [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child3 {
    @apply shadow-[0_0_0.375rem_black];
  }
  [data-nav-layout=horizontal] .app-sidebar .main-menu {
    @apply mb-0;
  }
  [data-nav-layout=horizontal] .main-sidebar-header {
    @apply hidden !important;
  }
  [data-nav-layout=horizontal] .main-sidebar {
    @apply mt-0 pb-12;
  }
  [data-nav-layout=horizontal] .slide__category {
    @apply hidden;
  }
  [data-nav-layout=horizontal] .main-menu-container .slide-left {
    @apply start-[1.438rem];
  }
  [data-nav-layout=horizontal] .main-menu-container .slide-left,
  [data-nav-layout=horizontal] .main-menu-container .slide-right {
    @apply absolute text-customwhite flex items-center justify-center z-[1] cursor-pointer border border-defaultborder p-1.5 rounded-[3.125rem] border-solid top-[0.563rem];
  }
  [data-nav-layout=horizontal] .main-menu-container .slide-left,
  [data-nav-layout=horizontal] .main-menu-container .slide-right {
    @apply absolute text-customwhite flex items-center justify-center z-[1] cursor-pointer border border-defaultborder bg-white dark:bg-bodybg p-1.5 rounded-[3.125rem] border-solid top-[0.313rem];
  }
  [data-nav-layout=horizontal] .main-menu-container .slide-right {
    @apply end-[-2%];
  }
  [data-nav-layout=horizontal] .main-menu-container .slide-left {
    @apply start-[-2%];
  }
  [data-nav-layout=horizontal][page-style=classic] .app-sidebar {
    @apply border-b-defaultborder border-b border-solid;
  }
  [data-nav-layout=horizontal][dir=rtl] .main-menu-container .slide-right, [data-nav-layout=horizontal][dir=rtl] .main-menu-container .slide-left {
    @apply rotate-180;
  }
  [data-nav-layout=horizontal][data-menu-styles=transparent][class=dark][data-icon-overlay=open] .app-sidebar:hover {
    @apply backdrop-blur-none;
  }
  [data-nav-layout=horizontal][data-menu-styles=light] .main-menu-container .slide-right, [data-nav-layout=horizontal][data-menu-styles=light] .main-menu-container .slide-left {
    --custom-white: 255 255 255;
    --default-border: 243 243 243;
  }
  [data-nav-layout=horizontal][data-page-style=modern] .app-sidebar {
    @apply border-b-headerbordercolor border-b border-solid;
  }
  [data-nav-layout=horizontal][data-menu-styles=transparent][class=dark] .app-sidebar {
    @apply backdrop-blur-[30px];
  }
  [data-nav-layout=horizontal][data-nav-style=menu-click] .app-sidebar .slide.has-sub .slide-menu.child1.force-left, [data-nav-layout=horizontal][data-nav-style=menu-hover] .app-sidebar .slide.has-sub .slide-menu.child1.force-left, [data-nav-layout=horizontal][data-nav-style=icon-click] .app-sidebar .slide.has-sub .slide-menu.child1.force-left, [data-nav-layout=horizontal][data-nav-style=icon-hover] .app-sidebar .slide.has-sub .slide-menu.child1.force-left {
    @apply end-0 !important;
  }
  [data-nav-layout=horizontal][data-nav-style=menu-click] .app-sidebar .slide.has-sub .slide-menu.child2.force-left, [data-nav-layout=horizontal][data-nav-style=menu-click] .app-sidebar .slide.has-sub .slide-menu.child3.force-left, [data-nav-layout=horizontal][data-nav-style=menu-hover] .app-sidebar .slide.has-sub .slide-menu.child2.force-left, [data-nav-layout=horizontal][data-nav-style=menu-hover] .app-sidebar .slide.has-sub .slide-menu.child3.force-left, [data-nav-layout=horizontal][data-nav-style=icon-click] .app-sidebar .slide.has-sub .slide-menu.child2.force-left, [data-nav-layout=horizontal][data-nav-style=icon-click] .app-sidebar .slide.has-sub .slide-menu.child3.force-left, [data-nav-layout=horizontal][data-nav-style=icon-hover] .app-sidebar .slide.has-sub .slide-menu.child2.force-left, [data-nav-layout=horizontal][data-nav-style=icon-hover] .app-sidebar .slide.has-sub .slide-menu.child3.force-left {
    @apply -start-full !important;
  }
}

@media (max-width: 991.98px) {
  [data-nav-layout=horizontal] .horizontal-logo .header-logo img {
    @apply h-[1.7rem] leading-[1.7rem];
  }
}
/* End:: horizontal */
/* Start:: chat */
.main-chart-wrapper {
  @apply relative overflow-hidden;
}
.main-chart-wrapper .chat-info,
.main-chart-wrapper .main-chat-area,
.main-chart-wrapper .chat-user-details {
  @apply bg-white dark:bg-bodybg h-[calc(100vh_-_8rem)] rounded-md;
}
.main-chart-wrapper .chat-users-tab,
.main-chart-wrapper .chat-groups-tab,
.main-chart-wrapper .chat-contacts-tab {
  @apply max-h-[calc(100vh_-_17.5rem)];
}
.main-chart-wrapper .chat-content {
  @apply max-h-[calc(100vh_-_19.5rem)];
}
.main-chart-wrapper .chat-content .simplebar-content-wrapper .simplebar-content {
  @apply mt-auto;
}
.main-chart-wrapper .chat-content ul li {
  @apply mb-4;
}
.main-chart-wrapper .chat-content ul li:last-child {
  @apply mb-0;
}
.main-chart-wrapper .responsive-chat-close,
.main-chart-wrapper button.responsive-userinfo-open {
  @apply hidden;
}
.main-chart-wrapper .chat-info {
  @apply relative;
}
.main-chart-wrapper .chat-info .tab-style-6 {
  @apply p-4 rounded-none;
}
.main-chart-wrapper .chat-info .nav-link {
  @apply bg-primary/50;
}
.main-chart-wrapper .chat-info .tab-pane {
  @apply p-0;
}
.main-chart-wrapper .chat-info .chat-groups-tab li {
  @apply px-5 py-2.5;
}
.main-chart-wrapper .chat-info .chat-groups-tab .group-indivudial {
  @apply text-primary font-normal;
}
.main-chart-wrapper .chat-info .chat-contacts-tab > li {
  @apply px-5 py-2.5;
}
.main-chart-wrapper .chat-info .chat-contacts-tab .incoming-call-success i,
.main-chart-wrapper .chat-info .chat-contacts-tab .outgoing-call-success i {
  @apply text-success text-sm;
}
.main-chart-wrapper .chat-info .chat-contacts-tab .incoming-call-failed i,
.main-chart-wrapper .chat-info .chat-contacts-tab .outgoing-call-failed i {
  @apply text-danger text-sm;
}
.main-chart-wrapper .chat-info .chat-users-tab li,
.main-chart-wrapper .chat-info .chat-groups-tab li {
  @apply px-4 py-2.5 border-s-2 border-s-transparent border-solid;
}
.main-chart-wrapper .chat-info .chat-users-tab li .chat-msg,
.main-chart-wrapper .chat-info .chat-groups-tab li .chat-msg {
  @apply text-textmuted dark:text-textmuted/50 max-w-[11.25rem] inline-block;
}
.main-chart-wrapper .chat-info .chat-users-tab li .chat-msg-typing .chat-msg,
.main-chart-wrapper .chat-info .chat-groups-tab li .chat-msg-typing .chat-msg {
  @apply text-success !important;
}
.main-chart-wrapper .chat-info .chat-users-tab li .chat-msg-typing .chat-read-icon,
.main-chart-wrapper .chat-info .chat-groups-tab li .chat-msg-typing .chat-read-icon {
  @apply hidden;
}
.main-chart-wrapper .chat-info .chat-users-tab li .chat-read-icon,
.main-chart-wrapper .chat-info .chat-groups-tab li .chat-read-icon {
  @apply leading-none;
}
.main-chart-wrapper .chat-info .chat-users-tab li .chat-read-icon i,
.main-chart-wrapper .chat-info .chat-groups-tab li .chat-read-icon i {
  @apply text-base text-success;
}
.main-chart-wrapper .chat-info .chat-users-tab li.active,
.main-chart-wrapper .chat-info .chat-groups-tab li.active {
  @apply bg-primary/10 text-defaulttextcolor border-s-[3px] border-s-primary border-solid;
}
.main-chart-wrapper .chat-info .chat-users-tab li.chat-msg-unread,
.main-chart-wrapper .chat-info .chat-groups-tab li.chat-msg-unread {
  @apply bg-light text-defaulttextcolor;
}
.main-chart-wrapper .chat-info .chat-users-tab li.chat-msg-unread.active,
.main-chart-wrapper .chat-info .chat-groups-tab li.chat-msg-unread.active {
  @apply bg-primary/10;
}
.main-chart-wrapper .chat-info .chat-users-tab li.chat-msg-unread .chat-msg,
.main-chart-wrapper .chat-info .chat-groups-tab li.chat-msg-unread .chat-msg {
  @apply text-defaulttextcolor;
}
.main-chart-wrapper .chat-info .chat-users-tab li.chat-msg-unread .chat-read-icon i,
.main-chart-wrapper .chat-info .chat-groups-tab li.chat-msg-unread .chat-read-icon i {
  @apply text-textmuted dark:text-textmuted/50;
}
.main-chart-wrapper .chat-info .chat-users-tab li.chat-inactive .chat-read-icon,
.main-chart-wrapper .chat-info .chat-groups-tab li.chat-inactive .chat-read-icon {
  @apply hidden;
}
.main-chart-wrapper .main-chat-area {
  @apply relative;
}
.main-chart-wrapper .main-chat-area .main-chat-head {
  @apply p-4;
}
.main-chart-wrapper .main-chat-area .chatnameperson, .main-chart-wrapper .main-chat-area .chatting-user-info {
  @apply font-semibold;
}
.main-chart-wrapper .main-chat-area .chat-content {
  @apply relative z-[1] p-4;
}
.main-chart-wrapper .main-chat-area .chat-content:before {
  @apply absolute content-[""] w-full h-full bg-[url("../public/assets/images/media/svg/pattern-1.svg")] bg-repeat z-[-1] opacity-[0.018] inset-0;
}
.main-chart-wrapper .main-chat-area .chat-content .chatting-user-info {
  @apply text-defaulttextcolor text-defaulttextcolor/80 text-[0.813rem];
}
.main-chart-wrapper .main-chat-area .chat-content .chatting-user-info .msg-sent-time {
  @apply text-textmuted dark:text-textmuted/50 text-xs font-medium;
}
.main-chart-wrapper .main-chat-area .chat-content .chatting-user-info .msg-sent-time .chat-read-mark i {
  @apply text-success me-[0.3rem];
}
.main-chart-wrapper .main-chat-area .chat-content .main-chat-msg div {
  @apply w-fit mb-[0.4rem] p-4;
}
.main-chart-wrapper .main-chat-area .chat-content .main-chat-msg div p {
  @apply text-[0.813rem];
}
.main-chart-wrapper .main-chat-area .chat-content .main-chat-msg div .chat-media-image {
  @apply w-[6.25rem] h-[6.25rem] rounded-md;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-start .main-chat-msg div {
  @apply bg-primarytint1color/10 text-defaulttextcolor dark:text-defaulttextcolor/80 font-medium rounded-br-[1.3rem] rounded-t-[1.3rem] rounded-bl-none;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-start .msg-sent-time {
  @apply ms-1;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-end {
  @apply text-end justify-end;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-end .main-chat-msg div {
  @apply bg-primary/90 text-white font-medium rounded-br-none rounded-t-[1.3rem] rounded-bl-[1.3rem];
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-end .msg-sent-time {
  @apply me-1;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-start,
.main-chart-wrapper .main-chat-area .chat-content .chat-item-end {
  @apply flex;
}
.main-chart-wrapper .main-chat-area .chat-content .chat-item-start .chat-list-inner,
.main-chart-wrapper .main-chat-area .chat-content .chat-item-end .chat-list-inner {
  @apply flex max-w-[75%] items-end;
}
.main-chart-wrapper .main-chat-area .chat-footer {
  @apply w-full shadow-[0_0.25rem_1rem_rgba(0,0,0,0.1)];
}
.main-chart-wrapper .main-chat-area .chat-footer {
  @apply shrink-0 flex items-center border-t-defaultborder bg-white dark:bg-bodybg absolute p-4 border-t border-solid bottom-0 inset-x-auto;
}
.main-chart-wrapper .main-chat-area .chat-day-label {
  @apply text-center text-textmuted dark:text-textmuted/50 opacity-75 relative mb-8;
}
.main-chart-wrapper .main-chat-area .chat-day-label span {
  @apply text-[0.7rem] bg-primary/10 text-primary px-2 py-[0.188rem] rounded-[0.3rem];
}
@media (min-width: 992px) {
  .main-chart-wrapper .chat-info {
    @apply min-w-[25rem] max-w-[25rem];
  }
}
.main-chart-wrapper .main-chat-area {
  @apply w-full max-w-full overflow-hidden;
}
@media (max-width: 1275.98px) and (min-width: 992px) {
  .main-chart-wrapper .chat-info {
    @apply min-w-[25rem] max-w-[25rem];
  }
  .main-chart-wrapper .main-chat-area {
    @apply w-full max-w-full overflow-hidden;
  }
}
@media (max-width: 991.98px) {
  .main-chart-wrapper .chat-info {
    @apply w-full;
  }
  .main-chart-wrapper .main-chat-area {
    @apply hidden min-w-full max-w-full;
  }
  .main-chart-wrapper .responsive-chat-close {
    @apply block;
  }
}

.chat-user-details .shared-files li {
  @apply mb-4;
}
.chat-user-details .shared-files li:last-child {
  @apply mb-0;
}
.chat-user-details .shared-files .shared-file-icon i {
  @apply w-4 h-4 leading-4 flex items-center justify-center text-lg p-[1.125rem] rounded-[0.3rem];
}
.chat-user-details .shared-files .shared-file-icon {
  @apply rounded-[0.3rem];
}
.chat-user-details .chat-media img {
  @apply w-full rounded-md mb-5;
}

@media (max-width: 1400px) {
  .chat-user-details.open {
    @apply block shadow-defaultshadow border-s-defaultborder border-s border-solid end-0 top-2;
  }
  button.responsive-userinfo-open {
    @apply block;
  }
}
@media (max-width: 991.98px) {
  .main-chart-wrapper.responsive-chat-open .chat-info {
    @apply hidden;
  }
  .main-chart-wrapper.responsive-chat-open .main-chat-area {
    @apply block;
  }
}
@media (max-width: 767.98px) {
  .main-chart-wrapper .main-chat-area .chat-content .main-chat-msg div .chat-media-image {
    @apply w-10 h-10;
  }
}
@media (max-width: 354px) {
  .main-chart-wrapper .chat-contacts-tab,
  .main-chart-wrapper .chat-groups-tab,
  .main-chart-wrapper .chat-users-tab {
    @apply max-h-[calc(100vh_-_19.5rem)];
  }
}
.chat-contacts-tab li {
  @apply border-b-defaultborder border-b border-solid dark:border-defaultborder/10;
}
.chat-contacts-tab li:last-child {
  @apply border-b-0;
}

[data-page-style=modern] .main-chat-area .rightIcons .btn-outline-light {
  @apply border-defaultborder;
}

[dir=rtl] .chat-footer .btn-send {
  @apply rotate-180;
}

[class=dark] .main-chat-area .chat-content:before {
  @apply invert-[1];
}

@media (max-width: 480px) {
  .main-chart-wrapper .chat-users-tab, .main-chart-wrapper .chat-groups-tab, .main-chart-wrapper .chat-contacts-tab {
    max-height: calc(100vh - 22.5rem);
  }
}
/* End:: chat */
/* Start:: ecommerce */
/* Start:: Products */
@media screen and (min-width: 576px) {
  [dir=rtl] .ecommerce-more-link::before, [dir=rtl] .ecommerce-more-link:after {
    @apply end-2;
  }
}

.ecommerce-more-link[aria-expanded=true]:after {
  @apply block;
}
.ecommerce-more-link[aria-expanded=true]:before {
  @apply hidden;
}

.products-navigation-card .form-check-label {
  @apply text-[0.813rem] font-medium;
}

/* End:: Products */
/* Start:: Product Details */
.swiper-view-details .swiper-slide {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaultborder/10 rounded-md border-solid;
}
.swiper-view-details .swiper-slide.swiper-slide-thumb-active {
  @apply bg-light !important;
}

.swiper-preview-details .swiper-button-next {
  @apply bg-black/10 text-customwhite;
}
.swiper-preview-details .swiper-button-prev {
  @apply bg-black/10 text-customwhite;
}

.product-colors {
  @apply w-[1.2rem] h-[1.2rem] flex items-center justify-center border border-defaultborder dark:border-defaultborder/10 bg-light me-2 rounded-[50%] border-solid;
}
.product-colors.color-2.selected {
  @apply border-2 border-solid border-[#f78aeb];
}
.product-colors.color-2 i {
  @apply text-[#f78aeb];
}
.product-colors.color-4.selected {
  @apply border-2 border-solid border-[#ff9594];
}
.product-colors.color-4 i {
  @apply text-[#ff9594];
}
.product-colors.color-1.selected {
  @apply border-2 border-solid border-[#8a90e7];
}
.product-colors.color-1 i {
  @apply text-[#8a90e7];
}
.product-colors.color-3.selected {
  @apply border-2 border-solid border-[#f78ab6];
}
.product-colors.color-3 i {
  @apply text-[#f78ab6];
}
.product-colors.color-5.selected {
  @apply border-2 border-solid border-[#b688f3];
}
.product-colors.color-5 i {
  @apply text-[#b688f3];
}

.ecommerce-assurance {
  @apply border border-primary/30 rounded-md px-6 py-4 border-dashed;
}
.ecommerce-assurance svg {
  @apply w-12 h-12;
}

.product-images {
  @apply ps-2;
}
.product-images .products-review-images img {
  @apply w-[3.125rem] h-[3.125rem] rounded-md bg-primary/10 me-1;
}

.similar-product-name {
  @apply max-w-[80%];
}

/* End:: Product Details */
/* Start:: Cart */
.product-quantity-container {
  @apply w-[10.5rem];
}
.product-quantity-container .input-group input.form-control:focus {
  @apply shadow-none;
}
.product-quantity-container .input-group .product-quantity-minus.btn:focus, .product-quantity-container .input-group .product-quantity-minus.btn:hover,
.product-quantity-container .input-group .product-quantity-plus.btn:focus,
.product-quantity-container .input-group .product-quantity-plus.btn:hover {
  @apply border-inputborder;
}
.product-quantity-container .cart-input-group {
  @apply w-36;
}
.product-quantity-container .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  @apply ms-0;
}

.cart-summary-nav.tab-style-8.nav-tabs.scaleX.nav-tabs > .nav-item > .nav-link {
  @apply bg-light;
}
.cart-summary-nav.tab-style-8.nav-tabs.scaleX.nav-tabs > .nav-item > .nav-link.active {
  @apply bg-primary/10 rounded-br-none rounded-bl-none;
}

.cart-items01 {
  @apply w-[31rem];
}

.quantity-icon {
  @apply align-text-top pt-px;
}

.cart-empty svg {
  @apply w-[6.25rem] h-[6.25rem] fill-defaulttextcolor mb-5;
}

/* End:: Cart */
/* Start:: Checkout */
#product-features .ql-editor ol {
  @apply pl-0 rtl:pr-0 !important;
}

.product-checkout .nav .nav-item .nav-link.active {
  @apply bg-transparent !important;
}
.product-checkout .tab-style-2 .nav-item .nav-link {
  @apply px-8 py-[0.85rem];
}
.product-checkout .form-floating input,
.product-checkout .form-floating textarea {
  @apply text-[0.813rem] font-normal;
}
.product-checkout .shipping-method-container,
.product-checkout .payment-card-container {
  @apply relative border border-defaultborder dark:border-defaultborder/10 rounded-md p-2.5 border-solid;
}
.product-checkout .shipping-method-container .form-check-input,
.product-checkout .payment-card-container .form-check-input {
  @apply absolute end-3 top-[1.2rem];
}
@media (min-width: 576px) {
  .product-checkout .shipping-method-container .shipping-partner-details,
  .product-checkout .shipping-method-container .saved-card-details,
  .product-checkout .payment-card-container .shipping-partner-details,
  .product-checkout .payment-card-container .saved-card-details {
    @apply w-[30rem];
  }
}

.checkout-payment-success {
  @apply text-center;
}
.checkout-payment-success img {
  @apply w-[200px] h-[200px];
}

@media screen and (max-width: 575px) {
  #shipped-tab-pane .btn-group .btn {
    @apply w-full rounded-md;
  }
}
/* End:: Checkout */
/* Start:: Order Details */
.order-track .accordion {
  @apply relative;
}
.order-track .accordion:before {
  @apply content-[""] absolute w-px h-[72%] opacity-20 border-s-primary border-s border-dashed start-3.5 end-0 top-[30px] bottom-0;
}
.order-track .accordion:last-child::before {
  @apply content-none;
}
.order-track .track-order-icon {
  @apply p-0.5;
}

/* End:: Order Details */
/* Start:: Add Products & Edit Products */
.add-products .tab-style-2 .nav-item .nav-link {
  @apply px-8 py-[0.85rem];
}

.color-selection .choices__input {
  @apply w-[6.25rem];
}

.product-documents-container .filepond--root {
  @apply w-full;
}
.product-documents-container .filepond--panel-root {
  @apply border-inputborder dark:border-inputborder/10 rounded-md !important;
}
.product-documents-container .filepond--root .filepond--drop-label label {
  @apply text-textmuted dark:text-textmuted/50;
}

#product-features {
  @apply max-h-[14.75rem] overflow-y-scroll;
}

/* End:: Add Products & Edit Products */
/* Start:: Ecommerce Landing */
.carousel-indicators [data-bs-target] {
  @apply bg-white h-1.5 w-1.5 opacity-100 border-primary/20 transition-all duration-[ease] delay-[0.5s] rounded-[50%] border-[3px] border-solid;
}

.carousel-indicators [data-bs-target][data-bs-target].active {
  @apply border-primary;
}

.heading-section {
  @apply text-center;
}

.heading-description {
  @apply text-defaulttextcolor mb-0;
}

.top-left-badge {
  @apply absolute z-[1] grid items-baseline justify-items-start px-[0.65rem] py-[0.45rem] start-4 top-4 content-stretch;
}

.box.card-style-2:hover {
  @apply shadow-[0_3px_10px_0px_black/10];
}

.card-style-2 {
  @apply overflow-hidden;
}
.card-style-2 .img-box-2 {
  @apply transition-transform duration-[ease] delay-[1.5s] flex justify-center;
}
.card-style-2 .img-box-2:after {
  @apply absolute w-full h-full bg-[rgba(0,0,0,0.03)] start-0 top-0;
}
.card-style-2 .card-img-top {
  @apply relative;
}
.card-style-2 .btn-style-1 {
  @apply absolute shadow-none end-4 bottom-4;
}
.card-style-2:hover {
  @apply border-primary/20;
}
.card-style-2:hover .btns-container-1 {
  @apply z-[2] bottom-[30%];
}
.card-style-2:hover .img-box-2 .bg-primary-transparent {
  @apply bg-primary/20;
}
.card-style-2 .btns-container-1 {
  @apply absolute text-center justify-center bottom-[-10%] flex w-full z-[-1] transition-all duration-[0.5s] start-0;
}

.box.card-style-3:hover {
  @apply shadow-[0_3px_10px_0px_black/10];
}

.ecommerce-more-link {
  @apply relative bg-primary/10 text-primary rounded-md font-medium w-full text-xs block ps-3 pe-6 py-[0.6rem];
}
.ecommerce-more-link:hover {
  @apply text-primary;
}
.ecommerce-more-link:before {
  @apply absolute content-[""] font-bold end-3 top-2.5 font-bootstrap;
}
.ecommerce-more-link:after {
  @apply absolute content-[""] font-bold hidden end-3 top-2.5 font-bootstrap;
}

.ecommerce-gallery img {
  @apply max-w-full max-h-full min-h-full object-contain rounded-[0.3rem];
}

.tag-badge {
  @apply absolute start-3 top-3;
}

.ecommerce-gallery {
  @apply max-h-[430px] flex justify-center bg-primary/10 rounded-[0.3rem];
}

.glightbox.card {
  @apply z-0;
}
.glightbox.card:hover .view-lightbox {
  @apply flex absolute z-[1] text-center bg-[rgba(0,0,0,0.7)] items-center justify-center start-[40%] top-auto bottom-[40%];
}
.glightbox.card:hover .view-lightbox:hover {
  @apply text-white/80;
}
.glightbox.card:hover:after {
  @apply absolute w-full h-full bg-[rgba(0,0,0,0.3)] z-[-1] start-0 top-0;
}

.glightbox.card .view-lightbox {
  @apply hidden;
}

.ad-gallery {
  @apply hidden;
}

@media (min-width: 769px) {
  .glightbox-clean .gslide-media {
    @apply shadow-none !important;
  }
}
.glightbox-clean .gslide-description {
  @apply bg-transparent !important;
}

.glightbox-clean .gslide-title {
  @apply text-white !important;
}

.glightbox-clean .gslide-title {
  @apply text-[1.2rem] font-medium mb-0 font-defaultfont !important;
}

.glightbox-clean .gdesc-inner {
  @apply text-center;
}

.similar-products-image img {
  @apply w-[4.97rem] h-[4.97rem] bg-primary/[0.05] rounded-[0.3rem];
}

.filter-bw {
  @apply grayscale-[1];
}

.card-style-6 .card-style-6-avatar .avatar {
  @apply border border-defaultborder text-defaulttextcolor border-solid;
}

.card-style-6:has(.form-check-input:checked) {
  @apply border-primary/50 bg-secondary/10 border-dashed;
}

.card-style-6:has(.form-check-input:checked) .card-style-6-avatar .avatar {
  @apply bg-primary text-white;
}

.glightbox.box {
  @apply z-0 hover:after:absolute hover:after:content-[""] hover:after:w-full hover:after:h-full hover:after:bg-[rgba(0,0,0,0.3)] hover:after:z-[-1] hover:after:start-0 hover:after:top-0;
}

.glightbox.box .view-lightbox {
  @apply hidden;
}

.glightbox.box:hover .view-lightbox {
  @apply flex absolute content-[""] z-[1] text-center bg-[rgba(0,0,0,0.7)] items-center justify-center start-[40%] top-auto bottom-[40%];
}

.ad-gallery {
  @apply hidden !important;
}

.product-quantity-container input::-webkit-inner-spin-button {
  @apply hidden !important;
}

/* End:: Ecommerce styles */
/* End:: ecommerce */
/* Start:: file-manager */
.file-manager-folders,
.selected-file-details {
  @apply bg-white dark:bg-bodybg;
}

.folder-svg-container svg {
  @apply w-12 h-12;
}

.file-details img {
  @apply w-[150px] h-[150px] bg-light rounded-[50%];
}

@media (max-width: 1200px) {
  .selected-file-details.open {
    @apply w-[19.5rem] absolute block shadow-[0_0.125rem_0_rgba(10,10,10,0.04)] border-s-defaultborder border-s border-solid end-0 top-2;
  }
}
.file-manager-folders.open {
  @apply block;
}

@media (max-width: 365px) {
  .file-manager-container .file-folders-container {
    @apply max-h-[calc(100vh_-_12.9rem)];
  }
}
ul.files-main-nav {
  @apply mb-0;
}
ul.files-main-nav li {
  @apply rounded-md mb-[0.08rem] px-[0.8rem] pt-2 pb-[0.43rem];
}
ul.files-main-nav li:last-child {
  @apply mb-0;
}
ul.files-main-nav li div {
  @apply text-textmuted dark:text-textmuted/50;
}
ul.files-main-nav li:hover div {
  @apply text-primary;
}
ul.files-main-nav li.active {
  @apply bg-primary/10;
}
ul.files-main-nav li.active div {
  @apply text-primary;
}
ul.files-main-nav li div.filemanager-upgrade-storage {
  @apply w-[235px] h-auto bg-light border-defaultborder rounded-md text-center text-defaulttextcolor p-4 border-2 border-dashed;
}
ul.files-main-nav li div.filemanager-upgrade-storage img {
  @apply w-[150px] h-[150px];
}

#file-manager-storage .apexcharts-pie line, #file-manager-storage .apexcharts-pie circle {
  @apply stroke-transparent;
}
#file-manager-storage .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  @apply fill-textmuted dark:fill-textmuted/50 !important;
}

#file-manager-storage .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  @apply fill-defaulttextcolor !important;
}

#file-manager-storage .apexcharts-datalabels-group {
  @apply translate-y-[-9px];
}

/* End:: file-manager */
/* Start:: landing */
@media (min-width: 992px) {
  [data-nav-style=menu-hover][data-nav-layout=horizontal][class=dark] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1::before {
    @apply border-s-[rgba(0,0,0,0.1)] border-t-[rgba(0,0,0,0.1)];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal][class=dark][dir=rtl] .slide.has-sub.open .slide-menu.child1::before {
    @apply border-e-defaultborder border-s-transparent;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item .side-menu__angle {
    @apply text-defaulttextcolor;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item .side-menu__angle {
    @apply text-menuprimecolor !important;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item:hover, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item.active, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item:hover, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item.active, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item:hover, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item.active {
    @apply text-primary;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item:hover .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item.active .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item:hover .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item.active .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item:hover .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item.active .side-menu__angle {
    @apply text-primary !important;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item:hover:before, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 .slide .side-menu__item.active:before, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item:hover:before, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child2 .slide .side-menu__item.active:before, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item:hover:before, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child3 .slide .side-menu__item.active:before {
    @apply border-primary !important;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item {
    @apply rounded-md px-4 py-[1.35rem];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide-menu.child1 {
    @apply rounded-md px-[0.3rem] py-[0.55rem];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide-menu.child1 .side-menu__item {
    @apply font-medium px-4 py-2;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1 {
    @apply overflow-visible !important;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide.has-sub.open .slide-menu.child1::before {
    @apply content-[""] top-[-7px] w-[13px] h-[13px] z-[99999] border border-t-defaultborder border-s-defaultborder rotate-45 bg-white dark:bg-bodybg border-solid border-transparent start-[10%];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item {
    @apply px-[1.3rem];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item.active,
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item:hover {
    @apply bg-transparent;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .slide-menu.child1 .slide .side-menu__item:before {
    @apply start-[0.65rem] top-[0.838rem];
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item:hover .side-menu__angle {
    @apply text-primary;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__label {
    @apply text-defaulttextcolor !important;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item.active, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item:hover {
    @apply bg-primary text-primary;
  }
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item.active .side-menu__label,
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item.active .side-menu__angle, [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item:hover .side-menu__label,
  [data-nav-style=menu-hover][data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item:hover .side-menu__angle {
    @apply text-primary;
  }
  [data-nav-layout=horizontal] .landing-body .app-sidebar .main-menu > .slide {
    @apply m-0;
  }
}
@media (min-width: 992px) {
  [data-nav-layout=horizontal] .landing-body .landing-page-wrapper .app-sidebar.sticky.sticky-pin {
    @apply fixed;
  }
}
.reviews-container .heading-section {
  @apply relative;
}
.reviews-container .heading-section:before {
  @apply absolute content-[""] w-[200px] bg-center h-[200px] start-[-85px] z-0 bottom-[-38px] rounded-[50%];
}

.reviews-container .box {
  @apply bg-[rgba(255,255,255,0.05)] dark:bg-[rgba(255,255,255,0.05)] border backdrop-blur-[30px] shadow-none mb-0 border-solid border-[rgba(255,255,255,0.1)] dark:border-[rgba(255,255,255,0.1)] !important;
}
.reviews-container .box .card-body {
  @apply text-white;
}

.testimonialSwiperService {
  @apply pt-0 pb-[3.375rem] px-0 !important;
}

.landing-body {
  @apply bg-white dark:bg-bodybg;
}
.landing-body .app-sidebar {
  @apply border-b-0;
}
.landing-body .app-sidebar .side-menu__item {
  @apply px-4 py-[0.8rem];
}
.landing-body .app-sidebar .side-menu__item.active, .landing-body .app-sidebar .side-menu__item:hover {
  @apply text-primary !important;
}
.landing-body .app-sidebar.sticky.sticky-pin .side-menu__item.active {
  @apply font-normal;
}
.landing-body .app-sidebar.sticky.sticky-pin .side-menu__item.active .side-menu__label {
  @apply text-primary !important;
}
.landing-body .app-sidebar.sticky.sticky-pin .side-menu__item.active .side-menu__angle {
  @apply text-primary !important;
}
.landing-body .accordion.accordion-primary .accordion-button.collapsed:after {
  @apply bg-primary/10 text-primary;
}
.landing-body .landing-Features {
  @apply relative w-full h-full bg-primary z-[1] top-0;
}
.landing-body .landing-Features:before {
  @apply bg-[url("../public/assets/images/media/media-80.jpg")] bg-no-repeat bg-cover content-[""] absolute w-full h-full z-[-1] opacity-[0.075] top-0;
}
@media (min-width: 992px) {
  .landing-body .app-sidebar {
    @apply h-auto bg-white dark:bg-bodybg shadow-none px-0 py-2 border-e-0 top-0;
  }
  .landing-body .app-sidebar .main-sidebar {
    @apply h-auto w-full;
  }
  .landing-body.sticky.sticky-pin {
    @apply bg-white dark:bg-bodybg shadow-[0_0.25rem_1rem_black/10];
  }
  .landing-body.sticky.sticky-pin .side-menu__item .side-menu__angle {
    @apply text-menuprimecolor;
  }
  .landing-body.sticky.sticky-pin .side-menu__item:hover .side-menu__angle {
    @apply text-primary !important;
  }
  .landing-body.sticky.sticky-pin.app-sidebar .side-menu__label {
    @apply text-black;
  }
  .landing-body.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-dark,
  .landing-body.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-white {
    @apply hidden;
  }
  .landing-body.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-logo {
    @apply block;
  }
  .landing-body.sticky.sticky-pin.app-sidebar .side-menu__item:hover .side-menu__label {
    @apply text-primary !important;
  }
  .landing-body.app-sidebar .slide.has-sub.open .slide-menu.child1::before {
    @apply border-t-defaultborder border-s-defaultborder !important;
  }
}
.landing-body .app-header {
  @apply hidden;
}
.landing-body .main-sidebar-header {
  @apply block !important;
}
.landing-body .main-menu-container {
  @apply flex items-center justify-between;
}
.landing-body .main-menu-container .slide-left,
.landing-body .main-menu-container .slide-right {
  @apply hidden;
}
.landing-body .main-content {
  @apply min-h-[calc(100vh_-_7.9rem)] p-0;
}

@media (max-width: 991.98px) {
  .landing-body .landing-logo-container .horizontal-logo .header-logo {
    @apply hidden;
  }
  .app-sidebar .side-menu__item.active,
  .app-sidebar .side-menu__item:hover {
    @apply bg-transparent;
  }
  .main-menu-container .main-menu {
    @apply w-full px-0;
  }
  .slide.has-sub.open > .side-menu__item .side-menu__angle {
    @apply rtl:rotate-90;
  }
  .app-sidebar .slide-menu {
    @apply ps-4;
  }
  .app-sidebar .slide {
    @apply p-0;
  }
}
.main-menu-container .main-menu {
  @apply ps-0;
}

.section {
  @apply bg-cover relative px-0 py-[4.375rem];
}

.landing-banner {
  @apply relative w-full bg-primary z-[1] top-0;
}
.landing-banner::before {
  @apply content-[""] absolute w-full h-full bg-[url(../public/assets/images/media/media-88.jpg)] bg-cover bg-center bg-no-repeat opacity-10 z-[-1];
}
.landing-banner .landing-banner-heading {
  @apply text-[2.1rem] font-medium text-defaulttextcolor;
}

@media (max-width: 767.98px) {
  .landing-main-image {
    @apply hidden;
  }
  .landing-banner .main-banner-container {
    @apply p-4;
  }
}
@media (max-width: 1115.98px) {
  .landing-main-image::before, .landing-main-image::after {
    @apply hidden;
  }
}
@media (max-width: 480px) {
  .landing-banner .section {
    @apply px-0 pb-[2.375rem] pt-[4.375rem];
  }
}
.landing-main-image {
  @apply relative z-10;
}
.landing-main-image:before {
  @apply absolute content-[""] w-[25rem] h-[25rem] bg-white dark:bg-bodybg opacity-[0.07] z-[-1] rounded-[50%] start-[116px] -top-9;
}
.landing-main-image img {
  @apply z-[11] relative -mt-2.5;
}

.landing-page-wrapper {
  @apply relative min-h-[calc(100vh_-_3.4rem)];
}

.app-sidebar .side-menu__label {
  @apply font-medium;
}

.landing-section-heading {
  @apply relative font-semibold overline;
}

.landing-footer {
  @apply bg-[rgb(25,32,56)] border-b-[rgba(255,255,255,0.05)] border-b border-solid;
}
.landing-footer .landing-footer-list li {
  @apply mb-2;
}
.landing-footer .landing-footer-list li:last-child {
  @apply mb-0;
}
.landing-footer .landing-footer-logo {
  @apply h-[1.7rem] leading-[1.7rem];
}

.landing-main-footer {
  @apply bg-[rgb(25,32,56)];
}

.section-bg {
  @apply bg-primary/10;
}

.box.landing-card .card-body {
  @apply p-8;
}

.sub-card-companies img {
  @apply bg-[rgba(255,255,255,0.1)] h-[77px] p-5 rounded-[50%] border-s-[rgba(255,255,255,0.15)] border-s border-solid;
}

@media (min-width: 992px) {
  [class=dark] .landing-body .landing-logo-container .horizontal-logo .desktop-logo {
    @apply hidden;
  }
  [class=dark] .landing-body .landing-logo-container .horizontal-logo .desktop-dark,
  [class=dark] .landing-body .landing-logo-container .horizontal-logo .desktop-white {
    @apply block;
  }
}
@media (max-width: 991.98px) {
  [class=dark] .landing-body .app-header .main-header-container .horizontal-logo .header-logo .desktop-logo,
  [class=dark] .landing-body .app-header .main-header-container .horizontal-logo .header-logo .desktop-dark {
    @apply hidden;
  }
  [class=dark] .landing-body .app-header .main-header-container .horizontal-logo .header-logo .desktop-white {
    @apply block;
  }
}
[class=dark] .landing-body .app-sidebar.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-dark,
[class=dark] .landing-body .app-sidebar.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-logo {
  @apply hidden;
}
[class=dark] .landing-body .app-sidebar.sticky.sticky-pin .landing-logo-container .horizontal-logo .desktop-white {
  @apply block;
}
[class=dark] .section-bg {
  @apply bg-primary/10;
}

@media (max-width: 420px) {
  .landing-body .landing-banner .main-banner-container {
    @apply p-4;
  }
  .landing-body .landing-banner .landing-banner-heading {
    @apply text-[2rem];
  }
}
@media (max-width: 992px) {
  .landing-body .app-sidebar .slide-menu.child1 li,
  .landing-body .app-sidebar .slide-menu.child2 li,
  .landing-body .app-sidebar .slide-menu.child3 li {
    @apply relative p-0;
  }
}
/* Jobs Landing  */
.custom-form-group {
  @apply relative flex items-center;
}

.custom-form-group .form-control {
  @apply ps-5 pe-28;
}

.custom-form-group .form-control-lg ~ .custom-form-btn {
  @apply end-[0.7rem];
}

.custom-form-group .custom-form-btn {
  @apply absolute flex items-center justify-center rounded-[0.3rem] end-2;
}

.landing-body .landing-main-footer .landing-footer-list li:not(:first-child)::before {
  @apply absolute content-[""] w-[0.3rem] h-[0.6rem] bg-transparent start-[-0.2rem] border-s-[rgba(255,255,255,0.2)] border-s border-solid top-[0.35rem];
}
.landing-body .landing-main-footer .landing-footer-list li:not(:first-child)::before .review-quote {
  @apply absolute text-3xl leading-[0] text-primary/70 bg-transparent p-2.5 end-[0.8rem] top-4;
}

.landing-body .landing-main-footer .landing-footer-list li:not(:first-child) {
  @apply relative;
}

/* Jobs Landing  */
.landing-body .landing-main-footer .landing-footer-list li {
  @apply inline-block px-3 py-0;
}

@media (max-width: 991.98px) {
  .landing-body .main-content {
    @apply pt-[0rem];
  }
  .landing-body .animated-arrow span {
    @apply top-[1.15rem];
  }
  .landing-body .app-header {
    @apply block;
  }
}
.landing-body .animated-arrow {
  @apply z-[8];
}

@media (min-width: 992px) {
  .landing-body .slide.has-sub.open > .side-menu__item .side-menu__angle {
    @apply rotate-[270deg];
  }
}
@media (min-width: 992px) {
  .landing-body .app-sidebar {
    @apply w-full !important;
  }
}
@media (min-width: 992px) {
  [data-nav-layout=horizontal] .landing-body .app-sidebar {
    @apply top-0 !important;
  }
  [data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__label {
    @apply text-[#61748f] dark:text-white/60;
  }
}
@media (max-width: 991.98px) {
  .app-sidebar {
    @apply fixed !important;
  }
}
/* End:: landing */
/* Start:: mail */
/* Start:: mail-app */
.main-mail-container {
  @apply relative overflow-hidden;
}

.mail-navigation,
.total-mails,
.mail-recepients {
  @apply bg-white dark:bg-bodybg h-[calc(100vh_-_10rem)] overflow-hidden rounded-md;
}

.mail-info-body {
  @apply max-h-[calc(100vh_-_8.3rem)];
}

@media (min-width: 1400px) {
  .total-mails .mail-msg .mail-msg-content {
    @apply inline-block;
  }
}
@media (min-width: 1400px) and (max-width: 1489.98px) {
  .responsive-mail-action-icons .dropdown {
    @apply block;
  }
  .responsive-mail-action-icons .close-button {
    @apply hidden;
  }
  .mail-action-icons {
    @apply hidden;
  }
}
@media (max-width: 1399.98px) {
  .responsive-mail-action-icons {
    @apply block flex;
  }
  .mail-action-icons {
    @apply hidden !important;
  }
}
@media (max-width: 575.98px) {
  .mail-recepients {
    @apply hidden;
  }
}
@media (min-width: 1489px) {
  .responsive-mail-action-icons {
    @apply hidden;
  }
}
@media (max-width: 991.98px) {
  .mail-navigation {
    @apply w-full;
  }
}
@media (min-width: 992px) {
  .mail-navigation {
    @apply min-w-[20rem] max-w-xs;
  }
}
.total-mails {
  @apply w-full;
}
.total-mails .mail-messages {
  @apply max-h-[calc(100vh_-_19.5rem)];
}
.total-mails .mail-messages li {
  @apply pt-[1.1rem] pb-2.5 px-4 border-b border-solid border-defaultborder dark:border-defaultborder/10;
}
.total-mails .mail-messages li.active, .total-mails .mail-messages li:hover {
  @apply bg-primary/[0.05];
}
.total-mails .mail-messages li:last-child {
  @apply border-b-0;
}
.total-mails .mail-messages li .avatar.mail-msg-avatar {
  @apply w-8 h-8;
}
.total-mails .mail-messages li .avatar.mail-msg-avatar.online:before, .total-mails .mail-messages li .avatar.mail-msg-avatar.offline:before {
  @apply w-[0.55rem] h-[0.55rem];
}
.total-mails .mail-messages li .mail-msg .mail-starred i {
  @apply text-textmuted dark:text-textmuted/50 opacity-50;
}
.total-mails .mail-messages li .mail-msg .mail-starred.true i {
  @apply text-warning opacity-100;
}

.mail-recepients {
  @apply min-w-[4.4rem] max-w-[4.4rem];
}
.mail-recepients .total-mail-recepients {
  @apply max-h-[calc(100vh_-_13.4rem)];
}
.mail-recepients .mail-recepeint-person .avatar {
  @apply w-8 h-8 mb-4;
}
.mail-recepients .mail-recepeint-person .avatar.online:before, .mail-recepients .mail-recepeint-person .avatar.offline:before {
  @apply w-[0.55rem] h-[0.55rem];
}
.mail-recepients .mail-recepeint-person:last-child {
  @apply mb-0;
}

.mail-navigation ul.mail-main-nav {
  @apply max-h-[calc(100vh_-_14.5rem)] mb-0 p-4;
}
.mail-navigation ul.mail-main-nav li {
  @apply rounded-md font-medium px-[0.8rem] py-[0.55rem];
}
.mail-navigation ul.mail-main-nav li div {
  @apply text-textmuted dark:text-textmuted/50;
}
.mail-navigation ul.mail-main-nav li.active {
  @apply bg-primary/10;
}
.mail-navigation ul.mail-main-nav li.active div {
  @apply text-primary font-semibold;
}
.mail-navigation ul.mail-main-nav li:hover div {
  @apply text-primary;
}

@media (min-width: 576px) {
  .mail-msg-content {
    @apply w-full;
  }
}
@media (max-width: 575.98px) {
  .mail-msg-content {
    @apply max-w-[180px];
  }
}
.mails-information {
  @apply w-full;
}
.mails-information .mail-info-header {
  @apply border-b-defaultborder p-3 border-b border-solid dark:border-defaultborder/10;
}
.mails-information .mail-info-footer {
  @apply border-t-defaultborder p-3 border-t border-solid dark:border-defaultborder/10;
}
.mails-information .mail-attachment {
  @apply w-48 h-11 border border-defaultborder rounded-md flex items-center p-1 border-solid;
}
.mails-information .mail-attachment .attachment-icon svg,
.mails-information .mail-attachment .attachment-icon i {
  @apply w-6 h-6 text-[2rem] me-2;
}
.mails-information .mail-attachment .attachment-name {
  @apply max-w-[7rem] inline-block text-xs font-medium;
}

.mail-reply .ql-toolbar.ql-snow .ql-formats {
  @apply my-[5px];
}

#mail-compose-editor .ql-editor {
  @apply min-h-[12.62rem] !important;
}

.mail-compose .ql-toolbar.ql-snow .ql-formats {
  @apply my-[5px];
}

.ti-offcanvas.ti-offcanvas-right.mail-info-offcanvas {
  @apply max-w-[50rem];
}

/* End:: mail-app */
/* Start:: mail-settings */
.mail-notification-settings, .mail-security-settings {
  @apply w-3/5;
}

@media (max-width: 575.98px) {
  #account-settings .btn-group label {
    @apply text-[0.625rem];
  }
}
.choices__list--dropdown .choices__item--selectable,
.choices__list[aria-expanded] .choices__item--selectable {
  @apply p-2.5 !important;
}

.choices__list--dropdown .choices__item--selectable::after,
.choices__list[aria-expanded] .choices__item--selectable::after {
  @apply hidden;
}

.mail-settings-tab.nav-tabs-header .nav-item .nav-link {
  @apply px-[0.8rem] py-[0.65rem] border-0;
}

.mail-settings-tab.nav-tabs-header .nav-item .nav-link:hover, .mail-settings-tab.nav-tabs-header .nav-item .nav-link:focus {
  @apply border-0;
}

/* End:: mail-settings */
/* End:: mail */
/* Start:: task */
/* Start::task-kanboard-board */
.TASK-kanban-board {
  @apply flex overflow-x-auto items-stretch mb-3 pb-4;
}
.TASK-kanban-board .kanban-tasks-type {
  @apply min-w-[20rem] w-full me-2;
}
.TASK-kanban-board .kanban-tasks-type .kanban-tasks .card {
  @apply touch-none;
}
.TASK-kanban-board::-webkit-scrollbar-thumb {
  @apply bg-dark/10 rounded-[0.3125rem];
}
.TASK-kanban-board::-webkit-scrollbar-track {
  @apply rounded-[0.3125rem];
}
.TASK-kanban-board .task-image .kanban-image {
  @apply h-[150px] w-full;
}
.TASK-kanban-board .kanban-content {
  @apply mt-3;
}
.TASK-kanban-board .kanban-task-description {
  @apply text-textmuted dark:text-textmuted/50 mb-[1rem];
}
.TASK-kanban-board .kanban-tasks-type.new .kanban-tasks .box {
  @apply border border-primary/20 border-solid;
}
.TASK-kanban-board .kanban-tasks-type.todo .kanban-tasks .box {
  @apply border border-primarytint1color/20 border-solid;
}
.TASK-kanban-board .kanban-tasks-type.in-progress .kanban-tasks .box {
  @apply border border-primarytint2color/20 border-solid;
}
.TASK-kanban-board .kanban-tasks-type.inreview .kanban-tasks .box {
  @apply border border-primarytint3color/20 border-solid;
}
.TASK-kanban-board .kanban-tasks-type.completed .kanban-tasks .box {
  @apply border border-secondary/20 border-solid;
}
.TASK-kanban-board #new-tasks .box:last-child, .TASK-kanban-board #todo-tasks .box:last-child, .TASK-kanban-board #inprogress-tasks .box:last-child, .TASK-kanban-board #inreview-tasks .box:last-child, .TASK-kanban-board #completed-tasks .box:last-child {
  @apply mb-0;
}
.TASK-kanban-board #new-tasks, .TASK-kanban-board #todo-tasks, .TASK-kanban-board #inprogress-tasks, .TASK-kanban-board #inreview-tasks, .TASK-kanban-board #completed-tasks {
  @apply relative max-h-[35rem];
}
.TASK-kanban-board #new-tasks .simplebar-content, .TASK-kanban-board #todo-tasks .simplebar-content, .TASK-kanban-board #inprogress-tasks .simplebar-content, .TASK-kanban-board #inreview-tasks .simplebar-content, .TASK-kanban-board #completed-tasks .simplebar-content {
  @apply ps-0 pe-4 py-0 !important;
}
.TASK-kanban-board .task-Null {
  @apply relative min-h-[12.5rem];
}
.TASK-kanban-board .task-Null::before {
  @apply absolute content-[""] bg-white dark:bg-bodybg bg-[url(../public/assets/images/media/media-92.svg)] bg-cover bg-center h-[12.5rem] w-full mx-auto my-0 rounded-[0.3rem] inset-0;
}
.TASK-kanban-board .view-more-button {
  @apply me-4;
}

/* end::task-kanboard-board */
/* Start::task-details */
.task-details-key-tasks {
  @apply list-decimal;
}
.task-details-key-tasks li {
  @apply text-textmuted dark:text-textmuted/50 mb-2;
}
.task-details-key-tasks li:last-child {
  @apply mb-0;
}

.task-description {
  @apply text-sm;
}

/* End::task-details */
/* End:: task */
/* TAILWIND */
.accordion-button {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor py-3 px-4 text-[0.85rem] font-medium;
}
.accordion-button:not(.collapsed) {
  @apply text-defaulttextcolor shadow-none;
}
.accordion-button:focus {
  @apply bg-defaultbackground dark:bg-light text-defaulttextcolor shadow-none;
}

.accordion-body {
  @apply py-3 px-4 text-[0.8125rem] text-textmuted dark:text-textmuted/50;
}

.accordion-item {
  @apply text-defaulttextcolor bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 dark:text-defaulttextcolor/70;
}

.accordions-items-seperate .accordion-item:not(:first-of-type) {
  @apply mt-2;
}
.accordions-items-seperate .accordion-item {
  @apply border-t border-solid border-defaultborder dark:border-defaultborder/10 rounded-md overflow-hidden;
}

.accordion-item:last-of-type {
  @apply rounded-b-[0.35rem];
}

.accordion-item:first-of-type {
  @apply rounded-t-[0.35rem];
}

.accordion.accordion-flush .accordion-button {
  @apply pe-[2.5rem];
}
.accordion.accordion-flush .accordion-button:after {
  @apply start-[98.5rem] !important;
}

/* Start:: light colored accordions */
.accordion.accordion-primary .accordion-button {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor py-3 px-4 text-[0.85rem] font-medium;
}
.accordion.accordion-primary .accordion-button:focus {
  @apply bg-primary/10 text-primary;
}
.accordion.accordion-primary .accordion-button:after, .accordion.accordion-primary .accordion-button:not(.collapsed)::after {
  @apply bg-none;
}
.accordion.accordion-primary .accordion-button:after {
  @apply bg-primary text-white;
}
.accordion.accordion-primary .accordion-button.collapsed {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70;
}
.accordion.accordion-primary .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor dark:text-defaulttextcolor/70;
}
.accordion.accordion-secondary .accordion-button {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor py-3 px-4 text-[0.85rem] font-medium;
}
.accordion.accordion-secondary .accordion-button:focus {
  @apply bg-secondary/10 text-secondary;
}
.accordion.accordion-secondary .accordion-button:after, .accordion.accordion-secondary .accordion-button:not(.collapsed)::after {
  @apply bg-none;
}
.accordion.accordion-secondary .accordion-button:after {
  @apply bg-secondary text-white;
}
.accordion.accordion-secondary .accordion-button.collapsed {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor;
}
.accordion.accordion-secondary .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-success .accordion-button {
  @apply bg-success/10 text-success;
}
.accordion.accordion-success .accordion-button:after {
  @apply bg-success text-white;
}
.accordion.accordion-success .accordion-button.collapsed {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor;
}
.accordion.accordion-success .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-danger .accordion-button {
  @apply bg-danger/10 text-danger;
}
.accordion.accordion-danger .accordion-button:after {
  @apply bg-danger text-white;
}
.accordion.accordion-danger .accordion-button.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-danger .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-warning .accordion-button {
  @apply bg-warning/10 text-warning;
}
.accordion.accordion-warning .accordion-button:after {
  @apply bg-warning text-white;
}
.accordion.accordion-warning .accordion-button.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-warning .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-info .accordion-button {
  @apply bg-info/10 text-info;
}
.accordion.accordion-info .accordion-button:after {
  @apply bg-info text-white;
}
.accordion.accordion-info .accordion-button.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-info .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}

/* End:: light colored accordions */
/* Start:: solid colored accordions */
.accordion.accordion-solid-primary .accordion-button {
  @apply bg-white text-defaulttextcolor py-3 px-4 text-[0.85rem] font-medium;
}
.accordion.accordion-solid-primary .accordion-button:focus {
  @apply bg-primary text-white;
}
.accordion.accordion-solid-primary .accordion-button:after {
  @apply bg-white text-primary;
}
.accordion.accordion-solid-primary .accordion-button.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-primary .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-solid-secondary .accordion-button {
  @apply bg-white text-defaulttextcolor py-3 px-4 text-[0.85rem] font-medium;
}
.accordion.accordion-solid-secondary .accordion-button:focus {
  @apply bg-secondary text-white;
}
.accordion.accordion-solid-secondary .accordion-button:after {
  @apply bg-white text-secondary;
}
.accordion.accordion-solid-secondary .accordion-button.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-secondary .accordion-button.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-solid-success {
  @apply bg-success text-white;
}
.accordion.accordion-solid-success:after {
  @apply bg-white text-success;
}
.accordion.accordion-solid-success.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-success.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-solid-danger {
  @apply bg-danger text-white;
}
.accordion.accordion-solid-danger:after {
  @apply bg-white text-danger;
}
.accordion.accordion-solid-danger.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-danger.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-solid-warning {
  @apply bg-danger text-white;
}
.accordion.accordion-solid-warning:after {
  @apply bg-white text-danger;
}
.accordion.accordion-solid-warning.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-warning.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}
.accordion.accordion-solid-info {
  @apply bg-info text-white;
}
.accordion.accordion-solid-info:after {
  @apply bg-white text-info;
}
.accordion.accordion-solid-info.collapsed {
  @apply bg-white text-defaulttextcolor;
}
.accordion.accordion-solid-info.collapsed:after {
  @apply bg-defaultbackground text-defaulttextcolor;
}

/* End:: solid colored accordions */
/* Start:: border colors */
.accordion.accordion-border-primary .accordion-item {
  @apply border border-solid border-primary;
}
.accordion.accordion-border-primary .accordion-button {
  @apply bg-white dark:bg-bodybg text-primary border-b-0;
}
.accordion.accordion-border-primary .accordion-button:after {
  @apply bg-white text-primary border border-solid border-primary;
}
.accordion.accordion-border-primary .accordion-button:not(.collapsed) {
  @apply border-solid border-primary;
}
.accordion.accordion-border-success .accordion-item {
  @apply border border-solid border-success;
}
.accordion.accordion-border-success .accordion-button {
  @apply bg-white dark:bg-bodybg text-success border-b-0;
}
.accordion.accordion-border-success .accordion-button:after {
  @apply bg-white dark:bg-bodybg text-success border border-solid border-success;
}
.accordion.accordion-border-success .accordion-button:not(.collapsed) {
  @apply border-solid border-success;
}

/* End:: border colors */
/* Start:: no icon */
.accordion.accordionicon-none .accordion-button:after {
  @apply hidden;
}

/* Start:: no icon */
/* Start:: left aligned icon */
.accordion.accordionicon-left .accordion-button:after {
  @apply absolute start-[1.935rem];
}

/* End:: left aligned icon */
/* Start:: custom icons */
.accordion.accordion-customicon1 .accordion-button:after {
  @apply content-[""] !important;
}
.accordion.accordion-customicon1 .accordion-button:not(.collapsed)::after {
  @apply content-[""] !important;
}

/* End:: custom icons */
/* Start:: customized accordion */
.customized-accordion .accordion-item.custom-accordion-primary .accordion-button {
  @apply bg-primary/10 border-s-[0.25rem] border-solid border-primary/60;
}
.customized-accordion .accordion-item.custom-accordion-primary .accordion-button:after {
  @apply bg-primary/60 text-white;
}
.customized-accordion .accordion-item.custom-accordion-primary .accordion-body {
  @apply bg-primary/10 pt-0 border-s-[0.25rem] border-solid border-primary/60;
}
.customized-accordion .accordion-item.custom-accordion-secondary .accordion-button {
  @apply bg-secondary/10 border-s-[0.25rem] border-solid border-secondary/60;
}
.customized-accordion .accordion-item.custom-accordion-secondary .accordion-button:after {
  @apply bg-secondary/60 text-white;
}
.customized-accordion .accordion-item.custom-accordion-secondary .accordion-body {
  @apply bg-secondary/10 pt-0 border-s-[0.25rem] border-solid border-secondary/60;
}
.customized-accordion .accordion-item.custom-accordion-danger .accordion-button {
  @apply bg-danger/10 border-s-[0.25rem] border-solid border-danger/60;
}
.customized-accordion .accordion-item.custom-accordion-danger .accordion-button:after {
  @apply bg-danger/60 text-white;
}
.customized-accordion .accordion-item.custom-accordion-danger .accordion-body {
  @apply bg-danger/10 pt-0 border-s-[0.25rem] border-solid border-danger/60;
}

/* End:: customized accordion */
.alert {
  @apply py-[0.625rem] px-[0.85rem] border rounded-md text-[0.8125rem] items-center;
}
.alert.alert-dismissible {
  @apply py-[0.625rem] px-[0.85rem];
}
.alert .btn-close {
  @apply bg-none p-[0.95rem] flex items-center justify-center;
}
.alert .btn-close i {
  @apply text-[1.5rem] leading-[1.5];
}
.alert:last-child {
  @apply mb-0;
}
.alert .alert-link {
  @apply font-semibold;
}

/* Basic Alerts */
.alert-warning {
  @apply bg-warning/10 text-warning border-warning/10;
}
.alert-warning .alert-link {
  @apply text-warning;
}
.alert-warning .btn-close {
  @apply text-warning;
}
.alert-warning .btn-close.custom-close {
  @apply text-warning opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-primary {
  @apply bg-primary/10 text-primary border-primary/10;
}
.alert-primary .alert-link {
  @apply text-primary;
}
.alert-primary .btn-close {
  @apply text-primary;
}
.alert-primary .btn-close.custom-close {
  @apply text-primary opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-primary1 {
  @apply bg-primarytint1color/10 text-primarytint1color border-primarytint1color/10;
}
.alert-primary1 .alert-link {
  @apply text-primarytint1color;
}
.alert-primary1 .btn-close {
  @apply text-primarytint1color;
}
.alert-primary1 .btn-close.custom-close {
  @apply text-primarytint1color opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-primary2 {
  @apply bg-primarytint3color/10 text-primarytint2color border-primarytint2color/10;
}
.alert-primary2 .alert-link {
  @apply text-primarytint2color;
}
.alert-primary2 .btn-close {
  @apply text-primarytint2color;
}
.alert-primary2 .btn-close.custom-close {
  @apply text-primarytint2color opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-primary3 {
  @apply bg-primarytint3color/10 text-primarytint3color border-primarytint3color/10;
}
.alert-primary3 .alert-link {
  @apply text-primarytint3color;
}
.alert-primary3 .btn-close {
  @apply text-primarytint3color;
}
.alert-primary3 .btn-close.custom-close {
  @apply text-primarytint3color opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-secondary {
  @apply bg-secondary/10 text-secondary border-secondary/10;
}
.alert-secondary .alert-link {
  @apply text-secondary;
}
.alert-secondary .btn-close {
  @apply text-secondary;
}
.alert-secondary .btn-close.custom-close {
  @apply text-secondary opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-success {
  @apply bg-success/10 text-success border-success/10;
}
.alert-success .alert-link {
  @apply text-success;
}
.alert-success .btn-close {
  @apply text-success;
}
.alert-success .btn-close.custom-close {
  @apply text-success opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-info {
  @apply bg-info/10 text-info border-info/10;
}
.alert-info .alert-link {
  @apply text-info;
}
.alert-info .btn-close {
  @apply text-info;
}
.alert-info .btn-close.custom-close {
  @apply text-info opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-danger {
  @apply bg-danger/10 text-danger border-danger/10;
}
.alert-danger .alert-link {
  @apply text-danger;
}
.alert-danger .btn-close {
  @apply text-danger;
}
.alert-danger .btn-close.custom-close {
  @apply text-danger opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-light {
  @apply bg-light text-defaultsize border-light;
}
.alert-light .alert-link {
  @apply text-defaultsize;
}
.alert-light .btn-close {
  @apply text-defaultsize;
}
.alert-light .btn-close.custom-close {
  @apply text-defaulttextcolor opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

.alert-dark {
  @apply bg-black/10 text-black dark:text-defaulttextcolor/70 border-black/10;
}
.alert-dark .alert-link {
  @apply text-black dark:text-defaulttextcolor/70;
}
.alert-dark .btn-close {
  @apply text-black dark:text-defaulttextcolor/70;
}
.alert-dark .btn-close.custom-close {
  @apply text-black dark:text-defaulttextcolor/70 opacity-[1] rounded-[50px] shadow p-[0.85rem];
}

/* Basic Alerts */
/* Solid Colored Alerts */
.alert-solid-primary {
  @apply bg-primary text-white border-primary;
}
.alert-solid-primary .btn-close {
  @apply text-white;
}

.alert-solid-primary1 {
  @apply bg-primarytint1color text-white border-primarytint1color;
}
.alert-solid-primary1 .btn-close {
  @apply text-white;
}

.alert-solid-primary2 {
  @apply bg-primarytint2color text-white border-primarytint2color;
}
.alert-solid-primary2 .btn-close {
  @apply text-white;
}

.alert-solid-primary3 {
  @apply bg-primarytint3color text-white border-primarytint3color;
}
.alert-solid-primary3 .btn-close {
  @apply text-white;
}

.alert-solid-secondary {
  @apply bg-secondary text-white border-secondary;
}
.alert-solid-secondary .btn-close {
  @apply text-white;
}

.alert-solid-warning {
  @apply bg-warning text-white border-warning;
}
.alert-solid-warning .btn-close {
  @apply text-white;
}

.alert-solid-info {
  @apply bg-info text-white border-info;
}
.alert-solid-info .btn-close {
  @apply text-white;
}

.alert-solid-success {
  @apply bg-success text-white border-success;
}
.alert-solid-success .btn-close {
  @apply text-white;
}

.alert-solid-danger {
  @apply bg-danger text-white border-danger;
}
.alert-solid-danger .btn-close {
  @apply text-white;
}

.alert-solid-light {
  @apply bg-light text-defaulttextcolor border-light;
}
.alert-solid-light .btn-close {
  @apply text-defaulttextcolor;
}

.alert-solid-dark {
  @apply bg-black text-white border-black;
}
.alert-solid-dark .btn-close {
  @apply text-white;
}

/* Solid Colored Alerts */
/* Outline Alerts */
.alert-outline-primary {
  @apply text-primary border-primary;
}
.alert-outline-primary .btn-close {
  @apply text-primary;
}

.alert-outline-secondary {
  @apply text-secondary border-secondary;
}
.alert-outline-secondary .btn-close {
  @apply text-secondary;
}

.alert-outline-info {
  @apply text-info border-info;
}
.alert-outline-info .btn-close {
  @apply text-info;
}

.alert-outline-warning {
  @apply text-warning border-warning;
}
.alert-outline-warning .btn-close {
  @apply text-warning;
}

.alert-outline-success {
  @apply text-success border-success;
}
.alert-outline-success .btn-close {
  @apply text-success;
}

.alert-outline-danger {
  @apply text-danger border-danger;
}
.alert-outline-danger .btn-close {
  @apply text-danger;
}

.alert-outline-light {
  @apply text-defaulttextcolor border-light;
}
.alert-outline-light .btn-close {
  @apply text-light;
}

.alert-outline-dark {
  @apply text-black dark:text-defaulttextcolor/70 border-black dark:border-white;
}
.alert-outline-dark .btn-close {
  @apply text-light;
}

/* Outline Alerts */
/* Customized Alerts */
.alert-primary.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-primary border-solid text-[#8c9097] bg-white dark:bg-bodybg border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-primary.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-secondary.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-secondary border-solid text-[#8c9097] bg-white dark:bg-bodybg dark:border-defaultborder/10 border border-defaultborder text-[0.813rem];
}
.alert-secondary.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-warning.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-warning border-solid bg-white dark:bg-bodybg text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-warning.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-danger.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-danger border-solid bg-white dark:bg-bodybg text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-danger.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-success.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-success border-solid bg-white dark:bg-bodybg text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-success.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-info.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-info border-solid bg-white dark:bg-bodybg text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-info.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-light.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-light border-solid text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-light.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

.alert-dark.custom-alert-icon {
  @apply border-s-[0.313rem] border-s-black border-solid text-[#8c9097] border dark:border-defaultborder/10 border-defaultborder text-[0.813rem];
}
.alert-dark.custom-alert-icon .btn-close {
  @apply text-[#8c9097];
}

/* Customized Alerts */
/* Customized Alerts1 */
.custom-alert1 {
  @apply me-0 bg-white dark:bg-bodybg border-0 p-[1.25rem] text-defaulttextcolor;
}
.custom-alert1 p {
  @apply mb-[2.5rem] text-[#8c9097] text-[0.8rem];
}
.custom-alert1 .custom-alert-icon {
  @apply w-[3.125rem] h-[3.125rem] mb-[0.85rem];
}
.custom-alert1 .btn-close {
  @apply p-0 mb-4;
}
.custom-alert1.alert-primary {
  @apply border-t-[0.313rem] border-solid border-primary;
}
.custom-alert1.alert-secondary {
  @apply border-t-[0.313rem] border-solid border-secondary;
}
.custom-alert1.alert-warning {
  @apply border-t-[0.313rem] border-solid border-warning;
}
.custom-alert1.alert-danger {
  @apply border-t-[0.313rem] border-solid border-danger;
}

/* Customized Alerts1 */
/* Image alerts */
.alert-img {
  @apply flex items-center;
}
.alert-img .avatar {
  @apply border border-solid border-black/10;
}

/* Image alerts */
/* Start Avatar Styles */
.avatar {
  @apply relative h-[2.625rem] w-[2.625rem] inline-flex items-center justify-center rounded-sm font-medium mb-1;
}
.avatar a.badge:hover {
  @apply text-white;
}
.avatar img {
  @apply w-full h-full rounded-md;
}
.avatar .avatar-rounded {
  @apply rounded-full !important;
}
.avatar .avatar-rounded img {
  @apply rounded-full !important;
}
.avatar.avatar-radius-0 {
  @apply rounded-none;
}
.avatar.avatar-radius-0 img {
  @apply rounded-none;
}
.avatar .avatar-badge {
  @apply absolute -top-[4%] -end-[0.375rem] w-[1.4rem] h-[1.4rem] text-[0.625rem] border-2 border-solid border-white rounded-full flex items-center justify-center !important;
}
.avatar.online, .avatar.offline {
  @apply relative before:absolute before:w-[0.75rem] before:h-[0.75rem] before:rounded-full before:end-0 before:bottom-0 before:border-[2px] before:border-solid before:border-white before:dark:border-black;
}
.avatar.online:before {
  @apply bg-success;
}
.avatar.offline:before {
  @apply bg-gray-500;
}
.avatar.avatar-xs {
  @apply w-[1.25rem] h-[1.25rem] leading-[1.25rem] text-[0.65rem];
}
.avatar.avatar-xs .avatar-badge {
  @apply p-[0.25rem] w-[1rem] h-[1rem] leading-[1rem] text-[0.5rem] -top-[25%] -end-[0.5rem] !important;
}
.avatar.avatar-xs.online, .avatar.avatar-xs.offline {
  @apply before:w-[0.5rem] before:h-[0.5rem];
}
.avatar.avatar-sm {
  @apply w-[1.75rem] h-[1.75rem] leading-[1.75rem] text-[0.65rem];
}
.avatar.avatar-sm .avatar-badge {
  @apply p-[0.3rem] w-[1.1rem] h-[1.1rem] leading-[1.1rem] text-[0.5rem] -top-[38%] -end-[0.5rem] !important;
}
.avatar.avatar-sm.online, .avatar.avatar-sm.offline {
  @apply before:w-[0.5rem] before:h-[0.5rem];
}
.avatar.avatar-md {
  @apply w-[2.5rem] h-[2.5rem] leading-[2.5rem] text-[0.8rem];
}
.avatar.avatar-md .avatar-badge {
  @apply p-[0.4rem] w-[1.2rem] h-[1.2rem] leading-[1.2rem] text-[0.65rem] -top-[6%] -end-[13%] !important;
}
.avatar.avatar-md.online, .avatar.avatar-md.offline {
  @apply before:w-[0.75rem] before:h-[0.75rem];
}
.avatar.avatar-md svg {
  @apply w-[1.5rem] h-[1.5rem];
}
.avatar.avatar-lg {
  @apply w-[3rem] h-[3rem] leading-[3rem] text-[1rem];
}
.avatar.avatar-lg .avatar-badge {
  @apply -top-[15%] -end-[0.25%];
}
.avatar.avatar-lg.online, .avatar.avatar-lg.offline {
  @apply before:w-[0.8rem] before:h-[0.8rem];
}
.avatar.avatar-lg svg {
  @apply w-[1.8rem] h-[1.8rem];
}
.avatar.avatar-xl {
  @apply w-[4rem] h-[4rem] leading-[4rem] text-[1.25rem];
}
.avatar.avatar-xl .avatar-badge {
  @apply -top-[8%] -end-[0.2%];
}
.avatar.avatar-xl.online, .avatar.avatar-xl.offline {
  @apply before:w-[0.95rem] before:h-[0.95rem];
}
.avatar.avatar-xxl {
  @apply w-[5rem] h-[5rem] leading-[5rem] text-[1.5rem];
}
.avatar.avatar-xxl .avatar-badge {
  @apply -top-[4%] -end-[0%];
}
.avatar.avatar-xxl.online, .avatar.avatar-xxl.offline {
  @apply before:w-[1.05rem] before:h-[1.05rem] bottom-1;
}

.avatar-rounded {
  @apply rounded-full !important;
}
.avatar-rounded img {
  @apply rounded-full !important;
}

/* Start:: Breadcrumb Styles */
.breadcrumb {
  @apply mb-2 flex;
}
.breadcrumb .breadcrumb-item a {
  @apply text-primary !important;
}
.breadcrumb .breadcrumb-item.active {
  @apply text-defaulttextcolor font-medium;
}

.breadcrumb-item + .breadcrumb-item {
  @apply ps-2;
}

.breadcrumb-item + .breadcrumb-item {
  @apply before:content-["/"] before:text-textmuted dark:before:text-textmuted/50 before:me-2;
}

.breadcrumb-example1 .breadcrumb-item + .breadcrumb-item {
  @apply before:content-[""] before:text-textmuted dark:before:text-textmuted/50 before:me-0 !important;
}

.breadcrumb-example2 .breadcrumb-item + .breadcrumb-item {
  @apply before:content-["~"] before:text-textmuted dark:before:text-textmuted/50;
}

.breadcrumb-style1 .breadcrumb-item + .breadcrumb-item::before {
  @apply text-textmuted dark:text-textmuted/50 content-["->"];
}

.breadcrumb-style2 .breadcrumb-item + .breadcrumb-item::before {
  @apply text-textmuted dark:text-textmuted/50 content-[""];
}

.breadcrumb-style3 .breadcrumb-item + .breadcrumb-item::before {
  @apply text-textmuted dark:text-textmuted/50 content-[""];
}

.embedded-breadcrumb:before {
  @apply opacity-[0.7] before:content-[""];
}

.dark .embedded-breadcrumb:before {
  @apply invert-[1];
}

/* End:: Breadcrumb Styles */
/* Start:: Page-header Breadcrumb Styles */
.page-header-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  @apply content-[""] font-tabler !important;
}

.page-header-breadcrumb {
  @apply my-7;
}

.breadcrumb-input {
  @apply min-w-[12.5rem] py-[0.375rem] px-2 !important;
}

.page-header-breadcrumb .form-group {
  @apply shadow-defaultshadow;
}

.page-header-breadcrumb .ti-btn {
  @apply shadow-defaultshadow;
}

/* End:: Page-header Breadcrumb Styles */
/* Start Buttons Styles */
.ti-btn {
  @apply py-[0.375rem] px-3 inline-flex justify-center items-center gap-2 rounded-sm border border-transparent font-medium focus:ring-0 focus:outline-none focus:ring-offset-0 transition-all text-sm m-1 ms-0;
}
.ti-btn.ti-btn-outline {
  @apply border;
}
.ti-btn.ti-btn-disabled {
  @apply cursor-not-allowed opacity-50;
}

.ti-btn.ti-btn-sm {
  @apply py-[0.26rem] px-2 rounded-[0.25rem] text-[0.75rem];
}

.ti-btn.ti-btn-lg {
  @apply py-[0.65rem] px-4 rounded-[0.3rem] text-[0.95rem];
}

.ti-btn-group .ti-btn {
  @apply py-[0.45rem] px-3;
}

.ti-btn-group-lg .ti-btn {
  @apply py-[0.65rem] px-4;
}

.ti-btn-group-sm .ti-btn {
  @apply py-[0.25rem] px-2;
}

.ti-btn-icon {
  @apply w-[2.1rem] h-[2.1rem] text-[0.95rem] p-[0.375rem] shrink-0;
}
.ti-btn-icon i {
  @apply p-0 my-0 -mx-2;
  padding: 0rem;
  margin: 0 -0.5rem;
}

.ti-btn-icon.ti-btn-sm {
  @apply w-[1.75rem] h-[1.75rem] text-[0.8rem] p-[0.1875rem];
}

.ti-btn-icon.ti-btn-lg {
  @apply w-[2.75rem] h-[2.75rem] text-[1.2rem] p-[0.5rem];
}

.ti-btn-list button,
.ti-btn-list div,
.ti-btn-list a,
.ti-btn-list input {
  @apply mt-0 mb-[0.375rem] ms-0 me-[0.375rem];
}

.ti-btn-list {
  @apply -mb-2;
}

.ti-btn-group {
  @apply py-3 px-4 inline-flex justify-center items-center gap-2 -ms-px first:rounded-s-sm first:ms-0 last:rounded-e-sm border border-defaultborder dark:border-defaultborder/10 font-medium align-middle focus:z-10 focus:outline-none focus:ring-0 transition-all text-sm;
}

.ti-btn-primary {
  @apply bg-primary text-white hover:bg-primary focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-secondary {
  @apply bg-secondary text-white hover:bg-secondary focus:ring-secondary dark:focus:ring-offset-white/10;
}

.ti-btn-warning {
  @apply bg-warning text-white hover:bg-warning focus:ring-warning dark:focus:ring-offset-white/10;
}

.ti-btn-success {
  @apply bg-success text-white hover:bg-success focus:ring-success dark:focus:ring-offset-white/10;
}

.ti-btn-light {
  @apply bg-light dark:bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 hover:bg-[#f2f2f3] focus:ring-gray-100 dark:focus:ring-offset-white/10;
}

.ti-btn-dark {
  @apply bg-gray-900 text-gray-100 hover:bg-gray-900 focus:ring-white/10 dark:focus:ring-offset-white/10 !important;
}

.ti-btn-info {
  @apply bg-info text-white hover:bg-info focus:ring-info dark:focus:ring-offset-white/10;
}

.ti-btn-danger {
  @apply bg-danger text-white hover:bg-danger focus:ring-danger dark:focus:ring-offset-white/10;
}

.ti-btn-orange {
  @apply bg-orangemain text-white hover:bg-orangemain focus:ring-orangemain dark:focus:ring-offset-white/10;
}

.ti-btn-purple {
  @apply bg-purplemain text-white hover:bg-purplemain focus:ring-purplemain dark:focus:ring-offset-white/10;
}

.ti-btn-teal {
  @apply bg-tealmain text-white hover:bg-tealmain focus:ring-tealmain dark:focus:ring-offset-white/10;
}

.ti-btn-outline-primary {
  @apply border-primary text-primary hover:text-white hover:bg-primary hover:border-primary focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-outline-secondary {
  @apply border-secondary text-secondary hover:text-white hover:bg-secondary hover:border-secondary focus:ring-secondary dark:focus:ring-offset-white/10;
}

.ti-btn-outline-danger {
  @apply border-danger text-danger hover:text-white hover:bg-danger hover:border-danger focus:ring-danger dark:focus:ring-offset-white/10;
}

.ti-btn-outline-warning {
  @apply border-warning text-warning hover:text-white hover:bg-warning hover:border-warning focus:ring-warning dark:focus:ring-offset-white/10;
}

.ti-btn-outline-info {
  @apply border-info text-info hover:text-white hover:bg-info hover:border-info focus:ring-info dark:focus:ring-offset-white/10;
}

.ti-btn-outline-success {
  @apply border-success text-success hover:text-white hover:bg-success hover:border-success focus:ring-success dark:focus:ring-offset-white/10;
}

.ti-btn-outline-light {
  @apply border-defaultborder text-defaulttextcolor dark:text-defaulttextcolor/80 dark:border-defaultborder/10 hover:bg-gray-200 dark:hover:bg-light hover:border-defaultborder focus:ring-gray-100 dark:focus:ring-offset-white/10;
}

.ti-btn-outline-dark {
  @apply border-gray-900 text-gray-900 hover:text-white hover:bg-gray-900 hover:border-gray-900 dark:border-white/10 dark:text-white/70 focus:ring-white/10 dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-primary {
  @apply text-primary hover:bg-primary/10 focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-secondary {
  @apply text-secondary hover:bg-secondary/10 focus:ring-secondary dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-warning {
  @apply text-warning hover:bg-warning/10 focus:ring-warning dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-info {
  @apply text-info hover:bg-info/10 focus:ring-info dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-danger {
  @apply text-danger hover:bg-danger/10 focus:ring-danger dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-success {
  @apply text-success hover:bg-success/10 focus:ring-success dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-purple {
  @apply text-purplemain hover:bg-purplemain/10 focus:ring-purplemain dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-teal {
  @apply text-tealmain hover:bg-tealmain/10 focus:ring-tealmain dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-orange {
  @apply text-orangemain hover:bg-orangemain/10 focus:ring-orangemain dark:focus:ring-offset-white/10;
}

.ti-btn-ghost-light {
  @apply text-gray-500 hover:bg-gray-100 focus:ring-gray-100 dark:focus:ring-offset-white/10 dark:hover:bg-bodybg;
}

.ti-btn-ghost-dark {
  @apply text-gray-900 hover:bg-gray-900/10 focus:ring-white/10 dark:focus:ring-offset-white/10 dark:text-white/70 dark:hover:bg-black/20;
}

.ti-btn-soft-primary {
  @apply bg-primary/[0.15] text-primary hover:text-white hover:bg-primary ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-soft-primary1 {
  @apply bg-primarytint1color/[0.15] text-primarytint1color hover:text-white hover:bg-primarytint1color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-soft-primary2 {
  @apply bg-primarytint2color/[0.15] text-primarytint2color hover:text-white hover:bg-primarytint2color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-soft-primary3 {
  @apply bg-primarytint3color/[0.15] text-primarytint3color hover:text-white hover:bg-primarytint3color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
}

.ti-btn-soft-secondary {
  @apply bg-secondary/[0.15] text-secondary hover:text-white hover:bg-secondary ring-offset-white focus:ring-secondary dark:focus:ring-offset-white/10;
}

.ti-btn-soft-warning {
  @apply bg-warning/[0.15] text-warning hover:text-white hover:bg-warning ring-offset-white focus:ring-warning dark:focus:ring-offset-white/10;
}

.ti-btn-soft-danger {
  @apply bg-danger/[0.15] text-danger hover:text-white hover:bg-danger ring-offset-white focus:ring-danger dark:focus:ring-offset-white/10;
}

.ti-btn-soft-info {
  @apply bg-info/[0.15] text-info hover:text-white hover:bg-info ring-offset-white focus:ring-info dark:focus:ring-offset-white/10;
}

.ti-btn-soft-orange {
  @apply bg-orangemain/[0.15] text-orangemain hover:text-white hover:bg-orangemain ring-offset-white focus:ring-orangemain dark:focus:ring-offset-white/10;
}

.ti-btn-soft-purple {
  @apply bg-purplemain/[0.15] text-purplemain hover:text-white hover:bg-purplemain ring-offset-white focus:ring-purplemain dark:focus:ring-offset-white/10;
}

.ti-btn-soft-teal {
  @apply bg-tealmain/[0.15] text-tealmain hover:text-white hover:bg-tealmain ring-offset-white focus:ring-tealmain dark:focus:ring-offset-white/10;
}

.ti-btn-soft-success {
  @apply bg-success/[0.15] text-success hover:text-white hover:bg-success ring-offset-white focus:ring-success dark:focus:ring-offset-white/10;
}

.ti-btn-soft-light {
  @apply bg-gray-100 dark:bg-gray-100/10 text-gray-500 dark:text-white dark:hover:text-gray-800 hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-200 ring-offset-white focus:ring-success dark:focus:ring-offset-white/10;
}

.ti-btn-soft-dark {
  @apply bg-gray-900/10 text-gray-900 hover:text-white hover:bg-gray-900 ring-offset-white focus:ring-white/10 dark:focus:ring-offset-white/10 dark:text-white/70;
}

.ti-btn-primary-gradient {
  @apply bg-gradient-to-r from-primary to-secondary text-white border-0;
}

.ti-btn-secondary-gradient {
  @apply bg-gradient-to-r from-secondary to-[#7289FF] text-white border-0;
}

.ti-btn-success-gradient {
  @apply bg-gradient-to-r from-success to-[#009CA4] text-white border-0;
}

.ti-btn-danger-gradient {
  @apply bg-gradient-to-r from-danger to-[#DE4980] text-white border-0;
}

.ti-btn-warning-gradient {
  @apply bg-gradient-to-r from-warning to-[#9EA53C] text-white border-0;
}

.ti-btn-info-gradient {
  @apply bg-gradient-to-r from-info to-[#4990E1] text-white border-0;
}

.ti-btn-orange-gradient {
  @apply bg-gradient-to-r from-orangemain to-[#E5647E] text-white border-0;
}

.ti-btn-purple-gradient {
  @apply bg-gradient-to-r from-purplemain to-[#0086FF] text-white border-0;
}

.ti-btn-teal-gradient {
  @apply bg-gradient-to-r from-tealmain to-[#3AE3C7] text-white border-0;
}

/* End Buttons Styles */
.ti-btn.ti-btn-w-sm {
  @apply min-w-[6.975rem] !important;
}

.ti-btn.ti-btn-w-md {
  @apply min-w-[8.125rem] !important;
}

.ti-btn.ti-btn-w-lg {
  @apply min-w-[9.375rem];
}

.custom-button {
  @apply relative ps-[2.75rem];
}
.custom-button .custom-ti-btn-icons {
  @apply shadow-[0_0_1px_0.25rem_rgba(0,0,0,0.1)] absolute -start-[0.125rem] top-0 bg-white flex items-center justify-center overflow-hidden p-[0.375rem] rounded-[3.125rem]
          text-[1rem] w-[2.25rem] h-[2.25rem];
}
.custom-button .custom-ti-btn-icons i {
  @apply absolute;
}

.ti-btn-border-down.ti-btn-soft-teal {
  @apply border-b-[0.1875rem] border-t-0 border-x-0 border-solid border-b-tealmain !important;
}

.ti-btn-border-start.ti-btn-soft-secondary {
  @apply border-s-[0.1875rem] border-t-0 border-b-0 border-e-0 border-solid border-s-secondary !important;
}

.ti-btn-border-end.ti-btn-soft-purple {
  @apply border-e-[0.1875rem] border-t-0 border-s-0 border-b-0 border-solid border-e-purplemain !important;
}

.ti-btn-border-top.ti-btn-soft-warning {
  @apply border-t-[0.1875rem] border-b-0 border-x-0 border-solid border-t-warning !important;
}

.ti-btn-hover {
  @apply relative;
}
.ti-btn-hover.ti-btn-hover-animate {
  @apply transition-all duration-[0.2s] ease-linear delay-[0s] before:content-[""] before:text-[0.8125rem] before:absolute
          before:flex before:items-center before:justify-center before:end-0 before:top-0 before:opacity-0 before:h-full before:w-[2rem] before:transition-all
          before:duration-[0.2s] before:ease-linear before:delay-[0s] before:font-bootstrap;
}
.ti-btn-hover.ti-btn-hover-animate:hover {
  @apply pe-8 before:opacity-[1] before:indent-0;
}

.ti-btn-darken-hover {
  @apply relative;
}
.ti-btn-darken-hover:hover {
  @apply before:absolute before:w-full before:h-full before:bg-black/[0.25] before:top-0 before:start-0;
}

.ti-btn-loader i {
  @apply animate-spin !important;
}

.ti-btn-group-vertical > .ti-btn,
.ti-btn-group > .ti-btn {
  @apply relative flex-grow;
}

.btn-check {
  @apply absolute pointer-events-none sr-only;
}

.ti-btn-list a {
  @apply ms-0 me-1.5 mt-0 mb-1.5 !important;
}

.label-ti-btn {
  @apply relative ps-[2.6rem];
}

.label-ti-btn-icon {
  @apply absolute w-[2.25rem] text-[1rem] flex items-center justify-center -start-0 -top-0 -bottom-0 bg-white/20;
}

.label-ti-btn.label-end {
  @apply ps-4 pe-[2.6rem] !important;
}
.label-ti-btn.label-end .label-ti-btn-icon {
  @apply -end-0 start-auto;
}

/* Start:: Social Buttons */
.ti-btn-facebook {
  @apply bg-facebook text-white border border-solid border-facebook;
}
.ti-btn-facebook:hover, .ti-btn-facebook:focus, .ti-btn-facebook:active {
  @apply bg-facebook text-white border border-solid border-facebook !important;
}

.ti-btn-google {
  @apply bg-google text-white border border-solid border-google;
}
.ti-btn-google:hover, .ti-btn-google:focus, .ti-btn-google:active {
  @apply bg-google text-white border border-solid border-google !important;
}

.ti-btn-twitter {
  @apply bg-twitter text-white border border-solid border-twitter;
}
.ti-btn-twitter:hover, .ti-btn-twitter:focus, .ti-btn-twitter:active {
  @apply bg-twitter text-white border border-solid border-twitter !important;
}

.ti-btn-github {
  @apply bg-github text-white border border-solid border-github;
}
.ti-btn-github:hover, .ti-btn-github:focus, .ti-btn-github:active {
  @apply bg-github text-white border border-solid border-github !important;
}

.ti-btn-youtube {
  @apply bg-youtube text-white border border-solid border-youtube;
}
.ti-btn-youtube:hover, .ti-btn-youtube:focus, .ti-btn-youtube:active {
  @apply bg-youtube text-white border border-solid border-youtube !important;
}

.ti-btn-instagram {
  @apply bg-[#f09433] text-white border border-solid border-transparent;
  background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: -webkit-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#f09433", endColorstr="#bc1888",GradientType=1 );
}
.ti-btn-instagram:hover, .ti-btn-instagram:focus, .ti-btn-instagram:active {
  @apply text-white border border-solid border-transparent;
}

.label-ti-btn {
  @apply relative ps-[2.6rem] !important;
}

.btn-check + .ti-btn.ti-btn-outline-primary:hover,
.btn-check:active + .ti-btn-outline-primary,
.btn-check:checked + .ti-btn-outline-primary,
.ti-btn-outline-primary.active,
.ti-btn-outline-primary.dropdown-toggle.show,
.ti-btn-outline-primary:active {
  @apply text-white bg-primary border-primary !important;
}

.btn-check + .ti-btn.ti-btn-outline-primary:hover,
.btn-check:active + .ti-btn-outline-primary,
.btn-check:checked + .ti-btn-outline-primary,
.ti-btn-outline-primary.active,
.ti-btn-outline-primary.dropdown-toggle.show,
.ti-btn-outline-primary:active {
  @apply text-white bg-primary border-primary !important;
}

.btn-check + .ti-btn.ti-btn-outline-light:hover,
.btn-check:active + .ti-btn-outline-light,
.btn-check:checked + .ti-btn-outline-light,
.ti-btn-outline-light.active,
.ti-btn-outline-light.dropdown-toggle.show,
.ti-btn-outline-light:active {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 bg-light border-light !important;
}

.btn-check + .ti-btn.ti-btn-outline-light:hover,
.btn-check:active + .ti-btn-outline-light,
.btn-check:checked + .ti-btn-outline-light,
.ti-btn-outline-light.active,
.ti-btn-outline-light.dropdown-toggle.show,
.ti-btn-outline-light:active {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 bg-light border-light !important;
}

button {
  @apply cursor-pointer !important;
}

.ti-btn {
  @apply cursor-pointer !important;
}

/* End:: Social Buttons */
.box.box-bg-primary {
  @apply bg-primary text-white !important;
}

.box.box-bg-success {
  @apply bg-secondary text-white !important;
}

/* Start::Card Background Colors */
.box-bg-primary {
  @apply bg-primary text-white !important;
}

.box-bg-primary .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-primary .box-body {
  @apply text-white;
}

.box-bg-primary .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-primary1 {
  @apply bg-primarytint1color text-white !important;
}

.box-bg-primary1 .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-primary1 .box-body {
  @apply text-white;
}

.box-bg-primary1 .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-primary2 {
  @apply bg-primarytint2color text-white !important;
}

.box-bg-primary2 .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-primary2 .box-body {
  @apply text-white;
}

.box-bg-primary2 .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-primary3 {
  @apply bg-primarytint3color text-white !important;
}

.box-bg-primary3 .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-primary3 .box-body {
  @apply text-white;
}

.box-bg-primary3 .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-secondary {
  @apply bg-secondary text-white !important;
}

.box-bg-secondary .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-secondary .box-body {
  @apply text-white;
}

.box-bg-secondary .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-warning {
  @apply bg-warning text-white !important;
}

.box-bg-warning .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-warning .box-body {
  @apply text-white;
}

.box-bg-warning .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-info {
  @apply bg-info text-white !important;
}

.box-bg-info .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-info .box-body {
  @apply text-white;
}

.box-bg-info .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-success {
  @apply bg-success text-white !important;
}

.box-bg-success .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-success .box-body {
  @apply text-white;
}

.box-bg-success .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-danger {
  @apply bg-danger text-white !important;
}

.box-bg-danger .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-danger .box-body {
  @apply text-white;
}

.box-bg-danger .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-orange {
  @apply bg-orangemain text-white !important;
}

.box-bg-orange .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.1)] border-b border-solid before:bg-white;
}

.box-bg-orange .box-body {
  @apply text-white;
}

.box-bg-orange .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.1)] border-t border-solid;
}

.box-bg-light {
  @apply bg-light text-defaulttextcolor !important;
}

.box-bg-light .box-header {
  @apply text-defaulttextcolor border-b-defaultborder;
}

.box-bg-light .box-body {
  @apply text-defaulttextcolor;
}

.box-bg-light .box-footer {
  @apply text-defaulttextcolor border-t-defaultborder;
}

.box-bg-dark {
  @apply bg-dark text-white !important;
}

.box-bg-dark .box-header {
  @apply text-white border-b-[rgba(255,255,255,0.2)] border-b border-solid before:bg-white;
}

.box-bg-dark .box-body {
  @apply text-white;
}

.box-bg-dark .box-footer {
  @apply text-white border-t-[rgba(255,255,255,0.2)] border-t border-solid;
}

/* End::Card Background Colors */
.mySwiper1 .swiper-button-next:after,
.mySwiper1 .swiper-button-prev:after {
  @apply text-[1.5rem] font-[300] text-white !important;
}
.mySwiper1 .swiper-pagination-bullet {
  @apply bg-white text-white w-[30px] h-[3px] rounded-none;
}

/* Start Box Styles */
.box {
  @apply flex flex-col shadow-defaultshadow dark:shadow-[0px_2px_1px_-1px_rgba(255,255,255,0.05)] rounded-md border-0 text-defaulttextcolor dark:text-defaulttextcolor/70 bg-white text-[0.813rem] mb-6 relative dark:bg-bodybg;
}

.box-header {
  @apply bg-transparent flex items-center relative flex-wrap gap-1 pt-4 pb-2 px-4 border-b-0;
}

.box-title {
  @apply relative text-[0.95rem] font-medium mb-0;
}

.box-footer {
  @apply bg-transparent border-t-defaultborder text-[0.8125rem] p-4 border-t border-solid;
}

.box-body {
  @apply p-4 text-defaulttextcolor dark:text-defaulttextcolor/70 flex-auto;
}

.box-footer {
  @apply border-t rounded-b-sm py-4 px-[1.25rem] text-defaultsize text-defaulttextcolor md:py-4 md:px-5 dark:border-white/10;
}

.display-1 {
  @apply text-[5rem];
}

.display-2 {
  @apply text-[4.5rem];
}

.display-3 {
  @apply text-[4rem];
}

.display-4 {
  @apply text-[3.5rem];
}

.display-5 {
  @apply text-[3rem];
}

.display-6 {
  @apply text-[2.5rem];
}

.overlay-box {
  @apply relative overflow-hidden text-white/90 before:absolute before:bg-black/20 before:inset-0;
}
.overlay-box .box-header {
  @apply border-white/10;
}
.overlay-box .box-footer {
  @apply border-white/10;
}

.over-content-bottom {
  @apply top-auto;
}

.box-anchor {
  @apply content-[] absolute inset-0 z-[1] pointer-events-none;
}

.box.box-fullscreen {
  @apply fixed inset-0 z-[9999] m-0 rounded-none;
}

.box-img-overlay {
  @apply absolute inset-0 rounded-sm overflow-auto;
}

.card-img-top {
  @apply rounded-t-md !important;
}

/* End Box Styles */
.lead {
  @apply text-[1.25rem] font-[300];
}

/* Start Alert Styles */
.alert {
  @apply text-[0.8125] rounded-sm px-4 py-[0.625rem] mb-4 last:mb-0 font-medium;
}

.text-badge .badge {
  @apply absolute -end-4 -top-4;
}

/* End Alert Styles */
.review-quote {
  @apply absolute end-[0.8rem] text-[1.875rem] leading-[0] text-primary/70 bg-transparent p-[0.625rem];
}

/* Start Badge Styles */
.badge {
  @apply inline-flex items-center text-[11px] py-1 px-[0.45rem] font-medium rounded-sm text-white leading-none;
}
.badge:last-child {
  @apply mb-0;
}

/* End Badge Styles */
/* Start List-Group Styles */
.ti-list-group {
  @apply items-center font-normal text-[0.8125rem] border border-solid border-defaultborder rounded-md text-start dark:border-defaultborder/10;
}
.ti-list-group .ti-list-group-item {
  @apply py-3 px-[1.25rem] border-b border-solid border-defaultborder dark:border-defaultborder/10 last:border-b-0;
}
.ti-list-group .ti-list-group-item.active {
  @apply text-white bg-primary border-primary rounded-t-md !important;
}
.ti-list-group .ti-list-group-item.disabled, .ti-list-group .ti-list-group-item:disabled {
  @apply text-textmuted dark:text-textmuted/50 bg-light opacity-[0.5] !important;
}
.ti-list-group .ti-list-group-item.ti-list-group-item-action {
  @apply w-full;
}
.ti-list-group .ti-list-group-item.ti-list-group-item-action:hover, .ti-list-group .ti-list-group-item.ti-list-group-item-action:focus {
  @apply bg-light;
}
.ti-list-group .ti-list-group-item.ti-icon-link {
  @apply focus:z-10 focus:outline-none py-[0.375rem] px-3 focus:ring-0;
}

.ti-list-group-flush {
  @apply border-0;
}

/* End List-Group Styles */
/* Start Progress Styles */
.ti-main-progress {
  @apply flex w-full h-1.5 rounded-full overflow-hidden;
}
.ti-main-progress .ti-main-progress-bar {
  @apply flex flex-col justify-center overflow-hidden;
}

/* End Progress Styles */
/* Start Spinner Styles */
.ti-spinner {
  @apply animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent rounded-full;
}

/* End Spinner Styles */
/* Start Toast Styles */
.ti-toast {
  @apply max-w-xs border rounded-sm shadow-lg dark:border-white/10;
}

/* End Toast Styles */
.ti-border {
  @apply border-[#e5e7eb];
}

/* Start:: Nav-tabs */
.nav-link:focus, .nav-link:hover {
  @apply text-primary;
}

.nav-tabs {
  @apply border-defaultborder;
}
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  @apply text-defaulttextcolor bg-light border-defaultborder;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  @apply border-defaultborder;
}

.nav-tabs-header {
  @apply border-0;
}
.nav-tabs-header .nav-item {
  @apply me-2;
}
.nav-tabs-header .nav-item:last-child {
  @apply me-0;
}
.nav-tabs-header .nav-item .nav-link {
  @apply border border-solid rounded-sm border-transparent text-[0.8rem] p-2 font-medium;
}
.nav-tabs-header .nav-item .nav-link.active {
  @apply bg-primary/10 text-primary border border-solid border-transparent;
}
.nav-tabs-header .nav-item .nav-link:hover, .nav-tabs-header .nav-item .nav-link:focus {
  @apply border border-solid border-transparent text-primary;
}

.nav-justified .nav-item,
.nav-justified > .nav-link {
  @apply basis-0 flex-grow text-center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  @apply w-full;
}

.nav.nav-style-1 {
  @apply border-0;
}

.nav.nav-style-3 .nav-link {
  @apply rounded-none !important;
}
.nav.nav-style-3 .nav-link {
  @apply border-b-[3px] border-solid border-transparent;
}
.nav.nav-style-3 .nav-link.active {
  @apply bg-transparent border-b-[3px] border-solid border-primary text-primary rounded-none !important;
}

/* End:: Nav-tabs */
/* Start:: Tab-style-6 */
.tab-style-6 {
  @apply border-0 bg-primary/10 rounded-md text-primary p-2;
}
.tab-style-6 .nav-item {
  @apply border-0 me-2;
}
.tab-style-6 .nav-item:last-child {
  @apply me-0;
}
.tab-style-6 .nav-item .nav-link {
  @apply text-primary py-2 px-4 text-defaultsize border-0 font-medium;
}
.tab-style-6 .nav-item .nav-link.active {
  @apply border-0 bg-primary text-white shadow-sm;
}
.tab-style-6 .nav-item .nav-link:hover, .tab-style-6 .nav-item .nav-link:focus {
  @apply border-0;
}

.navbar {
  @apply rounded-md;
}
.navbar .navbar-nav .nav-link {
  @apply leading-none py-[0.4rem] px-4 font-medium;
}
.navbar .navbar-toggler {
  @apply p-[0.4rem] text-[1rem] leading-none text-black border border-solid border-defaultborder rounded-md;
}
.navbar .navbar-toggler .navbar-toggler-icon {
  @apply w-[1rem] h-[1rem] relative bg-none before:content-[""] before:absolute before:text-[1rem] before:text-defaulttextcolor before:start-0;
}
.navbar .navbar-toggler:focus {
  @apply shadow-none;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  @apply text-white bg-primary;
}

.navitem {
  @apply me-4;
}

/* End:: Tab-style-6 */
/* start::background */
.bg-primary-gradient {
  @apply bg-gradient-to-r from-primary to-[#0086ed] text-white !important;
}

.bg-secondary-gradient {
  @apply bg-gradient-to-r from-secondary to-[#0086ed] text-white !important;
}

.bg-warning-gradient {
  @apply bg-gradient-to-r from-warning to-[#0086ed] text-white !important;
}

.bg-info-gradient {
  @apply bg-gradient-to-r from-info to-[#0086ed] text-white !important;
}

.bg-success-gradient {
  @apply bg-gradient-to-r from-success to-[#0086ed] text-white !important;
}

.bg-danger-gradient {
  @apply bg-gradient-to-r from-danger to-[#0086ed] text-white !important;
}

.bg-orange-gradient {
  @apply bg-gradient-to-r from-orangemain to-[#0086ed] text-white !important;
}

.bg-purple-gradient {
  @apply bg-gradient-to-r from-purplemain to-[#0086ed] text-white !important;
}

.bg-teal-gradient {
  @apply bg-gradient-to-r from-tealmain to-[#0086ed] text-white !important;
}

.bg-light-gradient {
  @apply bg-gradient-to-r from-light to-[#0086ed] text-white !important;
}

.bg-dark-gradient {
  @apply bg-gradient-to-r from-black to-[#0086ed] text-white !important;
}

/* End:: backgrounds */
code {
  @apply text-[0.675rem] break-words !important;
}

.color-container {
  @apply w-[3rem] h-[3rem] rounded-full flex items-center justify-center leading-[3rem];
}

.bd-example-row [class^=col],
.bd-example-cssgrid .grid > * {
  @apply pt-3 pb-3 bg-light/75 border border-solid border-defaultborder;
}

.bd-example-cssgrid .grid > * {
  @apply rounded-sm;
}

.bd-example-row-flex-cols .grid {
  @apply min-h-[10rem] bg-light/50;
}
.bd-example-row-flex-cols .grid .col {
  @apply p-3;
}

.flex-container div {
  @apply bg-transparent border-0;
}
.flex-container div > div {
  @apply bg-light border border-solid border-white dark:border-bodybg;
}

.ratio {
  @apply relative w-full before:block;
}

.bd-example-ratios .ratio {
  @apply inline-block w-[10rem] text-textmuted dark:text-textmuted/50 bg-primary/10 border-defaultborder rounded-md;
}

.bd-example-position-utils .position-absolute {
  @apply w-8 h-8 bg-primary/10 rounded-[0.375rem] absolute;
}

.bd-example-position-utils .position-relative {
  @apply h-[12.5rem] bg-light relative;
}

.border-container {
  @apply inline-block w-[5rem] h-[5rem] m-1 bg-light/30;
}

.upcoming-events-list li {
  @apply mb-[1.5rem] ps-[1.5rem] relative before:absolute before:h-full before:w-[0.25rem] before:start-0 before:rounded-md;
}

.upcoming-events-list li:nth-child(1) {
  @apply before:bg-primary/30;
}

.upcoming-events-list li:nth-child(2) {
  @apply before:bg-secondary/30;
}

.upcoming-events-list li:nth-child(3) {
  @apply before:bg-success/30;
}

.upcoming-events-list li:nth-child(4) {
  @apply before:bg-danger/30;
}

.upcoming-events-list li:nth-child(5) {
  @apply before:bg-info/30;
}

.upcoming-events-list li:nth-child(5) {
  @apply before:bg-warning/30;
}

.upcoming-events-list li:last-child {
  @apply mb-0;
}

#external-events .fc-event {
  @apply cursor-move mb-[0.4rem] py-[0.375rem] px-3 text-[0.75rem] rounded-[0.35rem];
}

.glightbox {
  @apply overflow-hidden;
}

/* start:: project-list */
.project-list-title {
  @apply max-w-[13.375rem];
}

/* Start Dropdown Styles */
.ti-dropdown {
  @apply relative inline-flex;
}

.ti-dropdown-toggle {
  @apply py-3 px-4 inline-flex justify-center items-center gap-2 rounded-sm font-medium align-middle;
}

.ti-dropdown-caret {
  @apply w-2.5 h-2.5 text-gray-600 dark:text-white/70;
}

.ti-dropdown-menu {
  @apply transition-[opacity,margin] hs-dropdown-open:opacity-100 opacity-0 z-[13] min-w-[10rem] bg-white shadow-md border border-defaultborder rounded-sm dark:bg-bodybg dark:border-white/10 dark:divide-white/10;
}

.ti-dropdown-menu {
  @apply -mt-3 !important;
}

.ti-dropdown-item {
  @apply py-2 px-[0.9375rem] text-[0.8125rem] font-medium flex w-full text-gray-800 hover:bg-primary/[0.05] hover:text-primary focus:ring-0 focus:ring-primary dark:text-white/80 dark:hover:bg-primary/5 dark:hover:text-primary;
}
.ti-dropdown-item.active {
  @apply bg-primary text-white !important;
}
.ti-dropdown-item.disabled {
  @apply text-textmuted dark:text-textmuted/50 bg-transparent pointer-events-none;
}

.ti-dropdown-menu-hover {
  @apply after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full;
}

.ti-dropdown-divider {
  @apply py-2 first:pt-0 last:pb-0 dark:border-defaultborder/10;
}

hr {
  @apply dark:border-defaultborder/10 !important;
}

.ti-dropdown-title {
  @apply block py-2 px-3 text-xs font-medium uppercase text-gray-400 dark:text-white/70;
}

.ti-dropdown-header {
  @apply py-3 px-5 -m-2 bg-gray-100 rounded-t-sm dark:bg-black/20;
}
.ti-dropdown-header .ti-dropdown-header-title {
  @apply text-sm text-gray-500 dark:text-white;
}
.ti-dropdown-header .ti-dropdown-header-content {
  @apply text-sm font-medium text-gray-800 dark:text-white;
}

.ti-dropdowm-profileimg {
  @apply w-8 h-auto rounded-full;
}

.ti-dropdowm-profileimg-content {
  @apply text-gray-600 font-medium truncate max-w-[7.5rem] dark:text-white/70;
}

.ti-dropdown-icon {
  @apply w-4 h-4 text-gray-600 dark:text-white/70;
}

.ti-dropdown-toggle {
  @apply relative;
}
.ti-dropdown-toggle .ti-dropdown-toggle {
  @apply after:inline-block after:ms-1 after:align-[0] after:content-[""] after:border-0 after:text-[0.6rem] after:font-semibold;
}
.ti-dropdown-toggle .ti-dropup .ti-dropdown-toggle {
  @apply after:inline-block after:ms-1 after:align-[0] after:content-[""] after:border-0 after:text-[0.6rem] after:font-semibold;
}
.ti-dropdown-toggle .dropend .ti-dropdown-toggle {
  @apply after:inline-block after:ms-1 after:align-[0] after:content-[""] after:border-0 after:text-[0.6rem] after:font-semibold;
}
.ti-dropdown-toggle .dropstart .ti-dropdown-toggle {
  @apply before:inline-block before:ms-1 before:align-[0] before:content-[""] before:border-0 before:text-[0.6rem] before:font-semibold;
}
.ti-dropdown-toggle .dropdown-toggle-split {
  @apply opacity-[0.85];
}

.dropmenu-item-warning .ti-dropdown-item:hover, .dropmenu-item-warning .ti-dropdown-item:focus, .dropmenu-item-warning .ti-dropdown-item:active, .dropmenu-item-warning .ti-dropdown-item.active {
  @apply bg-warning/10 text-warning !important;
}

.dropmenu-item-info .ti-dropdown-item:hover, .dropmenu-item-info .ti-dropdown-item:focus, .dropmenu-item-info .ti-dropdown-item:active, .dropmenu-item-info .ti-dropdown-item.active {
  @apply bg-info/10 text-info !important;
}

.dropmenu-light-success {
  @apply bg-success/10 backdrop-blur-3xl;
}
.dropmenu-light-success .ti-dropdown-item:hover, .dropmenu-light-success .ti-dropdown-item:focus, .dropmenu-light-success .ti-dropdown-item:active, .dropmenu-light-success .ti-dropdown-item.active {
  @apply text-white bg-success !important;
}

.dropmenu-light-danger {
  @apply bg-danger/10 backdrop-blur-3xl;
}
.dropmenu-light-danger .ti-dropdown-item:hover, .dropmenu-light-danger .ti-dropdown-item:focus, .dropmenu-light-danger .ti-dropdown-item:active, .dropmenu-light-danger .ti-dropdown-item.active {
  @apply text-white bg-danger !important;
}

.ti-btn-list a {
  @apply me-0;
}

.bd-example > .dropdown-menu {
  @apply static block;
}

.dropdown-header {
  @apply block py-2 px-4 mb-0 text-[0.875rem];
}

.dropdown-divider {
  @apply border-defaultborder dark:border-defaultborder/10 m-0 border-t;
}

/* End Dropdown Styles */
/* Start Form Styles */
.ti-form-input {
  @apply border border-inputborder block w-full text-sm focus:border-gray-200 focus:shadow-sm dark:shadow-white/10 dark:bg-bodybg dark:border-white/10 dark:focus:border-white/10 dark:text-white/70;
}

select {
  @apply border dark:border-white/10 dark:bg-bodybg !important;
}

select {
  @apply pe-9 ps-2;
}

select {
  @apply rtl:bg-left rtl:bg-[0.5rem] !important;
}

.ti-switch {
  @apply relative w-[3.25rem] h-7 bg-gray-200 checked:bg-none checked:bg-primary border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 ring-0 ring-transparent focus:border-primary focus:shadow-none focus:ring-transparent focus:ring-offset-0 ring-offset-white focus:outline-0 appearance-none dark:checked:bg-primary dark:focus:ring-offset-white/10 before:inline-block before:w-6 before:h-6 before:bg-white checked:before:bg-white before:translate-x-0 ltr:checked:before:translate-x-full rtl:checked:before:-translate-x-full before:shadow before:rounded-full before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-black/20 dark:checked:before:bg-black/20;
  @apply dark:bg-light !important;
}

.ti-switch {
  @apply relative w-[3.25rem] h-7 border-2 border-transparent rounded-full !important;
}

.ti-switch:checked {
  @apply bg-primary bg-none !important;
}

[type=checkbox].ti-switch {
  @apply border-transparent !important;
}

.ti-form-select {
  @apply py-3 px-4 pe-9 block w-full border-gray-200 rounded-sm text-sm focus:border-primary focus:ring-primary dark:dark:bg-bodybg dark:border-white/10 dark:text-white/70;
}

.ti-form-select-label {
  @apply block text-[0.8rem] font-medium mb-2 dark:text-white;
}

.ti-form-label {
  @apply block text-sm font-[0.8rem] mb-2 font-medium dark:text-white;
}

.ti-form-control {
  @apply border-inputborder text-defaulttextcolor bg-white dark:bg-bodybg2 dark:border-white/10 text-[0.875rem] font-normal leading-[1.6] rounded-[0.35rem] py-2 px-[0.85rem] placeholder:text-defaulttextcolor dark:placeholder:text-defaulttextcolor/70;
}
.ti-form-control:focus {
  @apply shadow-none border-inputborder bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70;
}

.form-select {
  @apply block w-full py-[0.375rem] pe-[2.25rem] ps-[0.75rem] text-[1rem] font-normal leading-[1.5];
}

.form-select {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-white/10 text-defaulttextcolor text-defaultsize rounded-md focus:border focus:border-primary;
}
.form-select option {
  @apply bg-white dark:bg-bodybg py-[0.35rem] px-3 rounded-sm border border-primary/10;
}
.form-select option:checked {
  @apply bg-primary/20 text-primary;
}

[type=checkbox], [type=radio] {
  @apply focus:ring-primary focus:border-primary !important;
}

.form-check-input {
  @apply h-[0.9rem] w-[0.9rem] bg-white dark:bg-bodybg dark:border-white/10 border border-solid border-inputborder rounded-sm;
}
.form-check-input:focus {
  @apply border border-defaultborder outline-none !important;
}
.form-check-input:checked {
  @apply bg-primary border-primary dark:bg-primary dark:border-primary !important;
}
.form-check-input.form-checked-outline:checked {
  @apply bg-transparent border-primary !important;
}
.form-check-input.form-checked-secondary:checked {
  @apply bg-secondary border-secondary hover:bg-secondary !important;
}
.form-check-input.form-checked-warning:checked {
  @apply bg-warning border-warning hover:bg-warning !important;
}
.form-check-input.form-checked-info:checked {
  @apply bg-info border-info hover:bg-info !important;
}
.form-check-input.form-checked-success:checked {
  @apply bg-success border-success focus:bg-success focus:border-success focus:shadow-success focus:ring-0 !important;
}
.form-check-input.form-checked-danger:checked {
  @apply bg-danger border-danger hover:bg-danger !important;
}
.form-check-input.form-checked-light:checked {
  @apply bg-light border-light hover:bg-light !important;
}
.form-check-input.form-checked-dark:checked {
  @apply bg-[#232323] border-[#232323] hover:bg-black !important;
}

[type=radio] {
  @apply dark:bg-bodybg dark:border-defaultborder/10 ring-primary;
}

.form-check-input {
  @apply align-top appearance-none;
}

.form-check-input[type=checkbox] {
  @apply rounded-[0.25rem];
}

.form-check-input[type=radio] {
  @apply rounded-[50%] ring-primary;
}

.form-check-input:active {
  @apply brightness-[90%];
}

.form-check-input:focus {
  @apply border-[#86b7fe] outline-none !important;
}

.form-check-input:checked {
  @apply bg-primary border-primary ring-primary;
}

.input-group-text {
  @apply border-inputborder text-[0.875rem] rounded-[0.3125rem] bg-light text-defaulttextcolor;
}
.input-group-text .form-control {
  @apply border-0 rounded-s-none !important;
}

.input-group-text {
  @apply flex items-center py-[0.375rem] px-3 text-[0.875rem] font-normal leading-[1.5] text-center whitespace-nowrap border border-solid border-defaultborder dark:border-defaultborder/10;
}

.input-group {
  @apply relative flex flex-wrap items-stretch w-full;
}

.input-group > .form-control,
.input-group > .form-floating,
.input-group > .form-select {
  @apply relative w-[1%] min-w-0 flex-grow flex-shrink basis-auto;
}

.form-control {
  @apply border-inputborder text-defaulttextcolor dark:text-defaulttextcolor/80 bg-white dark:border-white/10 dark:bg-bodybg text-[0.875rem] font-normal leading-[1] rounded-sm py-[0.375rem] px-[0.85rem] placeholder:text-defaulttextcolor dark:placeholder:text-defaulttextcolor/80 !important;
  @apply placeholder:opacity-40 placeholder:font-medium placeholder:text-[0.8rem];
}
.form-control:focus {
  @apply shadow-none border-inputborder bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70;
}

.form-checked-outline:checked[type=checkbox] {
  @apply bg-none relative bg-transparent hover:bg-transparent !important;
}
.form-checked-outline:checked[type=checkbox]::before {
  @apply absolute content-[""] font-bootstrap text-primary w-[0.625rem] h-[0.625rem] -top-0 start-[1px] text-[0.688rem] !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-success {
  @apply before:text-success border-success !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-secondary {
  @apply before:text-secondary border-secondary !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-warning {
  @apply before:text-warning border-warning !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-info {
  @apply before:text-info border-info !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-danger {
  @apply before:text-danger border-danger !important;
}
.form-checked-outline:checked[type=checkbox].form-checked-dark {
  @apply before:text-dark border-dark !important;
}

.form-checked-outline:checked[type=radio] {
  @apply bg-none relative bg-transparent hover:bg-transparent !important;
}
.form-checked-outline:checked[type=radio]::before {
  @apply absolute content-[""] font-bootstrap text-primary w-[0.625rem] h-[0.625rem] -top-[17px] -start-[9.5px] text-[2rem] !important;
}
.form-checked-outline:checked[type=radio].form-checked-success {
  @apply before:text-success border-success !important;
}
.form-checked-outline:checked[type=radio].form-checked-secondary {
  @apply before:text-secondary border-secondary !important;
}
.form-checked-outline:checked[type=radio].form-checked-warning {
  @apply before:text-warning border-warning !important;
}
.form-checked-outline:checked[type=radio].form-checked-info {
  @apply before:text-info border-info !important;
}
.form-checked-outline:checked[type=radio].form-checked-danger {
  @apply before:text-danger border-danger !important;
}
.form-checked-outline:checked[type=radio].form-checked-dark {
  @apply before:text-dark border-dark !important;
}

.form-control-sm {
  @apply text-[0.8rem] leading-[inherit] rounded-md px-[0.8rem] py-1;
}

.form-control-lg {
  @apply text-[.75rem] py-3 px-4 !important;
}

.form-check-md .form-check-input {
  @apply w-[1.15rem] h-[1.15rem];
}

.form-check-lg .form-check-input {
  @apply w-[1.35rem] h-[1.35rem];
}

.form-check-reverse {
  @apply pe-[1.5rem] ps-0 text-end;
}

.form-check-reverse .form-check-input {
  @apply ltr:float-right rtl:float-left -me-[1.5em] ms-0;
}

[type=text]:focus,
input:where(:not([type])):focus,
[type=email]:focus,
[type=url]:focus,
[type=password]:focus,
[type=number]:focus,
[type=date]:focus,
[type=datetime-local]:focus,
[type=month]:focus,
[type=search]:focus,
[type=tel]:focus,
[type=time]:focus,
[type=week]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
  @apply border-defaultborder dark:border-defaultborder/10 ring-0 !important;
}

[type=checkbox]:checked:hover,
[type=checkbox]:checked:focus,
[type=radio]:checked:hover,
[type=radio]:checked:focus {
  @apply bg-primary ring-primary;
}

/* Start:: toggle switches-1 */
.toggle {
  @apply w-[3.75rem] h-[1.563rem] bg-light ms-[0.625rem] mb-[0.313rem] p-[0.125rem] rounded-[0.188rem] relative overflow-hidden transition-all duration-[0.2s] ease-linear;
}
.toggle span {
  @apply absolute top-[0.188rem] bottom-1 start-[0.188rem] block w-[1.25rem] rounded-[0.125rem] bg-white dark:bg-bodybg shadow-sm cursor-pointer transition-all duration-[0.2s] ease-linear
    before:-start-[1.563rem] before:content-["on"] after:content-["off"] after:-end-[1.813rem] after:text-textmuted dark:after:text-textmuted/50;
}
.toggle span::before, .toggle span::after {
  @apply absolute text-[0.625rem] font-medium space-x-2 rtl:space-x-reverse uppercase top-[0.188rem] leading-[1.38] transition-all duration-[0.2s] ease-linear;
}
.toggle.on {
  @apply bg-primary/30;
}
.toggle.on span {
  @apply bg-primary start-[2.313rem];
}
.toggle.on span::before {
  @apply text-primary;
}
.toggle.on.toggle-secondary {
  @apply bg-secondary/40;
}
.toggle.on.toggle-secondary span {
  @apply bg-secondary before:text-secondary;
}
.toggle.on.toggle-warning {
  @apply bg-warning/40;
}
.toggle.on.toggle-warning span {
  @apply bg-warning before:text-warning;
}
.toggle.on.toggle-info {
  @apply bg-info/40;
}
.toggle.on.toggle-info span {
  @apply bg-info before:text-info;
}
.toggle.on.toggle-success {
  @apply bg-success/40;
}
.toggle.on.toggle-success span {
  @apply bg-success before:text-success;
}
.toggle.on.toggle-danger {
  @apply bg-danger/40;
}
.toggle.on.toggle-danger span {
  @apply bg-danger before:text-danger;
}
.toggle.on.toggle-light {
  @apply bg-light dark:bg-light/60;
}
.toggle.on.toggle-light span {
  @apply bg-light before:text-textmuted dark:before:text-textmuted/50;
}
.toggle.on.toggle-dark {
  @apply bg-black/40 dark:bg-white/40;
}
.toggle.on.toggle-dark span {
  @apply bg-black dark:bg-white dark:before:text-black before:text-white;
}
.toggle.on span {
  @apply start-[2.313rem];
}
.toggle.on.toggle-sm span {
  @apply start-[2.313rem] before:-top-[1px] before:-start-[1.563rem];
}
.toggle.on.toggle-lg span {
  @apply start-[2.563rem] before:top-2 before:-start-[1.75rem];
}
.toggle.toggle-sm {
  @apply h-[1.063rem] w-[3.125rem];
}
.toggle.toggle-sm span {
  @apply w-[0.625rem] h-[0.625rem] after:-end-[1.875rem] after:-top-[1px];
}
.toggle.toggle-lg {
  @apply h-[2.125rem] w-[4.5rem];
}
.toggle.toggle-lg span {
  @apply w-[1.75rem] after:top-2 after:-end-[1.938rem];
}

/* Start:: toggle switches-2 */
.custom-toggle-switch > input[type=checkbox] {
  @apply hidden;
}

.custom-toggle-switch > label {
  @apply cursor-pointer h-0 relative w-[2.5rem];
}

.label-primary {
  @apply bg-primary text-white;
}

.label-secondary {
  @apply bg-secondary text-white;
}

.label-warning {
  @apply bg-warning text-white;
}

.label-info {
  @apply bg-info text-white;
}

.label-success {
  @apply bg-success text-white;
}

.label-danger {
  @apply bg-danger text-white;
}

.label-light {
  @apply bg-light text-white;
}

.label-dark {
  @apply bg-black dark:bg-white dark:text-black text-white;
}

.custom-toggle-switch > input[type=checkbox]:checked + label::before {
  @apply bg-inherit opacity-[0.5];
}

.custom-toggle-switch > label {
  @apply before:bg-textmuted dark:before:text-textmuted/50 before:rounded-md before:h-4 before:-mt-2 before:absolute before:opacity-[0.3] before:transition-all before:duration-[0.4s] before:ease-in-out before:w-[2.5rem]
  after:bg-white after:rounded-[1rem] after:h-[1.5rem] after:shadow-[0_0_0.313rem_rgba(228,229,237,0.8)] after:-start-1 after:-mt-2 after:absolute after:-top-1 after:transition-all after:duration-[0.3s] after:ease-in-out after:w-[1.5rem];
}

.custom-toggle-switch > input[type=checkbox]:checked + label::after {
  @apply bg-inherit start-[19px];
}

.custom-toggle-switch.toggle-sm > label::before {
  @apply h-[10px] w-[27px] rounded-[10px];
}

.custom-toggle-switch.toggle-sm input[type=checkbox]:checked + label::after {
  @apply start-[13px];
}

.custom-toggle-switch.toggle-sm > label::after {
  @apply h-[17px] w-[17px] rounded-full;
}

.custom-toggle-switch.toggle-lg > label::before {
  @apply h-[27px] w-[55px] rounded-[20px];
}

.custom-toggle-switch.toggle-lg input[type=checkbox]:checked + label::after {
  @apply start-[27px];
}

.custom-toggle-switch.toggle-lg > label::after {
  @apply h-[35px] w-[35px] -mt-[8px] rounded-full;
}

/* End:: toggle switches-2 */
/* End:: toggle switches-1 */
/* End Form Styles */
.form-control-sm {
  @apply text-[0.8rem] py-1 px-[0.8rem] border-inputborder dark:border-white/10 !important;
}

.form-control {
  @apply text-defaulttextcolor bg-white text-[0.875rem] font-normal leading-[1.6] rounded-[0.35rem] py-[0.375rem] px-[0.85rem] w-full;
}
.form-control:focus {
  @apply shadow-none border-inputborder bg-white dark:bg-bodybg text-defaulttextcolor;
}

.form-control:disabled,
.form-select:disabled {
  @apply bg-light text-defaulttextcolor;
}

.form-control-plaintext {
  @apply block w-full p-[0.375rem] mb-0 leading-6 bg-transparent border-transparent border-0;
}

.form-input-color {
  @apply h-[2.25rem] w-[2.25rem] rounded-md overflow-hidden p-0;
}

.form-text {
  @apply mt-1 text-[0.875em] !important;
}

.form-check {
  @apply block ps-0 mb-[0.125rem];
}
.form-check .form-check-label {
  @apply ps-2;
}

.form-check-input:disabled ~ .form-check-label,
.form-check-input[disabled] ~ .form-check-label {
  @apply cursor-default opacity-50;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  @apply rounded-s-none !important;
}

.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select,
.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
  @apply rounded-e-none !important;
}

.input-group-lg > .btn,
.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text {
  @apply py-2 px-4 text-[1.25rem] rounded-md !important;
}

.input-group-sm > .btn,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
  @apply py-1 px-2 text-[0.875rem] !important;
}

.form-range {
  @apply w-full h-[0.5rem] p-0 bg-light rounded-md appearance-none;
}

.form-range:focus {
  @apply outline-none;
}

.form-range:disabled {
  @apply pointer-events-none;
}

.form-control-color {
  @apply w-[1.75rem] h-[1.75rem] overflow-hidden p-0 rounded-md !important;
}

input[type=week]::-webkit-calendar-picker-indicator, input[type=month]::-webkit-calendar-picker-indicator, input[type=date]::-webkit-calendar-picker-indicator, input[type=datetime-local]::-webkit-calendar-picker-indicator, input[type=time]::-webkit-calendar-picker-indicator {
  @apply dark:invert !important;
}

input[type=week], input[type=month], input[type=date], input[type=datetime-local], input[type=time] {
  @apply rtl:text-end rtl:dir-rtl !important;
}
input[type=week]::-webkit-calendar-picker-indicator, input[type=month]::-webkit-calendar-picker-indicator, input[type=date]::-webkit-calendar-picker-indicator, input[type=datetime-local]::-webkit-calendar-picker-indicator, input[type=time]::-webkit-calendar-picker-indicator {
  @apply rtl:text-end rtl:dir-rtl !important;
}

.form-select {
  @apply rtl:bg-left rtl:bg-[0.5rem] !important;
}

.form-label {
  @apply text-[0.8rem] font-medium text-defaulttextcolor dark:text-defaulttextcolor/80 mb-2 inline-block;
}

[type=checkbox], [type=radio] {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

[type=text],
input:where(:not([type])),
[type=email],
[type=url],
[type=password],
[type=number],
[type=date],
[type=datetime-local],
[type=month],
[type=search],
[type=tel],
[type=time],
[type=week],
[multiple],
textarea,
select {
  @apply border-inputborder dark:border-defaultborder/10 rounded-[0.35rem] text-[0.8125rem] !important;
}

input::placeholder, textarea::placeholder {
  @apply text-textmuted dark:text-defaulttextcolor/50 !important;
}

.switcher-pricing [type=checkbox], .switcher-pricing [type=radio] {
  @apply text-primary !important;
}

[dir=rtl] .select {
  @apply bg-[right_0.5rem];
}

/* Start:: form wizard */
.wizard-tab {
  @apply transition-all duration-[0.3sec] ease-linear hidden;
}
.wizard-tab .wizard-nav {
  @apply flex flex-wrap transition-all duration-[0.3sec] mt-[60px] px-6;
}
.wizard-tab .wizard-nav .wizard-step {
  @apply cursor-pointer;
}
.wizard-tab .wizard-nav .wizard-step.nav-buttons {
  @apply cursor-default;
}
.wizard-tab .wizard-nav .wizard-step.nav-buttons span {
  @apply cursor-default;
}
.wizard-tab .wizard-nav.tabs .wizard-step {
  @apply relative text-[14px] basis-0 max-w-full text-center border-b border-b-gray-300 py-2 px-4;
}
.wizard-tab .wizard-nav.tabs .wizard-step.active {
  @apply border border-gray-300 rounded-tl-sm rounded-tr-sm border-b-0;
}
.wizard-tab .wizard-nav.progress {
  @apply h-auto overflow-auto leading-4 rounded-sm;
}
.wizard-tab .wizard-nav.progress .wizard-step {
  @apply relative text-[14px] basis-0 flex-grow max-w-full text-center border border-gray-300 bg-success text-white py-2 px-4 transition-all duration-[0.3sec] ease-linear;
}
.wizard-tab .wizard-nav.progress .wizard-step.active ~ .wizard-step {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80;
}
.wizard-tab .wizard-nav.dots .wizard-step {
  @apply relative text-[14px] basis-0 flex-grow max-w-full text-center transition-all duration-[0.3sec] ease-linear;
  @apply before:absolute before:-top-[16px] before:start-[50%] before:w-full before:h-[2px] before:z-[2] before:bg-primary after:absolute after:-top-[16px]
    after:start-[50%] after:w-full after:h-[2px] after:bg-gray-300 dark:after:bg-light after:z-[1];
}
.wizard-tab .wizard-nav.dots .wizard-step:last-child {
  @apply before:hidden after:hidden;
}
.wizard-tab .wizard-nav.dots .wizard-step span {
  @apply cursor-pointer font-medium;
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(1).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.1s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(2).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.2s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(3).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.3s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(4).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.4s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(5).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.5s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(6).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.6s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(7).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.7s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(8).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.8s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(9).dot {
  @apply transition-all duration-[0.2s] ease-in delay-[0.9s];
}
.wizard-tab .wizard-nav.dots .wizard-step .dot {
  @apply absolute -top-[20px] start-[50%] z-[3] h-[10px] w-[10px] bg-primary rounded-full shadow-[0_0_0_2px_rgba(255,255,255)] dark:shadow-[0_0_0_2px_rgb(--body-bg)] transition-all duration-[0.5s] ease-in-out;
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(1) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.1s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(2) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.2s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(3) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.3s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(4) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.4s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(5) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.5s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(6) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.6s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(7) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.7s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(8) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.8s];
}
.wizard-tab .wizard-nav.dots .wizard-step:nth-of-type(9) {
  @apply before:transition-all before:duration-[0.2s] before:ease-in before:delay-[0.9s];
}
.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step .dot {
  @apply bg-gray-300 dark:bg-light before:bg-gray-300 dark:before:bg-light before:w-[0%] after:bg-gray-300;
}
.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step .dot {
  @apply dark:before:bg-light !important;
}
.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step .wizard-step.active .dot {
  @apply bg-primary shadow-[0_0_0_3px_rgba(92,103,247,0.2)] before:bg-gray-300 after:bg-gray-300 dark:after:bg-light;
}
.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step .wizard-step.active .dot {
  @apply dark:before:bg-light !important;
}
.wizard-tab .wizard-nav.dots .wizard-content {
  @apply transition-all duration-[0.3s] ease-in p-[3rem];
}
.wizard-tab .wizard-nav.dots .wizard-content .wizard-step {
  @apply transition-all duration-[0.3s] ease-in hidden;
}
.wizard-tab .wizard-nav.dots .wizard-content .wizard-step.active {
  @apply block;
}
.wizard-tab .wizard-nav.dots .wizard-buttons {
  @apply transition-all duration-[0.3s] ease-in flex items-center justify-end;
}

.wizard-btn {
  @apply inline-block font-normal leading-3 text-center align-middle cursor-pointer select-none border border-transparent pb-[0.65rem] pt-[0.75rem] px-3 rounded-[0.15rem] transition-all
  duration-[0.3s] ease-in my-0 mx-[10px] bg-primary text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:pointer-events-none disabled:opacity-[0.65] !important;
}

.wizard.vertical {
  @apply flex flex-row flex-wrap w-full transition-all duration-[0.3s] ease-in;
}
.wizard.vertical .wizard-nav {
  @apply flex-col flex-1 py-0 px-[3rem] transition-all duration-[0.3s] ease-linear;
}
.wizard.vertical .wizard-nav .wizard-step {
  @apply before:top-[7px] before:-start-[12px] before:w-[2px] before:h-full after:top-[7px] after:-start-[12px] after:w-[2px] after:h-full;
}
.wizard.vertical .wizard-nav .wizard-step .dot {
  @apply top-[7px] start-[-15px];
}
.wizard.vertical .wizard-nav .wizard-step.active ~ .wizard-step {
  @apply before:h-[0%];
}
.wizard.vertical .wizard-content {
  @apply w-[75%] transition-all duration-[0.3s] ease-in pt-0 pe-0 ps-[3rem] pb-0;
}
.wizard.vertical .wizard-buttons {
  @apply basis-[100%] transition-all duration-[0.3s] ease-in;
}

.highlight-error {
  @apply outline-1 outline-danger;
}

@media screen and (min-width: 1024px) {
  .wizard.vertical .wizard-nav {
    @apply max-w-[250px];
  }
}
@media screen and (max-width: 767px) {
  .wizard.vertical .wizard-nav .wizard-step {
    @apply text-start ps-[1rem];
  }
  .wizard.vertical .wizard-content {
    @apply py-[2rem] px-0;
  }
}
.wizard.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step:before {
  @apply w-[35%] p-[25px] bg-transparent !important;
}

.wizard.wizard-tab .wizard-nav.dots .wizard-step:before {
  @apply absolute -top-[16px];
}

.wizard.wizard-tab .wizard-nav.dots .wizard-step .dot {
  @apply -top-[25px] start-[46%] h-[20px] w-[20px];
}

.wizard.wizard-tab .wizard-nav.dots .wizard-step.active ~ .wizard-step .dot {
  @apply bg-white dark:bg-bodybg border-[2px] border-gray-300 dark:border-defaultborder/10;
}

.wizard-btn.btn.finish {
  @apply hidden !important;
}

.wizard.wizard-tab .wizard-buttons {
  @apply border-t border-defaultborder dark:border-defaultborder/10 justify-between p-4;
}

.wizard.wizard-tab .wizard-buttons .wizard-btn {
  @apply m-0 py-2 px-[0.85rem] min-w-[100px] rounded-[0.35rem];
}

.wizard.wizard-tab .wizard-buttons .wizard-btn:hover {
  @apply bg-primary;
}

.wizard.wizard-tab .wizard-buttons .wizard-btn:disabled {
  @apply opacity-0;
}

.wizard-tab .wizard-buttons .wizard-btn.prev {
  @apply bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 hover:bg-light !important;
}

.payment-card-container {
  @apply relative p-[0.625rem] border border-inputborder dark:border-defaultborder/10 rounded-[0.3rem];
}
.payment-card-container .form-check-input {
  @apply absolute end-3 top-[1.2rem];
}

@media (max-width: 575.98px) {
  .wizard-tab .wizard-nav {
    @apply flex-col gap-[1.5rem];
  }
  .wizard.wizard-tab .wizard-nav.dots .wizard-step:before {
    @apply top-[1.75rem] w-[2px] h-full start-[2.55rem] !important;
  }
  .wizard.wizard-tab .wizard-nav.dots .wizard-step:after {
    @apply top-[1.75rem] w-[2px] h-full start-[2.55rem] !important;
  }
  .wizard.wizard-tab .wizard-nav.dots .wizard-step .dot {
    @apply top-[6px] start-[2.25rem] w-[12px] h-[12px] !important;
  }
  .wizard-tab .wizard-nav.dots .wizard-step {
    @apply text-start ps-[4rem] !important;
  }
}
.wizard-tab .wizard-content .wizard-step {
  @apply hidden;
}

.wizard-tab .wizard-content .wizard-step.active {
  @apply block;
}

.wizard-tab .wizard-content {
  @apply p-[3rem];
}

.wizard-tab .wizard-buttons .wizard-btn.prev {
  @apply text-defaulttextcolor bg-light;
}

.wizard-tab .wizard-buttons .wizard-btn {
  @apply inline-block font-normal leading-none text-center align-middle select-none border border-transparent py-[0.75rem] px-3 rounded-[0.35rem] mx-[10px] my-0 bg-primary text-white !important;
}

.wizard-btn.btn.finish {
  @apply hidden !important;
}

.wizard.wizard-tab .wizard-buttons {
  @apply border-t border-defaultborder dark:border-defaultborder/10 p-[1rem] flex justify-between;
}

.wizard-tab .wizard-nav.dots .wizard-step.active:before {
  @apply bg-gray-300 dark:bg-light;
}

.wizard-tab .wizard-nav.dots .wizard-step.active:after {
  @apply bg-gray-300 dark:bg-light;
}

.wizard-tab .wizard-nav.dots .wizard-step:before {
  @apply bg-primary !important;
}

.form-control-light {
  @apply bg-light dark:bg-light border-0 !important;
}

.choices-control-light .choices__inner {
  @apply bg-light border-0 !important;
}

.ti-switch.ti-switch-custom {
  @apply relative w-[2em] h-[14px] bg-gray-200 checked:bg-none checked:bg-primary border-0 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 ring-0 ring-transparent focus:border-primary focus:shadow-none focus:ring-transparent focus:ring-offset-0 ring-offset-white focus:outline-0 appearance-none dark:bg-black/20 dark:checked:bg-primary dark:focus:ring-offset-white/10 before:inline-block before:w-[0.6rem] before:h-[0.6rem] before:absolute before:top-[2px] before:end-[13px] before:bg-white checked:before:bg-white before:translate-x-0 ltr:checked:before:translate-x-full rtl:checked:before:-translate-x-full before:shadow before:rounded-full before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-black/20 dark:checked:before:bg-black/20 !important;
}
.ti-switch.ti-switch-custom.secondary {
  @apply checked:bg-secondary !important;
}
.ti-switch.ti-switch-custom.warning {
  @apply checked:bg-warning !important;
}
.ti-switch.ti-switch-custom.info {
  @apply checked:bg-info !important;
}
.ti-switch.ti-switch-custom.success {
  @apply checked:bg-success !important;
}
.ti-switch.ti-switch-custom.danger {
  @apply checked:bg-danger !important;
}
.ti-switch.ti-switch-custom.dark {
  @apply checked:bg-black !important;
}

.ti-switch {
  @apply bg-light !important;
}

[type=checkbox], [type=radio] {
  @apply focus:outline-none focus:shadow-none focus:ring-transparent focus:ring-offset-0 !important;
}

[type=checkbox], [type=radio] {
  @apply focus:ring-transparent !important;
}

.input-group .form-control {
  @apply border-s-0;
}

.underlined-floatiing-label textarea {
  @apply dark:focus:border-x-0 dark:focus:border-t-0 !important;
}
.underlined-floatiing-label input {
  @apply dark:focus:border-x-0 dark:focus:border-t-0 !important;
}

.wizard-tab .wizard-nav.dots .wizard-step.active:before {
  @apply bg-gray-300 !important;
}

.wizard-tab .wizard-nav.dots .wizard-step.active:after {
  @apply bg-gray-300;
}

.wizard-tab .wizard-nav.dots .wizard-step.active {
  @apply dark:before:bg-light !important;
}

.ts-control, .ts-control input, .ts-dropdown {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

/* End:: form wizard */
/* Start Modal Styles */
.ti-modal {
  @apply size-full fixed top-0 start-0 z-[999] hs-overlay-open:opacity-100 hs-overlay-open:duration-500 opacity-0 overflow-x-hidden transition-all overflow-y-auto pointer-events-none;
}
.ti-modal .ti-modal-header {
  @apply flex justify-between items-center py-3 px-4 border-b border-defaultborder dark:border-white/10;
}
.ti-modal .ti-modal-box {
  @apply sm:max-w-lg sm:w-full m-3 sm:mx-auto;
}
.ti-modal .ti-modal-content {
  @apply flex flex-col pointer-events-auto bg-white border shadow-sm rounded-sm dark:dark:bg-bodybg dark:border-white/10 dark:shadow-black/[0.05];
}
.ti-modal .ti-modal-title {
  @apply font-bold text-gray-800 dark:text-white;
}
.ti-modal .ti-modal-close-btn {
  @apply inline-flex flex-shrink-0 justify-center items-center h-8 w-8 rounded-md text-gray-500 dark:text-white dark:hover:text-white/70 hover:text-gray-500 focus:outline-none focus:ring-0 focus:ring-gray-400 focus:ring-offset-0 focus:ring-offset-white transition-all text-sm dark:focus:ring-white/10 dark:focus:ring-offset-white/10;
}
.ti-modal .ti-modal-body {
  @apply p-4;
}
.ti-modal .ti-modal-footer {
  @apply flex justify-end items-center gap-x-2 py-3 px-4 border-t border-defaultborder dark:border-white/10;
}

/* End Modal Styles */
/* Start Offcanvas Styles */
.ti-offcanvas {
  @apply fixed transition-all duration-300 transform h-full bg-white dark:dark:bg-bodybg dark:border-white/10 z-[150] shadow-md;
}
.ti-offcanvas.ti-offcanvas-left {
  @apply ltr:-translate-x-full rtl:translate-x-full top-0 start-0 max-w-[27.5rem] w-full border-e;
}
.ti-offcanvas.ti-offcanvas-top {
  @apply -translate-y-full top-0 inset-x-0 max-h-40 w-full border-b;
}
.ti-offcanvas.ti-offcanvas-right {
  @apply ltr:translate-x-full rtl:-translate-x-full top-0 end-0 max-w-[27.5rem] w-full border-s;
}
.ti-offcanvas.ti-offcanvas-bottom {
  @apply translate-y-full bottom-0 inset-x-0 max-h-40 w-full border-b;
}
.ti-offcanvas.open {
  @apply translate-x-0 translate-y-0 !important;
}
.ti-offcanvas .ti-offcanvas-header {
  @apply flex justify-between items-center py-3 px-4 border-b dark:border-white/10;
}
.ti-offcanvas .ti-offcanvas-title {
  @apply font-medium text-gray-800 dark:text-white;
}
.ti-offcanvas .ti-offcanvas-body {
  @apply overflow-y-auto h-screen p-[1rem];
}
.ti-offcanvas .ti-offcanvas-footer {
  @apply py-3 px-[1.563rem] absolute bottom-0 w-full bg-white dark:bg-bodybg border-t border-dashed border-defaultborder dark:border-defaultborder/10 shadow-md !important;
}

/* End Offcanvas Styles */
.switcher-style-head {
  @apply text-[0.8rem] font-semibold mb-0 bg-light py-[0.313rem] px-[0.625rem] text-defaulttextcolor;
}

.switcher-style {
  @apply py-[0.875rem] px-[1.563rem];
}

[type=radio] {
  @apply text-primary !important;
}

.menu-image .switch-select [type=radio] {
  @apply checked:before:content-[""] checked:border-[3px] checked:border-defaultborder !important;
}

.custom-theme-colors.switcher-style {
  @apply py-[0.938rem] px-[1.563rem];
}
.custom-theme-colors [type=radio] {
  @apply bg-none checked:before:content-[""] checked:before:font-remix checked:before:text-[22px] checked:before:text-success checked:before:relative checked:before:start-[4px] checked:before:top-[-2px] checked:before:font-medium checked:ring-transparent focus:border-transparent ring-transparent focus:ring-transparent !important;
}
.custom-theme-colors .switch-select .color-input {
  @apply w-8 h-8 rounded-full;
}
.custom-theme-colors .switch-select .color-input.form-check-input:checked {
  @apply border border-defaultborder border-solid relative before:absolute before:content-[""] before:text-success before:w-full
                before:h-full before:flex before:items-center before:justify-center before:text-[1.35rem] before:font-semibold;
}
.custom-theme-colors .switch-select .color-input.color-white {
  @apply bg-white;
}
.custom-theme-colors .switch-select .color-input.color-dark {
  @apply bg-black;
}
.custom-theme-colors .switch-select .color-input.color-primary {
  @apply bg-primary;
}
.custom-theme-colors .switch-select .color-input.color-primary-1 {
  @apply bg-[#7647e5];
}
.custom-theme-colors .switch-select .color-input.color-primary-2 {
  @apply bg-[#3f4bec];
}
.custom-theme-colors .switch-select .color-input.color-primary-3 {
  @apply bg-[#377dce];
}
.custom-theme-colors .switch-select .color-input.color-primary-4 {
  @apply bg-[#019fa2];
}
.custom-theme-colors .switch-select .color-input.color-primary-5 {
  @apply bg-[#8b9504];
}
.custom-theme-colors .switch-select .color-input.color-gradient {
  @apply bg-gradient-to-tr from-primary to-[#6e72a8] !important;
}
.custom-theme-colors .switch-select .color-input.color-transparent {
  @apply bg-[url("../public/assets/images/menu-bg-images/transparent.png")] bg-cover bg-center !important;
}
.custom-theme-colors .switch-select .color-input.color-bg-1 {
  @apply bg-[#0c175b];
}
.custom-theme-colors .switch-select .color-input.color-bg-2 {
  @apply bg-[#320b6e];
}
.custom-theme-colors .switch-select .color-input.color-bg-3 {
  @apply bg-[#085171];
}
.custom-theme-colors .switch-select .color-input.color-bg-4 {
  @apply bg-[#03513c];
}
.custom-theme-colors .switch-select .color-input.color-bg-5 {
  @apply bg-[#494e01];
}

.menu-image .bgimage-input {
  @apply w-[3.5rem] h-[5.625rem] rounded-md border-0;
}
.menu-image .bgimage-input.form-check-input:focus {
  @apply border-transparent shadow-sm;
}
.menu-image .bgimage-input.bg-img1 {
  @apply bg-[url("../public/assets/images/menu-bg-images/bg-img1.jpg")] bg-center bg-cover bg-no-repeat !important;
}
.menu-image .bgimage-input.bg-img1.form-check-input:checked[type=radio] {
  @apply bg-none !important;
}
.menu-image .bgimage-input.bg-img2 {
  @apply bg-[url("../public/assets/images/menu-bg-images/bg-img2.jpg")] bg-center bg-cover bg-no-repeat !important;
}
.menu-image .bgimage-input.bg-img2.form-check-input:checked[type=radio] {
  @apply bg-none !important;
}
.menu-image .bgimage-input.bg-img3 {
  @apply bg-[url("../public/assets/images/menu-bg-images/bg-img3.jpg")] bg-center bg-cover bg-no-repeat !important;
}
.menu-image .bgimage-input.bg-img3.form-check-input:checked[type=radio] {
  @apply bg-none !important;
}
.menu-image .bgimage-input.bg-img4 {
  @apply bg-[url("../public/assets/images/menu-bg-images/bg-img4.jpg")] bg-center bg-cover bg-no-repeat !important;
}
.menu-image .bgimage-input.bg-img4.form-check-input:checked[type=radio] {
  @apply bg-none !important;
}
.menu-image .bgimage-input.bg-img5 {
  @apply bg-[url("../public/assets/images/menu-bg-images/bg-img5.jpg")] bg-center bg-cover bg-no-repeat !important;
}
.menu-image .bgimage-input.bg-img5.form-check-input:checked[type=radio] {
  @apply bg-none !important;
}

.pickr-container-primary .pickr .pcr-button,
.pickr-container-background .pickr .pcr-button {
  @apply h-8 w-8 overflow-hidden relative rounded-full border border-solid bg-primary border-inputborder -top-[4px] after:text-white/70 after:text-[1.25rem] !important;
}
.pickr-container-primary .pickr .pcr-button:focus,
.pickr-container-background .pickr .pcr-button:focus {
  @apply shadow-none;
}

#hs-overlay-switcher .custom-theme-colors .custom-container-primary button,
#hs-overlay-switcher .custom-theme-colors .custom-container-background button {
  @apply hidden !important;
}

.pickr-container-primary .pickr .pcr-button,
.pickr-container-background .pickr .pcr-button {
  @apply -top-[4px] h-8 w-8 overflow-hidden rounded-full border border-solid bg-primary !important;
}

.pickr-container-primary .pickr .pcr-button::after,
.pickr-container-background .pickr .pcr-button::after {
  @apply content-[""] font-["remixicon"] text-white/70 !important;
}

/* Start Pagination Styles */
.ti-pagination {
  @apply flex items-center;
}
.ti-pagination li .page-link {
  @apply border-s border-t border-b border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor leading-[1.5] dark:text-defaulttextcolor/80 hover:text-primary py-[0.375rem] px-[0.75rem] inline-flex items-center text-[0.8125rem] font-normal gap-2;
}
.ti-pagination li .page-link.active {
  @apply bg-primary text-white border-primary !important;
}
.ti-pagination li .page-link.disabled {
  @apply pointer-events-none text-opacity-30;
}
.ti-pagination li .page-link:last-child {
  @apply border-e border-defaultborder dark:border-defaultborder/10;
}
.ti-pagination li:not(:first-child) .page-link {
  @apply ms-[calc(1px_*_-1)];
}
.ti-pagination li:first-child .page-link {
  @apply rounded-s-md;
}
.ti-pagination li:last-child .page-link {
  @apply rounded-e-md;
}
.ti-pagination.pagination-sm li .page-link {
  @apply py-2 px-2 text-xs;
}
.ti-pagination.pagination-sm li:first-child .page-link {
  @apply rounded-s-md;
}
.ti-pagination.pagination-sm li:last-child .page-link {
  @apply rounded-e-md;
}
.ti-pagination.pagination-lg li .page-link {
  @apply py-3 sm:px-6 px-3 text-lg;
}
.ti-pagination.pagination-lg li:first-child .page-link {
  @apply rounded-s-md;
}
.ti-pagination.pagination-lg li:last-child .page-link {
  @apply rounded-e-md;
}

.pagination-style-1 .ti-pagination li {
  @apply space-x-2;
}
.pagination-style-1 .ti-pagination li .page-link {
  @apply border-0 rounded-full leading-none py-[0.375rem] px-3;
}

.pagination-style-2 .ti-pagination li {
  @apply space-x-2;
}
.pagination-style-2 .ti-pagination li .page-link {
  @apply border-0 rounded-[0.3rem] leading-none py-[0.375rem] px-3;
}
.pagination-style-2 .ti-pagination li .page-link.active {
  @apply bg-primary text-white relative font-medium before:absolute before:h-1 leading-[1.5] before:w-full before:inset-x-0 before:bottom-0 before:bg-primary before:hidden;
}

.pagination-style-3 .ti-pagination li {
  @apply space-x-2;
}
.pagination-style-3 .ti-pagination li .page-link {
  @apply border-0 rounded-full leading-none py-[0.375rem] px-3;
}

/* End Pagination Styles */
.progress {
  @apply bg-light h-[0.75rem] rounded-sm flex w-full;
}

/* Start:: progressbar-height */
.progress.progress-xs,
.progress-stacked.progress-xs {
  @apply h-[0.25rem];
}
.progress.progress-sm,
.progress-stacked.progress-sm {
  @apply h-[0.5rem];
}
.progress.progress-lg,
.progress-stacked.progress-lg {
  @apply h-[1rem];
}
.progress.progress-xl,
.progress-stacked.progress-xl {
  @apply h-[1.25rem];
}

/* End:: progressbar-height */
.progress-stacked {
  @apply bg-light;
}

.progress-bar {
  @apply text-white bg-primary flex justify-center items-center rounded-s-full;
}

@keyframes progress-animate {
  0% {
    @apply w-0;
  }
}
.progress-animate {
  @apply relative rounded-[0.625rem];
}
.progress-animate .progress-bar {
  @apply relative rounded-[0.625rem];
}

/* Start:: custom-progress */
.progress-custom {
  @apply overflow-visible relative;
}
.progress-custom .progress-bar {
  @apply relative overflow-visible after:w-[1.15rem] after:h-[1.15rem] after:rounded-[50%] after:bg-white after:absolute after:-end-[0.375rem]
        after:-top-[0.375rem] after:border-[0.25rem] after:border-solid after:border-primary;
}
.progress-custom .progress-bar.progress-secondary {
  @apply bg-secondary after:border-secondary !important;
}
.progress-custom .progress-bar.progress-secondary .progress-bar-value {
  @apply after:border-t-secondary;
}
.progress-custom .progress-bar.progress-warning {
  @apply bg-warning after:border-warning !important;
}
.progress-custom .progress-bar.progress-warning .progress-bar-value {
  @apply after:border-t-warning;
}
.progress-custom .progress-bar.progress-info {
  @apply bg-info after:border-info !important;
}
.progress-custom .progress-bar.progress-info .progress-bar-value {
  @apply after:border-t-info;
}
.progress-custom .progress-bar.progress-success {
  @apply bg-success after:border-success !important;
}
.progress-custom .progress-bar.progress-success .progress-bar-value {
  @apply after:border-t-success;
}
.progress-custom .progress-bar.progress-danger {
  @apply bg-danger after:border-danger !important;
}
.progress-custom .progress-bar.progress-danger .progress-bar-value {
  @apply after:border-t-danger;
}
.progress-custom .progress-bar-value {
  @apply w-[1.875rem] h-[1.25rem] leading-[1.25rem] rounded-[.188rem] bg-primary shadow-[0_0.313rem_0.313rem_rgba(0,0,0,.4)] dark:shadow-[rgba(255,255,255,.4)] text-[.625rem] font-semibold text-white text-center absolute bottom-6 end-[-0.688rem]
    after:absolute after:border-x-[0.438rem] after:border-x-transparent after:border-t-[0.438rem] after:border-primary after:bottom-[-0.375rem] after:start-[28%];
}

.progress-bar-title {
  @apply py-[0.313rem] px-[0.625rem] m-0 bg-primary rounded-s-sm text-[0.625rem] font-semibold text-white uppercase absolute -top-[10px] start-0 z-[1] after:absolute after:top-[1px] after:-end-[1.063rem] after:border-t-[.85rem] after:border-b-[.85rem] after:border-transparent after:border-s-[1.063rem] after:border-solid after:border-s-primary;
}

/* Start:: custom-progress-2 */
.progress-item-1,
.progress-item-2,
.progress-item-3 {
  @apply absolute -mt-1 z-[1] h-[0.938rem] w-[0.98rem] rounded-[1.563rem] bg-light;
}

.progress-item-1 {
  @apply start-[25%];
}

.progress-item-2 {
  @apply start-[50%];
}

.progress-item-3 {
  @apply start-[75%];
}

/* End:: custom-progress-2 */
/* Start:: custom-progress-3 */
.custom-progress-3 {
  @apply visible rounded-[0.625rem];
}
.custom-progress-3 .progress-bar {
  @apply relative visible m-[0.313rem] rounded-sm
        before:absolute before:w-1 before:h-1 before:start-[0.125rem] before:bg-white before:rounded-md;
}
.custom-progress-3 .progress-bar .progress-bar-value {
  @apply border-[0.125rem] border-solid border-primary w-[2.5rem] h-[1.563rem] leading-[1.563rem] rounded-[3.125rem] bg-white dark:bg-bodybg text-[0.625rem] font-semibold
            text-primary text-center absolute -end-[0.625rem];
}
.custom-progress-3 .progress-bar .progress-bar-value.primary {
  @apply border-[0.125rem] border-solid border-primary text-primary;
}
.custom-progress-3 .progress-bar .progress-bar-value.secondary {
  @apply border-[0.125rem] border-solid border-secondary text-secondary;
}
.custom-progress-3 .progress-bar .progress-bar-value.warning {
  @apply border-[0.125rem] border-solid border-warning text-warning;
}
.custom-progress-3 .progress-bar .progress-bar-value.info {
  @apply border-[0.125rem] border-solid border-info text-info;
}
.custom-progress-3 .progress-bar .progress-bar-value.success {
  @apply border-[0.125rem] border-solid border-success text-success;
}
.custom-progress-3 .progress-bar .progress-bar-value.danger {
  @apply border-[0.125rem] border-solid border-danger text-danger;
}

/* End:: custom-progress-3 */
/* Start:: custom-progress-4 */
.custom-progress-4.progress {
  @apply bg-primary/10 rounded-sm;
}
.custom-progress-4.progress.secondary {
  @apply bg-secondary/10;
}
.custom-progress-4.progress.secondary .progress-bar-label {
  @apply text-secondary;
}
.custom-progress-4.progress.warning {
  @apply bg-warning/10;
}
.custom-progress-4.progress.warning .progress-bar-label {
  @apply text-warning;
}
.custom-progress-4.progress.info {
  @apply bg-info/10;
}
.custom-progress-4.progress.info .progress-bar-label {
  @apply text-info;
}
.custom-progress-4.progress.success {
  @apply bg-success/10;
}
.custom-progress-4.progress.success .progress-bar-label {
  @apply text-success;
}
.custom-progress-4.progress.danger {
  @apply bg-danger/10;
}
.custom-progress-4.progress.danger .progress-bar-label {
  @apply text-danger;
}
.custom-progress-4.progress .progress-bar {
  @apply rounded-sm;
}
.custom-progress-4.progress .progress-bar-label {
  @apply absolute end-[0.313rem] font-semibold text-[0.813rem] text-primary;
}

/* End:: custom-progress-4 */
/* Start Table Styles */
.table {
  @apply text-defaulttextcolor rounded-md mb-0 text-start w-full;
  /* Table header border-radius styling */
}
.table tr {
  @apply dark:border-defaultborder/10;
}
.table tbody tr th {
  @apply font-medium;
}
.table th,
.table td {
  @apply p-[0.75rem] align-middle leading-[1.462] text-[0.813rem] font-medium text-start;
}
.table thead tr th {
  @apply font-semibold text-[0.85rem];
}
.table thead {
  @apply rounded-lg overflow-hidden;
}
.table thead tr {
  @apply rounded-lg overflow-hidden;
}
.table thead tr th:first-child {
  @apply rounded-tl-lg;
}
.table thead tr th:last-child {
  @apply rounded-tr-lg;
}
.table.table-sm > :not(caption) > * > * {
  @apply p-[0.3rem];
}
.table.table-dark {
  @apply text-white/70 border-white/10 dark:border-black/[0.025];
}
.table.table-primary {
  @apply text-primary/70 border-primary/10;
}
.table.table-bordered.table-bordered-primary tbody,
.table.table-bordered.table-bordered-primary td,
.table.table-bordered.table-bordered-primary tfoot,
.table.table-bordered.table-bordered-primary th,
.table.table-bordered.table-bordered-primary thead,
.table.table-bordered.table-bordered-primary tr {
  @apply border-primary/30 !important;
}
.table.table-bordered.table-bordered-info tbody,
.table.table-bordered.table-bordered-info td,
.table.table-bordered.table-bordered-info tfoot,
.table.table-bordered.table-bordered-info th,
.table.table-bordered.table-bordered-info thead,
.table.table-bordered.table-bordered-info tr {
  @apply border-info/30;
}
.table.table-bordered.table-bordered-secondary tbody,
.table.table-bordered.table-bordered-secondary td,
.table.table-bordered.table-bordered-secondary tfoot,
.table.table-bordered.table-bordered-secondary th,
.table.table-bordered.table-bordered-secondary thead,
.table.table-bordered.table-bordered-secondary tr {
  @apply border-secondary/30;
}
.table.table-bordered.table-bordered-warning tbody,
.table.table-bordered.table-bordered-warning td,
.table.table-bordered.table-bordered-warning tfoot,
.table.table-bordered.table-bordered-warning th,
.table.table-bordered.table-bordered-warning thead,
.table.table-bordered.table-bordered-warning tr {
  @apply border-warning/30;
}
.table.table-bordered.table-bordered-success tbody,
.table.table-bordered.table-bordered-success td,
.table.table-bordered.table-bordered-success tfoot,
.table.table-bordered.table-bordered-success th,
.table.table-bordered.table-bordered-success thead,
.table.table-bordered.table-bordered-success tr {
  @apply border-success/30;
}
.table.table-bordered.table-bordered-danger tbody,
.table.table-bordered.table-bordered-danger td,
.table.table-bordered.table-bordered-danger tfoot,
.table.table-bordered.table-bordered-danger th,
.table.table-bordered.table-bordered-danger thead,
.table.table-bordered.table-bordered-danger tr {
  @apply border-danger/30;
}
.table.table-striped > tbody > tr:nth-of-type(odd) > * {
  @apply text-defaulttextcolor;
}
.table.table-striped-columns > :not(caption) > tr > :nth-child(2n) {
  @apply text-defaulttextcolor;
}
.table tbody.table-group-divider {
  @apply border-t border-solid rounded-md;
}
.table.table-hover > tbody > tr:hover > * {
  @apply text-defaulttextcolor;
}
.table .table-active {
  @apply text-defaulttextcolor;
}

.ti-custom-table-head {
  @apply divide-y divide-defaultborder dark:divide-defaultborder/10;
}

.table-responsive,
.table {
  @apply overflow-y-visible !important;
}

.table-responsive {
  @apply block w-full overflow-x-auto;
}

.table-responsive {
  @apply overflow-x-auto;
}

.ti-custom-table.without-borders tbody {
  @apply divide-y-0 !important;
}

.ti-custom-table {
  @apply min-w-full;
  /* Table header border-radius styling for ti-custom-table */
}
.ti-custom-table thead {
  @apply rounded-lg overflow-hidden;
}
.ti-custom-table thead tr {
  @apply rounded-lg overflow-hidden;
}
.ti-custom-table thead tr th:first-child {
  @apply rounded-tl-lg;
}
.ti-custom-table thead tr th:last-child {
  @apply rounded-tr-lg;
}
.ti-custom-table th {
  @apply px-4 py-3 text-start leading-[1.462] font-medium text-[0.85rem] dark:text-defaulttextcolor/80;
}
.ti-custom-table tbody {
  @apply divide-y divide-defaultborder dark:divide-defaultborder/10;
}
.ti-custom-table td {
  @apply px-4 py-[0.85rem] whitespace-nowrap text-base dark:text-gray-400 font-rubik font-normal leading-none tracking-normal;
}

.ti-striped-table tbody tr {
  @apply odd:bg-white even:bg-gray-100 dark:odd:dark:bg-bodybg dark:even:bg-black/20;
}

.ti-custom-table-hover tbody tr {
  @apply hover:bg-gray-100 dark:hover:bg-light;
}

.table-bordered {
  @apply border dark:border-defaultborder/10;
}
.table-bordered tr {
  @apply divide-x rtl:divide-x-reverse divide-defaultborder dark:divide-defaultborder/10;
}

.table-bordered-default {
  @apply border border-defaultborder dark:border-defaultborder/10;
}
.table-bordered-default .ti-custom-table-head {
  @apply divide-y divide-defaultborder dark:divide-defaultborder/10;
}
.table-bordered-default tbody {
  @apply divide-y divide-defaultborder dark:divide-defaultborder/10;
}
.table-bordered-default tr {
  @apply divide-x rtl:divide-x-reverse divide-defaultborder dark:divide-defaultborder/10;
}

.table-bordered-primary {
  @apply border border-primary/30 dark:border-primary/30;
}
.table-bordered-primary .ti-custom-table-head {
  @apply divide-y divide-primary/30 dark:divide-primary/30;
}
.table-bordered-primary tbody {
  @apply divide-y divide-primary/30 dark:divide-primary/30;
}
.table-bordered-primary tr {
  @apply divide-x rtl:divide-x-reverse divide-primary/30 dark:divide-primary/30;
}

.table-bordered-secondary {
  @apply border border-secondary/30 dark:border-secondary/30;
}
.table-bordered-secondary .ti-custom-table-head {
  @apply divide-y divide-secondary/30 dark:divide-secondary/30;
}
.table-bordered-secondary tbody {
  @apply divide-y divide-secondary/30 dark:divide-secondary/30;
}
.table-bordered-secondary tr {
  @apply divide-x rtl:divide-x-reverse divide-secondary/30 dark:divide-secondary/30;
}

.table-bordered-warning {
  @apply border border-warning/30 dark:border-warning/30;
}
.table-bordered-warning .ti-custom-table-head {
  @apply divide-y divide-warning/30 dark:divide-warning/30;
}
.table-bordered-warning tbody {
  @apply divide-y divide-warning/30 dark:divide-warning/30;
}
.table-bordered-warning tr {
  @apply divide-x rtl:divide-x-reverse divide-warning/30 dark:divide-warning/30;
}

.table-bordered-danger {
  @apply border border-danger/30 dark:border-danger/30;
}
.table-bordered-danger .ti-custom-table-head {
  @apply divide-y divide-danger/30 dark:divide-danger/30;
}
.table-bordered-danger tbody {
  @apply divide-y divide-danger/30 dark:divide-danger/30;
}
.table-bordered-danger tr {
  @apply divide-x rtl:divide-x-reverse divide-danger/30 dark:divide-danger/30;
}

.table-bordered-info {
  @apply border border-info/30 dark:border-info/30;
}
.table-bordered-info .ti-custom-table-head {
  @apply divide-y divide-info/30 dark:divide-info/30;
}
.table-bordered-info tbody {
  @apply divide-y divide-info/30 dark:divide-info/30;
}
.table-bordered-info tr {
  @apply divide-x rtl:divide-x-reverse divide-info/30 dark:divide-info/30;
}

.table-bordered-success {
  @apply border border-success/30 dark:border-success/30;
}
.table-bordered-success .ti-custom-table-head {
  @apply divide-y divide-success/30 dark:divide-success/30;
}
.table-bordered-success tbody {
  @apply divide-y divide-success/30 dark:divide-success/30;
}
.table-bordered-success tr {
  @apply divide-x rtl:divide-x-reverse divide-success/30 dark:divide-success/30;
}

.table-primary {
  @apply bg-primary/10;
}
.table-primary th {
  @apply text-black dark:text-white;
}
.table-primary tbody {
  @apply divide-y divide-primary/10 dark:divide-primary/10;
}
.table-primary td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-primary tr {
  @apply divide-x rtl:divide-x-reverse divide-primary/10 dark:divide-primary/10;
}
.table-primary .ti-custom-table-head {
  @apply divide-y divide-primary/10 dark:divide-primary/10;
}

.table-dark {
  @apply bg-black/10 dark:bg-bodybg2;
}
.table-dark th {
  @apply text-black dark:text-white;
}
.table-dark tbody {
  @apply divide-y divide-black/10 dark:divide-black/[0.025];
}
.table-dark td {
  @apply text-black dark:text-white;
}
.table-dark tr {
  @apply divide-x rtl:divide-x-reverse divide-black/10 dark:divide-black/[0.025];
}
.table-dark .ti-custom-table-head {
  @apply divide-y divide-black/10 dark:divide-black/[0.025];
}

.table-secondary {
  @apply bg-secondary/10;
}
.table-secondary th {
  @apply text-black dark:text-white;
}
.table-secondary tbody {
  @apply divide-y divide-secondary/10 dark:divide-secondary/10;
}
.table-secondary td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-secondary tr {
  @apply divide-x rtl:divide-x-reverse divide-secondary/10 dark:divide-secondary/10;
}
.table-secondary .ti-custom-table-head {
  @apply divide-y divide-secondary/10 dark:divide-secondary/10;
}

.table-warning {
  @apply bg-warning/10;
}
.table-warning th {
  @apply text-black dark:text-white;
}
.table-warning tbody {
  @apply divide-y divide-warning/10 dark:divide-warning/10;
}
.table-warning td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-warning tr {
  @apply divide-x rtl:divide-x-reverse divide-warning/10 dark:divide-warning/10;
}
.table-warning .ti-custom-table-head {
  @apply divide-y divide-warning/10 dark:divide-warning/10;
}

.table-info {
  @apply bg-info/10;
}
.table-info th {
  @apply text-black dark:text-white;
}
.table-info tbody {
  @apply divide-y divide-info/10 dark:divide-info/10;
}
.table-info td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-info tr {
  @apply divide-x rtl:divide-x-reverse divide-info/10 dark:divide-info/10;
}
.table-info .ti-custom-table-head {
  @apply divide-y divide-info/10 dark:divide-info/10;
}

.table-danger {
  @apply bg-danger/10;
}
.table-danger th {
  @apply text-black dark:text-white;
}
.table-danger tbody {
  @apply divide-y divide-danger/10 dark:divide-danger/10;
}
.table-danger td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-danger tr {
  @apply divide-x rtl:divide-x-reverse divide-danger/10 dark:divide-danger/10;
}
.table-danger .ti-custom-table-head {
  @apply divide-y divide-danger/10 dark:divide-danger/10;
}

.table-success {
  @apply bg-success/10;
}
.table-success th {
  @apply text-black dark:text-white;
}
.table-success tbody {
  @apply divide-y divide-success/10 dark:divide-success/10;
}
.table-success td {
  @apply text-gray-800 dark:text-gray-200;
}
.table-success tr {
  @apply divide-x rtl:divide-x-reverse divide-success/10 dark:divide-success/10;
}
.table-success .ti-custom-table-head {
  @apply divide-y divide-success/10 dark:divide-success/10;
}

.ti-head-primary th {
  @apply bg-primary/20 dark:bg-primary/20;
}

.ti-head-secondary th {
  @apply bg-secondary/20 dark:bg-secondary/20;
}

.ti-head-warning th {
  @apply bg-warning/20 dark:bg-warning/20;
}

.ti-head-success th {
  @apply bg-success/20 dark:bg-success/20;
}

.ti-head-info th {
  @apply bg-info/20 dark:bg-info/20;
}

.ti-head-danger th {
  @apply bg-danger/20 dark:bg-danger/20;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  @apply bg-black/[0.0125] text-defaulttextcolor;
}

.table-striped-columns > :not(caption) > tr > :nth-child(2n) {
  @apply bg-black/[0.0125] text-defaulttextcolor dark:bg-black/20;
}

.table.table-success.table-striped > tbody > tr:nth-of-type(odd) > * {
  @apply bg-success/20;
}

caption {
  @apply text-textmuted dark:text-textmuted/50 pt-2 pb-2 text-start;
}

.table-active {
  @apply bg-light;
}

.table th, .table td {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}

.table-striped-columns > :not(caption) > tr > :nth-child(2n),
.ti-striped-table tbody tr:nth-child(even) {
  @apply bg-light !important;
}

/* End Table Styles */
.toast-container {
  @apply z-[1090] max-w-full pointer-events-none;
}

.toast {
  @apply bg-white border border-solid border-defaultborder shadow-sm rounded-md;
}
.toast .toast-header {
  @apply bg-light py-[0.375rem] px-3 flex items-center;
}
.toast .toast-header img {
  @apply w-[1.25rem] h-[1.25rem];
}
.toast .toast-header {
  @apply border-b border-solid border-defaultborder;
}
.toast .toast-header .btn-close {
  @apply me-[0.125rem] p-3;
}
.toast .toast-body {
  @apply text-[0.8rem] p-3;
}
.toast .btn-close {
  @apply text-[0.563rem];
}
.toast.colored-toast {
  @apply border-0 backdrop-blur-[20px] !important;
}
.toast.colored-toast .btn-close {
  @apply invert-[1];
}
.toast.colored-toast .toast-header {
  @apply border-b border-solid border-black/10;
}

.bd-example-toasts {
  @apply min-h-[15rem];
}

/* Start Tooltip Styles */
.ti-main-tooltip {
  @apply inline-block relative;
}
.ti-main-tooltip .ti-main-tooltip-toggle {
  @apply block text-center;
}

.ti-main-tooltip-content {
  @apply leading-4 hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity inline-block absolute invisible z-50 py-1 px-3 bg-white text-sm text-gray-600 rounded-sm shadow-md dark:bg-bodybg dark:text-white/70;
}

/* End Tooltip Styles */
/* Global */
/* Start:: React-Datepicker */
.input-group .react-datepicker__input-container input {
  @apply rounded-ss-none rounded-es-none !important;
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle {
  @apply stroke-defaultborder dark:stroke-defaultborder/10 !important;
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected {
  @apply bg-primarytint2color !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  @apply bg-primary !important;
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
  @apply fill-defaultborder dark:fill-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 stroke-transparent !important;
}

.page-header-breadcrumb .react-datepicker-popper {
  @apply z-[2] !important;
}

.react-datepicker-wrapper {
  @apply block !important;
}

.react-datepicker__input-container input {
  @apply relative flex-grow w-full min-w-0 h-[36px] rounded-sm border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg py-[0.375rem] px-3;
}

.react-datepicker, .react-datepicker__header {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 !important;
}

.react-datepicker__current-month, .react-datepicker-time__header, .react-datepicker-year-header,
.react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.react-datepicker__time-list {
  @apply bg-white dark:bg-bodybg !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover,
.react-datepicker__day:hover, .react-datepicker__month-text:hover, .react-datepicker__quarter-text:hover, .react-datepicker__year-text:hover {
  @apply bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range, .react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--in-range {
  @apply bg-primary text-white !important;
}

.react-datepicker__input-container input:focus-visible {
  @apply outline-none !important;
}

.react-datepicker__time-list-item {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

/* End:: React-Datepicker */
/* Start:: React-select */
.Select2__option--is-focused {
  @apply dark:bg-bodybg !important;
}

.Select2__control {
  @apply rounded-sm !important;
}

.Select2__menu {
  @apply bg-white border border-solid dark:bg-bodybg border-inputborder dark:border-white/10 !important;
}

.Select2__option:hover {
  @apply text-[#fff] bg-primary !important;
}

.Select2__menu div.active,
.Select2__menu div .Select2__option--is-selected,
.Select2__menu li.active,
.Select2__menu li .Select2__option--is-selected,
.Select2__menu .Select2__single-value.active,
.Select2__menu .Select2__single-value .Select2__option--is-selected {
  @apply bg-primary !important;
}

.Select2__option--is-selected,
.Select2__option--is-focused {
  @apply bg-white text-defaulttextcolor !important;
}

.Select2__option:hover {
  @apply bg-primary !important;
}

.Select2__control {
  @apply bg-white border-defaultborder dark:bg-bodybg dark:border-defaultborder/10 shadow-none !important;
}

.Select2__option img, .Select2__single-value img {
  @apply w-[30px] h-[30px] rounded-full !important;
}

.Select2__input-container, .Select2__single-value {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 !important;
}

.default .Select2__multi-value__label {
  @apply py-[0.15rem] px-[0.625rem] bg-primary text-white !important;
}
.default .Select2__multi-value__remove {
  @apply hidden;
}

.Select2__multi-value {
  @apply bg-primary text-[#fff] rounded-[4px] !important;
}

.Select2__multi-value__label {
  @apply text-[#fff] !important;
}

/* End:: React-select */
/* Start:: Customstyles */
#hs-wrapper-select-for-copy select {
  @apply w-full border-defaultborder dark:border-defaultborder/10 rounded-sm bg-none !important;
}

.Select2__option img,
.Select2__single-value img {
  width: 30px !important;
  height: 30px !important;
  border-radius: 50%;
}

.Select2__indicator-separator {
  @apply hidden;
}

.js-example-templating .Select2__single-value img {
  @apply hidden;
}

#secondary-colored-slider .css-1diafny-MuiSlider-root {
  @apply text-[#9e5cf7] !important;
}

#warning-colored-slider .MuiSlider-root {
  @apply text-warning !important;
}

#info-colored-slider .MuiSlider-root {
  @apply text-info !important;
}

#success-colored-slider .MuiSlider-root {
  @apply text-success !important;
}

#danger-colored-slider .MuiSlider-root {
  @apply text-danger !important;
}

.range-slider__thumb {
  @apply left-[calc(50%+0px)] !important;
}

.range-slider__range {
  @apply left-0 w-[50%] !important;
}

.MuiSlider-root.MuiSlider-colorPrimary {
  @apply text-[#5c67f7];
}

.range-slider .range-slider__thumb {
  @apply bg-[#5c67f7];
}

.range-slider .range-slider__range {
  @apply bg-[#5c67f7];
}

.square-thumb.range-slider .range-slider__thumb {
  @apply rounded-[5px];
}

.single-thumb .range-slider__range {
  @apply rounded-[6px];
}

.react-select__indicator-separator {
  @apply hidden;
}

.default .Select2__multi-value__label {
  @apply py-[0.15rem] px-[0.625rem] bg-primary text-white !important;
}
.default .Select2__multi-value__remove {
  @apply hidden;
}

@media (max-width: 800px) {
  .MuiStepper-root.MuiStepper-horizontal {
    @apply block;
  }
  .MuiStepConnector-line.MuiStepConnector-lineHorizontal {
    @apply border-transparent;
  }
  .MuiStep-root.MuiStep-horizontal {
    @apply mb-[10px];
  }
  .wizard-tab .wizard-content {
    @apply p-4;
  }
  .MuiButtonBase-root.MuiStepButton-root {
    @apply justify-start -my-[20px] !important;
  }
}
.MuiStepConnector-line.MuiStepConnector-lineHorizontal {
  @apply border-defaultborder !important;
}

.MuiStepContent-root,
.MuiStepConnector-line.MuiStepConnector-lineVertical {
  @apply border-defaultborder !important;
}

.MuiStepLabel-label {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}
.MuiStepLabel-label.Mui-active {
  @apply text-primary !important;
}

.MuiStepLabel-root .MuiSvgIcon-root {
  @apply text-white opacity-60 border-[3px] border-textmuted rounded-full w-[0.8em] h-[0.8em] !important;
}

.MuiStepLabel-iconContainer.Mui-disabled {
  @apply pe-[4px] !important;
}

.MuiStepLabel-iconContainer {
  @apply pe-[8px] !important;
}

.MuiStep-root.MuiStep-horizontal {
  @apply my-[10x] !important;
}

.MuiTypography-root {
  @apply font-defaultfont !important;
}

.Mui-active .MuiStepIcon-root.MuiSvgIcon-fontSizeMedium {
  @apply text-primary rounded-full shadow-[0_0_0_3px_var(--primary02)] !important;
}

.MuiStepIcon-text {
  @apply hidden;
}

.wizard-tab .MuiBox-root:first-child {
  @apply pt-0 pb-[25px] px-[20px];
}
.wizard-tab .MuiBox-root {
  @apply pt-0;
}

.wizard-content {
  @apply p-8 transition-all ease-in duration-[0.3s] !important;
}

.header-link .badge.custom-header-icon-pulse {
  @apply block absolute p-0 top-[6px] end-[18px];
}

.data-table-extensions-action {
  @apply hidden;
}

.data-table-extensions > .data-table-extensions-filter {
  @apply float-right !important;
}

.data-table-extensions-filter {
  @apply border border-defaultborder dark:border-defaultborder/10 rounded-sm !important;
}
.data-table-extensions-filter .icon {
  @apply mt-1 text-textmuted dark:text-defaulttextcolor/80;
}
.data-table-extensions-filter input {
  @apply border-0 !important;
}

.rdt_Table {
  @apply border bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 !important;
}
.rdt_Table .rdt_TableRow {
  @apply border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.rdt_TableHeader {
  @apply border bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 !important;
}

.rdt_TableHeadRow {
  @apply border bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.rdt_Pagination {
  @apply border bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}
.rdt_Pagination svg {
  @apply hidden !important;
}
.rdt_Pagination button svg {
  @apply fill-defaulttextcolor dark:fill-defaulttextcolor/80 block !important;
}

.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1 {
  @apply bg-white dark:bg-bodybg shadow-none !important;
}
.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1 table {
  @apply border-defaultborder dark:border-defaultborder/10;
}
.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1 .MuiTableCell-root.MuiTableCell-body {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}
.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1 .MuiTableCell-root.MuiTableCell-head.MuiTableCell-sizeMedium {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 !important;
}

.MuiTable-root.MuiTable-stickyHeader.sticky-header-table {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 border-collapse !important;
}

.sticky-header-table .MuiTableCell-root.MuiTableCell-body {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80;
}
.sticky-header-table .MuiTableCell-root.MuiTableCell-head.MuiTableCell-stickyHeader {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.MuiToolbar-root.MuiToolbar-gutters.MuiToolbar-regular.MuiTablePagination-toolbar {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 !important;
}

.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded {
  @apply rounded-none !important;
}

.MuiTablePagination-root.css-7mt0f-MuiTablePagination-root {
  @apply hidden;
}

.stdropdown-container {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}
.stdropdown-container input {
  @apply border-0 !important;
}
.stdropdown-container .stdropdown-tool svg {
  @apply fill-defaulttextcolor dark:fill-defaulttextcolor/80 !important;
}

.stdropdown-menu {
  border: 1px solid var(--default-border) !important;
  background-color: var(--custom-white) !important;
}

.ms-container .ms-selectable,
.ms-container .ms-selection {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 !important;
}

.ms-elem-selectable {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 !important;
}

.ms-container .ms-selectable li:hover,
.ms-container .ms-selection li:hover {
  @apply bg-primary text-white !important;
}

.ms-container .ms-selectable li.selected,
.ms-container .ms-selection li.selected {
  @apply bg-primary text-white border-primary !important;
}

.ms-selectionpanel {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 !important;
}

.ms-selectionpanel2 {
  @apply hidden !important;
}

.ms-list {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/80 border-defaultborder dark:border-defaultborder/10 !important;
}

.ms-container .ms-selectable li.ms-elem-selectable,
.ms-container .ms-selection li.ms-elem-selection {
  @apply border-defaultborder dark:border-defaultborder/10 !important;
}

.tags-input .tagify {
  @apply w-full rounded-sm !important;
}

.rdt_TableHeader {
  @apply border-0 bg-white dark:bg-bodybg !important;
}

.stdropdown-menu {
  @apply border border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg !important;
}

.sun-editor .se-toolbar {
  @apply bg-white dark:bg-bodybg outline-1 outline-defaultborder dark:outline-defaultborder/10;
}

.sun-editor {
  @apply border border-defaultborder dark:border-defaultborder/10 dark:bg-bodybg;
}

.sun-editor .se-resizing-bar {
  @apply border-t-defaultborder dark:border-t-defaultborder/10 border-t border-solid dark:bg-bodybg;
}

.sun-editor .se-btn-module-border {
  @apply border border-defaultborder dark:border-defaultborder/10;
}

.sun-editor button {
  @apply dark:text-defaulttextcolor/70;
}

.sun-editor .se-btn:enabled:focus,
.sun-editor .se-btn:enabled:hover {
  @apply dark:bg-bodybg2;
}

.sun-editor .se-container {
  @apply z-0;
}

.sun-editor-editable * {
  @apply dark:text-defaulttextcolor/70;
}

.sun-editor-editable {
  @apply bg-white dark:bg-bodybg;
}

.top-landing-pages-list li {
  @apply mb-4;
}
.top-landing-pages-list li:last-child {
  @apply mb-0;
}

.pickr-container-primary .pickr .pcr-button {
  @apply after:bg-primary !important;
}

.buy-crypto .Select2__control {
  @apply rounded-ss-[0] rounded-es-[0] !important;
}
.buy-crypto .Select2__control input[type=text] {
  @apply dark:bg-transparent !important;
}

.Select2__dropdown-indicator:after {
  @apply pointer-events-none absolute mt-[-2.5px] h-0 w-0 border-t-textmuted dark:border-t-white/80 content-[""] border-b-transparent border-x-transparent border-[5px] border-solid end-[11.5px] top-[50%] !important;
}

.Select2__indicator svg {
  @apply hidden !important;
}

.Select2__control input[type=text] {
  @apply dark:bg-transparent !important;
}

.Select2__control {
  @apply min-h-[36px] !important;
}

.Select2__menu {
  @apply mt-[-1px] my-0 shadow-none !important;
}
.Select2__menu .Select2__option--is-selected {
  @apply text-white !important;
}

#full-calendar-activity {
  @apply overflow-auto;
}

.project-list-main .basic-multi-select {
  @apply w-[150px] !important;
}

.fc-h-event.bg-primarytint1color i {
  @apply text-primarytint1color !important;
}

.fc-h-event.bg-primarytint2color i {
  @apply text-primarytint2color !important;
}

.fc-h-event.bg-primarytint3color i {
  @apply text-primarytint3color !important;
}

.fc-h-event.bg-primary i {
  @apply text-primary !important;
}

.fc-h-event.bg-secondary i {
  @apply text-secondary !important;
}

.fc-h-event.bg-success i {
  @apply text-success !important;
}

.fc-h-event.bg-info i {
  @apply text-info !important;
}

.fc-h-event.bg-warning i {
  @apply text-warning !important;
}

.fc-h-event.bg-danger i {
  @apply text-danger !important;
}

.fc-h-event i {
  @apply text-primary !important;
}

.react-select__control {
  @apply bg-white dark:bg-bodybg2 border-inputborder dark:border-white/10 !important;
}

.react-select__multi-value {
  @apply bg-primary !important;
}

.react-select__multi-value {
  @apply rounded-sm !important;
}

.react-select__multi-value__label {
  @apply text-white !important;
}

.react-select__multi-value__remove svg {
  @apply fill-white !important;
}

.custom-input-pickr .react-datepicker-wrapper {
  @apply grow;
}

.react-select__input-container,
.react-datepicker-time__caption,
.react-datepicker-time__input {
  @apply text-defaulttextcolor !important;
}

.react-select__input {
  @apply dark:bg-transparent dark:text-defaulttextcolor !important;
}

@media (max-width: 480px) {
  #zoom-chart .apexcharts-toolbar,
  #column-rotated-labels .apexcharts-toolbar,
  #treemap-distributed .apexcharts-toolbar {
    @apply hidden !important;
  }
  .ms-container {
    @apply w-full !important;
  }
}
@media (max-width: 576px) {
  #mixed-multiple-y .apexcharts-toolbar {
    @apply hidden !important;
  }
}
.MuiRating-icon.MuiRating-iconEmpty {
  @apply text-textmuted dark:text-textmuted/80 !important;
}

[dir=rtl] .MuiRating-root {
  @apply dir-ltr !important;
}

@media (max-width: 575.98px) {
  .MuiRating-root.MuiRating-sizeLarge {
    @apply text-[1.57rem];
  }
}
.ti-btn-group .ti-btn.ti-btn-lg {
  @apply py-[0.65rem] px-4;
}

[dir=rtl] .PhoneInputCountrySelectArrow {
  @apply mr-[0.35em] ml-[0];
}

[dir=rtl] .PhoneInputCountry {
  @apply mr-[0] ml-[.35em];
}

[dir=rtl] [type=tel] {
  @apply dir-rtl;
}

[dir=rtl] .stdropdown-container {
  @apply text-right;
}

.stdropdown-item span {
  @apply mx-[3px] inline-block;
}

.ms-container input.search-input {
  @apply shadow-none;
}

[dir=rtl] input[type=date],
[dir=rtl] input[type=datetime-local],
[dir=rtl] input[type=month],
[dir=rtl] input[type=time],
[dir=rtl] input[type=week] {
  @apply text-end;
}

[type=checkbox].ti-switch:before {
  @apply content-[""];
}

[dir=rtl] .react-dropdown-select.basic-multi-select {
  @apply dir-rtl !important;
}

.react-dropdown-select .react-dropdown-select-option {
  @apply bg-primary;
}

.react-dropdown-select-input {
  @apply text-[0.8125rem];
}

.react-dropdown-select-clear {
  @apply text-base;
}

.react-dropdown-select .react-dropdown-select-dropdown .react-dropdown-select-item {
  @apply border-defaultborder dark:border-white/10;
}

.react-dropdown-select .react-dropdown-select-dropdown {
  @apply bg-customwhite border border-defaultborder border-solid dark:border-white/10 rounded-md;
}

.react-dropdown-select .react-dropdown-select-dropdown .react-dropdown-select-item.react-dropdown-select-item-selected {
  @apply bg-primary text-white;
}

.react-dropdown-select-item.react-dropdown-select-item-disabled {
  @apply border-b-defaultborder text-defaulttextcolor opacity-[.4] border-b border-solid dark:border-white/10 bg-customwhite !important;
}

.react-dropdown-select {
  @apply bg-customwhite border border-inputborder rounded-[.35rem] dark:border-white/10 !important;
}

.react-dropdown-select .react-dropdown-select-dropdown .react-dropdown-select-item:hover {
  @apply bg-primary/10 text-primary !important;
}

#slider-square .MuiSlider-thumb {
  @apply rounded-[5px] bg-primary;
}

#hs-wrapper-select-for-copy select {
  @apply rtl:pr-[.75rem] rtl:pl-[2.5rem] !important;
}

.sun-editor .se-btn:enabled.active:active {
  @apply dark:bg-white/10 dark:border-white/10 shadow-none !important;
}

.sun-editor .se-list-layer {
  @apply bg-customwhite border-defaultborder dark:border-white/10 !important;
}

.sun-editor {
  @apply border border-defaultborder dark:border-white/10 text-defaulttextcolor border-solid !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-header {
  @apply border-b-defaultborder dark:border-white/10 border-b border-solid !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-content {
  @apply bg-customwhite border border-defaultborder dark:border-white/10 border-solid !important;
}

.sun-editor .se-dialog button,
.sun-editor .se-dialog input,
.sun-editor .se-dialog label {
  @apply text-defaulttextcolor !important;
}

.sun-editor input,
.sun-editor select,
.sun-editor textarea {
  @apply text-defaulttextcolor border border-defaultborder dark:border-white/10 border-solid !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-footer {
  @apply border-t-defaultborder dark:border-white/10 border-t border-solid !important;
}

.sun-editor .se-btn-primary {
  @apply text-white bg-primary border border-primary border-solid !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-btn-revert {
  @apply border border-defaultborder dark:border-white/10 border-solid !important;
}

.sun-editor input:focus,
.sun-editor select:focus,
.sun-editor textarea:focus {
  @apply border border-defaultborder dark:border-white/10 shadow-[0_0_0_0.2rem_transparent] border-solid !important;
}

.sun-editor input:focus-visible,
.sun-editor select:focus-visible,
.sun-editor textarea:focus-visible {
  @apply outline-[0] !important;
}

.sun-editor .se-dialog-tabs button.active {
  @apply bg-primary text-white !important;
}

.sun-editor .se-dialog-tabs button {
  @apply bg-light border-r-defaultborder dark:border-white/10 border-r border-solid !important;
}

.sun-editor .se-dialog-tabs {
  @apply border-b-defaultborder dark:border-white/10 border-b border-solid !important;
}

.se-dialog-body div:first-child {
  @apply border-defaultborder dark:border-white/10 !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-form .se-dialog-form-files .se-dialog-files-edge-button {
  @apply border border-defaultborder dark:border-white/10 border-solid !important;
}

.react-dropdown-select:focus {
  @apply shadow-none !important;
}

.react-datepicker__triangle path {
  @apply stroke-defaultborder dark:stroke-white/10 !important;
}

.se-dialog-content [type=checkbox]:checked,
.se-dialog-content [type=radio]:checked,
.se-dialog-content [type=checkbox],
.se-dialog-content [type=radio] {
  @apply dark:bg-white/10 !important;
}

.sun-editor .se-btn-list:active {
  @apply dark:bg-white/10 dark:border-white/10 shadow-none !important;
}

.sun-editor .se-btn:enabled:hover,
.sun-editor .se-btn-list.default_value,
.sun-editor .se-btn-list:hover,
.sun-editor .se-btn:enabled.on {
  @apply dark:bg-white/10 dark:border-white/10 !important;
}

.se-input-form::file-selector-button {
  @apply py-[5px] px-[7px] ms-[-.5em];
}

.filepond--file-info .filepond--file-info-main {
  @apply text-white;
}

.filepond--file [data-align*=left],
.filepond--file [data-align*=right] {
  @apply top-[2px];
}

.color-picker-input input[type=color i]::-webkit-color-swatch {
  @apply bg-primary border-primary border border-solid rounded-full !important;
}

.pcr-button {
  @apply relative;
}

.pcr-button:after {
  @apply absolute content-[""] w-full h-full rounded-[0.15em] left-0 top-0;
}

.color-picker-input input {
  @apply relative h-8 w-8 cursor-pointer rounded-lg;
}

.input-group .buysell .Select2__control {
  @apply rounded-ss-none rounded-es-none !important;
}

.companies-search-input .Select2__value-container {
  @apply p-[8px] !important;
}

@media screen and (max-width: 622px) {
  .input-group.companies-search-input .Select2__control {
    @apply rounded-[.3rem] mb-[.5rem] !important;
  }
  .companies-search-input.companies-search-input1 .custom-width-form {
    @apply w-full !important;
  }
}
#grid-example1,
#grid-header-fixed,
#grid-hidden-column,
#grid-loading,
#grid-pagination,
#grid-search,
#grid-sorting,
#grid-wide {
  @apply overflow-auto;
}

.rdt_TableHeadRow {
  @apply border-0 border-b !important;
}

.rdt_Pagination {
  @apply border-t-0 !important;
}

.rdt_Pagination select {
  @apply rtl:pl-10 rtl:pr-2 !important;
}

.MuiTable-root .MuiTableCell-root {
  @apply text-start !important;
}

.stdropdown-menu::-webkit-scrollbar-thumb,
.stdropdown-menu::-webkit-scrollbar-track {
  @apply bg-light dark:bg-white/10 !important;
}

body .stdropdown-menu::-webkit-scrollbar {
  @apply bg-light dark:bg-white/20 !important;
}

.stdropdown-menu::-webkit-scrollbar-track {
  @apply bg-[#f1f1f1] !important;
}

.ms-container .ms-list {
  @apply shadow-none !important;
}

::-webkit-datetime-edit {
  @apply justify-end !important;
}

[dir=rtl] .sun-editor .se-btn-list > .se-list-icon {
  @apply mr-0 ml-[10px] mt-[-1px] mb-0 !important;
}

.filepond--file-status .filepond--file-status-main {
  @apply text-white !important;
}

@media (min-width: 623px) {
  .crm-search-custom {
    @apply border-e-[0] !important;
  }
}
.topselling-products-list li {
  @apply mb-[1.05rem];
}
.topselling-products-list li:last-child {
  @apply mb-0;
}

.page-header-breadcrumb .datepicker-input {
  @apply min-w-[12rem] !important;
}

[data-nav-layout=horizontal][data-nav-style=icon-click][data-toggled=icon-click-closed] .slide.has-sub .slide-menu.child1, [data-nav-layout=horizontal][data-nav-style=icon-hover][data-toggled=icon-hover-closed] .slide.has-sub .slide-menu.child1, [data-nav-layout=horizontal][data-nav-style=menu-click][data-toggled=menu-click-closed] .slide.has-sub .slide-menu.child1, [data-nav-layout=horizontal][data-nav-style=menu-hover][data-toggled=menu-hover-closed] .slide.has-sub .slide-menu.child1 {
  @apply start-auto !important;
}

/* End:: Customstyles */
.product-checkout .tab-pane:focus-visible {
  @apply outline-[0] !important;
}

.yarl__slide_image {
  @apply rounded-md !important;
}

.min-w-fit-content .ri-star-fill,
.bi-star-fill,
.ri-star-half-fill,
.bi-star-half {
  @apply m-[2px] !important;
}

.custom-tablelist {
  @apply dark:text-white/80 !important;
}

.mail-navigation .simplebar-content {
  @apply -mb-[15px] !important;
}

.main-mail-container .hs-dropdown.open > .ti-dropdown-menu {
  @apply -translate-y-[115px] translate-x-[230px] !important;
}

.custom-btn a {
  @apply m-[4px] !important;
}

#profile-tabs .react-select__control {
  @apply dark:bg-bodybg !important;
}

@media (max-width: 320px) {
  .custom-terms {
    @apply w-full !important;
  }
}
#input-time {
  @apply dir-rtl !important;
}

.custom-checkbox {
  @apply flex !important;
}

#hs-trailing-multiple-add-on {
  @apply rounded-tr-none rounded-br-none !important;
}

.css-13cymwt-control {
  @apply dark:bg-bodybg dark:border-defaultborder/10 !important;
}

.MuiStepConnector-line.MuiStepConnector-lineHorizontal {
  @apply border-defaultborder/10 !important;
}

.custom-file-upload {
  @apply h-[148px] w-full border-[2px] border-dashed !important;
}

.custom-task .react-datepicker-wrapper {
  @apply flex-grow !important;
}

.ti-custom-table.thead th {
  @apply border-b border-defaultborder dark:border-defaultborder/10 !important;
}

::-webkit-datetime-edit {
  @apply dir-rtl !important;
}

.custom-progress {
  @apply rounded-[10px] !important;
}

@media (min-width: 992px) {
  [data-nav-style=menu-hover][data-nav-layout=horizontal][data-toggled=menu-hover-closed] .app-sidebar .slide.has-sub .slide-menu {
    @apply dark:bg-bodybg !important;
  }
  [data-nav-style=icon-click][data-nav-layout=horizontal][data-toggled=icon-click-closed] .app-sidebar .slide.has-sub .slide-menu {
    @apply dark:bg-bodybg !important;
  }
  [data-nav-style=icon-hover][data-nav-layout=horizontal][data-toggled=icon-hover-closed] .app-sidebar .slide.has-sub .slide-menu {
    @apply dark:bg-bodybg !important;
  }
}
#react-select-9-input {
  @apply hidden !important;
}

@media (min-width: 992px) {
  [data-nav-layout=horizontal] .app-sidebar .slide.has-sub .slide-menu.child1 {
    @apply start-auto !important;
  }
}
[data-header-position=scrollable][data-nav-layout=vertical] .app-header {
  @apply absolute !important;
}

@media (max-width: 474px) {
  #marker-image-map .pigeon-click-block img {
    @apply hidden !important;
  }
}
@media (max-width: 1420px) {
  .ms-container {
    @apply w-full !important;
  }
}
@media (max-width: 740px) {
  .MuiStepConnector-line.MuiStepConnector-lineHorizontal {
    @apply hidden !important;
  }
}
@media (max-width: 320px) {
  .wizard-content {
    @apply p-4 !important;
  }
  .swiper.vertical {
    @apply h-[10.875rem];
  }
  .custom-terms {
    @apply max-w-full !important;
  }
  .aspect-video {
    @apply w-[91%] !important;
  }
  .create-nft-item .filepond--root {
    @apply w-[128px] h-[128px] !important;
  }
}
.custom-select {
  @apply inline-flex items-center !important;
}

@media (max-width: 580px) {
  .ti-modal .custom-contact {
    @apply w-[32rem] !important;
  }
}
.create-nft-item .filepond--root {
  @apply mx-auto my-0 !important;
}

.custom-filepond {
  @apply h-[9.5rem] !important;
}

.custom-filepond .filepond--drop-label {
  @apply h-[150px] !important;
}

@media (max-width: 398px) {
  .swiper-vertical.swiper-related-profiles {
    @apply h-[22rem] !important;
  }
}
.custom-table1 thead tr {
  @apply border-b border-defaultborder dark:border-defaultborder/10;
}

.custom-table1 thead tr {
  @apply dark:border-b-defaultborder/10 !important;
}

.custom-inputgroup1 {
  @apply rounded-tr-none rounded-br-none rounded-s-none !important;
}

.react-datepicker__input-container .custom-flatpickr {
  @apply dark:bg-bodybg !important;
}

@media (max-width: 560px) {
  .custom-avatar {
    @apply mt-[5px] !important;
  }
}
.custom-mail .hs-dropdown.open > .ti-dropdown-menu {
  @apply -translate-y-[117px] translate-x-[291px] !important;
}

[data-menu-styles=light] .app-sidebar .main-sidebar-header {
  @apply border-b-defaultborder !important;
}

@media (max-width: 768px) {
  .custom-form .MuiStepConnector-line {
    @apply border-0 !important;
  }
}
.custom-stocks {
  @apply inline-block !important;
}

.custom-products .Select2__control {
  @apply shadow-none !important;
}

.custom-select-1 .react-select__dropdown-indicator {
  @apply hidden !important;
}

.companies-search-input .Select2__control {
  @apply dark:bg-bodybg !important;
}

[dir=rtl] .custom-btn {
  @apply rounded-tl-[5px] rounded-bl-[5px] !important;
}

@media (max-width: 320px) {
  .companies-search-input .custom-select {
    @apply w-full !important;
  }
}
.currency-exchange-area .Select2__control {
  @apply dark:bg-bodybg rounded-[6px] !important;
}

@media (max-width: 365px) {
  #mixed-linecolumn .apexcharts-toolbar {
    @apply hidden;
  }
}
@media (max-width: 320px) {
  #candlestick-basic .apexcharts-toolbar,
  #candlestick-line .apexcharts-toolbar {
    @apply hidden;
  }
}
@media (max-width: 460px) {
  #heatmap-multiseries .apexcharts-toolbar {
    @apply mt-[19px] !important;
  }
}
.pigeon-click-block {
  @apply translate-y-[321.75px] translate-x-[63px];
}

.custom-navbar .dark\:text-white {
  @apply dark:text-defaulttextcolor/70 !important;
}

.custom-badge {
  @apply flex !important;
}

.custom-buttons .ti-btn-dark {
  @apply dark:bg-[#f0f5f8] dark:text-[#181717] !important;
}

.custom-buttons .ti-btn-link {
  @apply text-primary bg-transparent underline !important;
}

[dir=rtl] .custom-mail .hs-dropdown.open > .ti-dropdown-menu {
  @apply translate-y-[117px] translate-x-[291px] !important;
}
[dir=rtl] .custom-pricing .ti-circle-arrow-right-filled {
  @apply rotate-180 !important;
}

.custom-review .box-body {
  @apply dark:border-e-defaultborder/10 dark:border-s-defaultborder/10 !important;
}

.custom-review .box-body {
  @apply border-e-defaultborder border-s-defaultborder !important;
}

.timeline .timeline-content {
  @apply after:-start-[2.93rem];
}

@media (max-width: 1199px) {
  .landing-main-image:before {
    @apply translate-y-[-138px] translate-x-2.5;
  }
}
.custom-search {
  @apply mb-[5px] !important;
}

.custom-search .search-result-item:hover {
  color: #5c67f7 !important;
}

[data-nav-style=icon-click][data-nav-layout=horizontal] .app-sidebar .main-menu-container .slide-left {
  @apply hidden !important;
}

[data-nav-style=icon-click][data-nav-layout=horizontal] .app-sidebar .main-menu-container .slide-right {
  display: none !important;
  @apply hidden !important;
}

[data-nav-style=icon-hover][data-nav-layout=horizontal] .app-sidebar .main-menu-container .slide-left {
  @apply hidden !important;
}

[data-nav-style=icon-hover][data-nav-layout=horizontal] .app-sidebar .main-menu-container .slide-right {
  @apply hidden !important;
}

[data-menu-styles=color][data-nav-layout=horizontal][data-bg-img=bgimg1] ul.slide-menu, [data-menu-styles=color][data-nav-layout=horizontal][data-bg-img=bgimg2] ul.slide-menu, [data-menu-styles=color][data-nav-layout=horizontal][data-bg-img=bgimg3] ul.slide-menu, [data-menu-styles=color][data-nav-layout=horizontal][data-bg-img=bgimg4] ul.slide-menu, [data-menu-styles=color][data-nav-layout=horizontal][data-bg-img=bgimg5] ul.slide-menu {
  @apply before:bg-transparent !important;
}

@media (max-width: 762px) {
  .header-link .bi-search::before {
    @apply -translate-y-[2px] translate-x-0 !important;
  }
}
#ms-pre-selected-options .ms-selectionpanel {
  @apply translate-y-[15px] translate-x-[10px] sm:translate-x-[20px] !important;
}

.custom-color .rcp-root {
  @apply w-[225px] !important;
}

.custom-color .rcp-saturation {
  @apply h-[100px] !important;
}

.custom-picker .react-datepicker-wrapper {
  @apply flex-grow;
}

[data-vertical-style=doublemenu].dark[data-menu-styles=light] .app-sidebar .main-sidebar-header .header-logo .toggle-white {
  @apply hidden !important;
}

[data-nav-style=icon-click].dark[data-menu-styles=light] .app-sidebar .main-sidebar-header .header-logo .toggle-white {
  @apply hidden !important;
}

[data-nav-style=icon-hover].dark[data-menu-styles=light] .app-sidebar .main-sidebar-header .header-logo .toggle-white {
  @apply hidden !important;
}

.landing-body {
  @apply dark:bg-bodybg;
}

.custom-landing .ri-twitter-x-line {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 !important;
}

.custom-products .Select2__control {
  @apply flex-grow !important;
}

.landing-banner .section {
  @apply pt-[8.375rem];
}

@media (max-width: 1000px) {
  .landing-banner .section {
    @apply pt-[4.375rem] !important;
  }
}
.custom-products .basic-multi-select {
  @apply w-[15%];
}

@media (max-width: 616px) {
  .custom-products .basic-multi-select {
    @apply w-full;
  }
}
#revenue-report {
  @apply pt-[5.1rem];
}

[dir=rtl] .echart-charts span {
  @apply me-2 ms-auto !important;
}

@media (max-width: 991.98px) {
  [data-toggled=open] #responsive-overlay {
    @apply visible !important;
    --tw-bg-opacity: 0.5;
  }
}
@media (min-width: 992px) {
  [data-nav-layout=horizontal] .landing-body .app-sidebar .side-menu__item.active .side-menu__label {
    @apply text-primary;
  }
}
#search-modal .box-body .ti-list-group {
  @apply p-3 rounded-none border-b-0;
}
#search-modal .box-body .ti-list-group:first-child {
  @apply p-3 rounded-tl-sm rounded-tr-sm border-b-0 !important;
}
#search-modal .box-body .ti-list-group:last-child {
  @apply border-b !important;
}

.\!text-tealmain {
  color: rgb(var(--teal)) !important;
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.light[data-menu-styles=dark] .app-sidebar,
.light[data-menu-styles=dark] .main-sidebar-header {
  --tw-bg-opacity: 1;
  background-color: rgb(32, 41, 71, var(--tw-bg-opacity, 1));
}

.currency-exchange-area .Select2__control,
.currency-exchange-area .form-control,
.currency-exchange-area .select2-container--default .select2-selection--single {
  @apply h-[40px];
}

@media (max-width: 576px) {
  #zoom-chart .apexcharts-canvas .apexcharts-toolbar,
  #annotation-chart .apexcharts-canvas .apexcharts-toolbar,
  #stepline-chart .apexcharts-canvas .apexcharts-toolbar,
  #area-negative .apexcharts-canvas .apexcharts-toolbar,
  #area-spline .apexcharts-canvas .apexcharts-toolbar,
  #boxplot-basic .apexcharts-canvas .apexcharts-toolbar,
  #boxplot-scatter .apexcharts-canvas .apexcharts-toolbar,
  #area-datetime .apexcharts-canvas .apexcharts-toolbar,
  #area-stacked .apexcharts-canvas .apexcharts-toolbar {
    @apply hidden;
  }
}
.custom-file-upload {
  @apply border-inputborder !important;
}

.sun-editor .se-btn-select.se-btn-tool-font {
  @apply gap-5;
}

.sun-editor .se-btn-select.se-btn-tool-size {
  @apply gap-5;
}

[dir=rtl] .sun-editor .se-dialog .se-dialog-inner .se-dialog-form .se-input-form.se-input-url {
  @apply dir-rtl;
}

.sun-editor .se-dialog button,
.sun-editor .se-dialog input,
.sun-editor .se-dialog label {
  @apply text-black/70 dark:text-white/70 !important;
}

.sun-editor .se-dialog .se-dialog-inner .se-dialog-content .se-btn-primary {
  @apply text-white !important;
}

.sun-editor .se-btn-module-border {
  @apply border-inputborder dark:border-defaultborder/10 !important;
}

.sun-editor .se-toolbar {
  @apply outline-0 border-b border-inputborder dark:border-defaultborder/10 !important;
}

.crypto-input .meter-select {
  @apply w-[70px] !important;
}

.buy-crypto .Select2__value-container--has-value {
  @apply me-[0.4rem];
}

.add-products .Select2__value-container {
  @apply me-3;
}

[data-header-styles=light] .app-header .header-search-bar::placeholder {
  @apply dark:text-black/80 !important;
}

.dark[data-header-styles=light][data-vertical-style=detached][data-toggled=detached-close][data-nav-layout=vertical] .app-header .animated-arrow span {
  @apply bg-transparent !important;
}

.pcr-button.ti-btn-undefined {
  @apply dark:border-white/30 !important;
}

[data-nav-layout=vertical] .main-menu > .slide.active .slide-menu .side-menu__item:hover .side-menu__angle,
[data-nav-layout=vertical] .main-menu > .slide:hover .slide-menu .side-menu__item:hover .side-menu__angle {
  @apply text-primary !important;
}

[dir=rtl] .shepherd-enabled.shepherd-element {
  @apply right-auto;
}

.react-dropdown-select {
  @apply focus-within:shadow-none !important;
}

.ms-selectionpanel {
  @apply flex justify-center items-center -m-1;
}

@media (max-width: 420px) {
  .ms-selectionpanel {
    @apply block !important;
  }
}
#ms-pre-selected-options.ms-container .ms-selectable li.selected, #ms-pre-selected-options.ms-container .ms-selection li.selected {
  @apply bg-primary !important;
}

.MuiStepConnector-root.MuiStepConnector-horizontal {
  @apply hidden lg:block;
}

.fc-v-event .fc-event-main {
  @apply whitespace-break-spaces;
}

.fc-timegrid-event-harness > .fc-timegrid-event {
  @apply inset-[-2px] !important;
}

/* Dark Theme Table Status Colors */
:root,
html,
body {
  /* Status Badge Colors */
  --status-success-bg: rgba(65, 136, 118, 0.2); /* #41887633 */
  --status-success-text: #21CE9E;
  --status-failed-bg: #5B2424;
  --status-failed-text: #FB3D32;
  --action-type-bg: rgba(255, 165, 0, 0.2); /* #FFA50033 */
  --action-type-text: #FFA500;
}

/* Table Body Text Styling - Rubik Font with Dark Theme */
.table tbody {
  @apply text-gray-400 font-rubik font-normal text-base leading-none tracking-normal !important;
}

/* Additional table body styling for consistency */
.table tbody td {
  @apply font-rubik font-normal text-base leading-none tracking-normal !important;
}

/* Table Header Styling Override */
.table thead th {
  @apply font-rubik font-semibold text-base text-white leading-none !important;
}

@font-face {
  font-family: "Ionicons";
  src: url(../public/assets/icon-fonts/react-listbox/fonts/ionicons.woff?v=2.0.0) format("woff");
  font-weight: normal;
  font-style: normal;
}
.ion, .ionicons, .ion-arrow-down-b:before, .ion-arrow-left-a:before, .ion-arrow-right-a:before, .ion-arrow-up-b:before, .ion-ios-skipbackward:before, .ion-ios-skipforward:before {
  display: inline-block;
  font-family: "Ionicons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ion-arrow-down-b:before {
  content: "\f104";
}

.ion-arrow-left-a:before {
  content: "\f106";
}

.ion-arrow-right-a:before {
  content: "\f109";
}

.ion-arrow-up-b:before {
  content: "\f10d";
}

.ion-ios-skipbackward:before {
  content: "\f4ab";
}

.ion-ios-skipforward:before {
  content: "\f4ad";
}

.ms-container {
  @apply w-[570px];
}

.ms-container {
  @apply after:block after:h-0 after:leading-[0] after:text-[0] after:clear-both after:min-h-0 after:invisible;
}

.ms-container .ms-selectable, .ms-container .ms-selection {
  @apply bg-white dark:bg-bodybg text-[#555555] float-left w-[43%];
}

.ms-selectionpanel, .ms-selectionpanel2 {
  @apply bg-white dark:bg-bodybg text-[#555555] float-left w-[7%];
}

.ms-container .ms-selection {
  @apply float-right;
}

.ms-container .ms-selectionpanel2 {
  @apply float-right !important;
}

.ms-container .ms-selectionpanel2 span,
.ms-container .ms-selectionpanel span {
  @apply text-xs;
}

.ms-container .ms-list {
  @apply transition-[border_linear_0.2s,box-shadow_linear_0.2s] border border-defaultborder dark:border-defaultborder/10 rounded-[3px] h-[340px] overflow-y-auto p-0 relative;
}

.ms-container .ms-list.ms-focus {
  @apply border-primary outline-none;
}

.ms-container ul {
  @apply list-none m-0 p-0;
}

.ms-container .ms-optgroup-container {
  @apply w-full;
}

.ms-container .ms-optgroup-label {
  @apply cursor-pointer text-[#999] m-0 ps-[5px] pe-0 pt-[5px] pb-0;
}

.ms-container .ms-selectable li.ms-elem-selectable,
.ms-container .ms-selection li.ms-elem-selection {
  @apply text-[#555] text-sm cursor-pointer p-3 border-b border-defaultborder dark:border-defaultborder/10;
}

.ms-container .ms-selectable li.selected,
.ms-container .ms-selection li.selected {
  @apply text-white bg-[#6969c4] border-b border-defaultborder dark:border-defaultborder/10;
}

.ms-container .ms-selectable li:hover,
.ms-container .ms-selection li:hover {
  @apply cursor-pointer text-white bg-[#8e8ed3];
}

.ms-container .ms-selectable li.disabled,
.ms-container .ms-selection li.disabled {
  @apply bg-[#eee] text-[#aaa] cursor-text;
}

i.icon {
  @apply text-sm;
}

input.search-input {
  @apply box-border w-full rounded-sm h-[30px] bg-white border mb-[5px] border border-defaultborder dark:border-defaultborder/10;
}

input[type=text] {
  @apply inline-block leading-5 text-sm text-[#555] align-middle ml-0 px-1.5 py-1;
}

/* UTILITIES */
/* Start::avatars */
.avatar {
  @apply relative h-[2.625rem] w-[2.625rem] inline-flex items-center justify-center rounded-[0.25rem] text-white mb-0 font-medium;
}
.avatar a.badge:hover {
  @apply text-white;
}
.avatar img {
  @apply w-full h-full rounded-md;
}
.avatar svg {
  @apply w-6 h-6;
}
.avatar.avatar-rounded {
  @apply rounded-[50%];
}
.avatar.avatar-rounded img {
  @apply rounded-[50%];
}
.avatar.avatar-radius-0 {
  @apply rounded-none;
}
.avatar.avatar-radius-0 img {
  @apply rounded-none;
}
.avatar .avatar-badge {
  @apply absolute top-[-4%] end-[-0.375rem] w-[1.4rem] h-[1.4rem] text-[0.625rem] border-customwhite flex items-center justify-center rounded-[50%] border-2 border-solid;
}
.avatar.online:before, .avatar.offline:before {
  @apply absolute content-[""] w-2 h-2 border-customwhite rounded-[50%] border-2 border-solid end-0 bottom-0;
}
.avatar.online:before {
  @apply bg-success;
}
.avatar.offline:before {
  @apply bg-gray-500;
}
.avatar.avatar-xs {
  @apply w-5 h-5 leading-5 text-[0.65rem];
}
.avatar.avatar-xs .avatar-badge {
  @apply w-4 h-4 leading-4 text-[0.5rem] end-[-0.5rem] p-1 -top-1/4;
}
.avatar.avatar-sm {
  @apply w-7 h-7 leading-7 text-xs;
}
.avatar.avatar-sm .avatar-badge {
  @apply w-[1.1rem] h-[1.1rem] leading-[1.1rem] text-[0.5rem] top-[-38%] end-[-0.5rem] p-[0.3rem];
}
.avatar.avatar-sm.online:before, .avatar.avatar-sm.offline:before {
  @apply w-2 h-2;
}
.avatar.avatar-sm svg {
  @apply w-4 h-4;
}
.avatar.avatar-md {
  @apply w-10 h-10 leading-10 text-[0.8rem];
}
.avatar.avatar-md .avatar-badge {
  @apply w-[1.2rem] h-[1.2rem] leading-[1.2rem] text-[0.65rem] top-[-6%] end-[-13%] p-[0.4rem];
}
.avatar.avatar-md.online:before, .avatar.avatar-md.offline:before {
  @apply w-3 h-3;
}
.avatar.avatar-md svg {
  @apply w-5 h-5;
}
.avatar.avatar-lg {
  @apply w-12 h-12 leading-[3rem] text-base;
}
.avatar.avatar-lg .avatar-badge {
  @apply top-[-15%] end-[-0.25%];
}
.avatar.avatar-lg.online:before, .avatar.avatar-lg.offline:before {
  @apply w-[0.8rem] h-[0.8rem];
}
.avatar.avatar-lg svg {
  @apply w-6 h-6;
}
.avatar.avatar-xl {
  @apply w-16 h-16 leading-[4rem] text-xl;
}
.avatar.avatar-xl .avatar-badge {
  @apply top-[-8%] end-[-0.2%];
}
.avatar.avatar-xl.online:before, .avatar.avatar-xl.offline:before {
  @apply w-[0.95rem] h-[0.95rem];
}
.avatar.avatar-xxl {
  @apply w-20 h-20 leading-[5rem] text-2xl;
}
.avatar.avatar-xxl .avatar-badge {
  @apply top-[-4%] end-0;
}
.avatar.avatar-xxl.online:before, .avatar.avatar-xxl.offline:before {
  @apply w-[1.05rem] h-[1.05rem] bottom-1;
}

.avatar-list-stacked {
  @apply p-0;
}
.avatar-list-stacked .avatar {
  @apply me-[-0.5rem] align-middle transition-transform duration-[ease] delay-200 border-2 border-solid border-transparent;
}
.avatar-list-stacked .avatar:last-child {
  @apply me-0 !important;
}
.avatar-list-stacked .avatar:hover {
  @apply z-[1] border-customwhite border-2 border-solid scale-[1.15];
}

[dir=rtl] .avatar-list-stacked .ri-arrow-right-s-line {
  @apply rotate-180;
}

/* End::avatars */
/* Start:: background */
/*
 * LEGACY BACKGROUND UTILITIES
 *
 * For new components, use semantic background classes from Tailwind config:
 * - bg-background (#0F0F0F) - Main body background
 * - bg-nav (#1D1D1D) - Navigation components
 * - bg-section (#272729) - Filter headings background
 * - bg-filter (#1D1D1F) - Filter background
 * - bg-table-section (#1D1D1F) - Table section background
 * - bg-elevated (#272729) - Table background, elevated above table-section
 * - bg-table-total (#494C72) - Table total/pagination background
 * - bg-table-head (#313452) - Table header (thead) background
 */
.color-container {
  @apply w-12 h-12 shadow-[0px_0.125rem_0.25rem_rgba(0,0,0,0.05)] flex items-center justify-center leading-[3rem] rounded-lg;
}

.text-container {
  @apply rounded-md shadow-defaultshadow px-2 py-1;
}

/* Start::background color */
.bg-body {
  @apply bg-bodybg !important;
}

/* End::background color */
/* Start::background color */
.bg-primary1 {
  @apply bg-primary/10 !important;
}
.bg-primary1.bg-opacity-10 {
  @apply bg-primarytint1color/10 !important;
}
.bg-primary1.bg-opacity-25 {
  @apply bg-primarytint1color/25 !important;
}
.bg-primary1.bg-opacity-50 {
  @apply bg-primarytint1color/50 !important;
}
.bg-primary1.bg-opacity-75 {
  @apply bg-primarytint1color/75 !important;
}
.bg-primary1.bg-opacity-100 {
  @apply bg-primarytint1color/100 !important;
}

.bg-primary2 {
  @apply bg-primary/20 !important;
}
.bg-primary2.bg-opacity-10 {
  @apply bg-primarytint2color/10 !important;
}
.bg-primary2.bg-opacity-25 {
  @apply bg-primarytint2color/25 !important;
}
.bg-primary2.bg-opacity-50 {
  @apply bg-primarytint2color/50 !important;
}
.bg-primary2.bg-opacity-75 {
  @apply bg-primarytint2color/75 !important;
}
.bg-primary2.bg-opacity-100 {
  @apply bg-primarytint2color/100 !important;
}

.bg-primary3 {
  @apply bg-primary/30 !important;
}
.bg-primary3.bg-opacity-10 {
  @apply bg-primarytint3color/10 !important;
}
.bg-primary3.bg-opacity-25 {
  @apply bg-primarytint3color/25 !important;
}
.bg-primary3.bg-opacity-50 {
  @apply bg-primarytint3color/50 !important;
}
.bg-primary3.bg-opacity-75 {
  @apply bg-primarytint3color/75 !important;
}
.bg-primary3.bg-opacity-100 {
  @apply bg-primarytint3color/100 !important;
}

/* End::background color */
/* Start::gradient colors */
.bg-primary-gradient {
  @apply bg-primarygradient text-white !important;
}

.bg-primary1-gradient {
  @apply bg-primary1gradient text-white !important;
}

.bg-primary2-gradient {
  @apply bg-primary2gradient text-white !important;
}

.bg-primary3-gradient {
  @apply bg-primary3gradient text-white !important;
}

.bg-secondary-gradient {
  @apply bg-secondarygradient text-white !important;
}

.bg-warning-gradient {
  @apply bg-warninggradient text-white !important;
}

.bg-info-gradient {
  @apply bg-infogradient text-white !important;
}

.bg-success-gradient {
  @apply bg-successgradient text-white !important;
}

.bg-danger-gradient {
  @apply bg-dangergradient text-white !important;
}

.bg-orange-gradient {
  @apply bg-orangegradient text-white !important;
}

.bg-purple-gradient {
  @apply bg-purplegradient text-white !important;
}

.bg-teal-gradient {
  @apply bg-orangegradient text-white !important;
}

.bg-light-gradient {
  @apply bg-lightgradient text-white !important;
}

.bg-dark-gradient {
  @apply bg-darkgradient text-white !important;
}

/* End::gradient colors */
/* Start:: outline colors */
.bg-outline-primary {
  @apply bg-white dark:bg-bodybg border text-primary border-solid border-primary;
}

.bg-outline-primary1 {
  @apply bg-white dark:bg-bodybg border border-primarytint1color text-primarytint1color border-solid;
}

.bg-outline-primary2 {
  @apply bg-white dark:bg-bodybg border border-primarytint2color text-primarytint2color border-solid;
}

.bg-outline-primary3 {
  @apply bg-white dark:bg-bodybg border border-primarytint3color text-primarytint3color border-solid;
}

.bg-outline-secondary {
  @apply bg-white dark:bg-bodybg border text-secondary border-solid border-secondary;
}

.bg-outline-warning {
  @apply bg-white dark:bg-bodybg border text-warning border-solid border-warning;
}

.bg-outline-info {
  @apply bg-white dark:bg-bodybg border text-info border-solid border-info;
}

.bg-outline-success {
  @apply bg-white dark:bg-bodybg border text-success border-solid border-success;
}

.bg-outline-danger {
  @apply bg-white dark:bg-bodybg border text-danger border-solid border-danger;
}

.bg-outline-dark {
  @apply bg-white dark:bg-bodybg border text-dark border-solid border-dark;
}

.bg-outline-light {
  @apply bg-white dark:bg-bodybg border text-black border-solid border-light;
}

/* End:: outline colors */
/* Start::background transparent */
.bg-primary-transparent {
  @apply bg-primary/10 text-primary !important;
}
.bg-primary-transparent:hover {
  @apply bg-primary/10 text-primary !important;
}

.bg-primary1-transparent {
  @apply bg-primarytint1color/10 text-primary/10 !important;
}
.bg-primary1-transparent:hover {
  @apply bg-primarytint1color/10 text-primary/10 !important;
}

.bg-primary2-transparent {
  @apply bg-primarytint2color/10 text-primary/20 !important;
}
.bg-primary2-transparent:hover {
  @apply bg-primarytint2color/10 text-primary/20 !important;
}

.bg-primary3-transparent {
  @apply bg-primarytint3color/10 text-primary/30 !important;
}
.bg-primary3-transparent:hover {
  @apply bg-primarytint3color/10 text-primary/30 !important;
}

.bg-secondary-transparent {
  @apply bg-secondary/10 text-secondary !important;
}
.bg-secondary-transparent:hover {
  @apply bg-secondary/10 text-secondary !important;
}

.bg-info-transparent {
  @apply bg-info/10 text-info !important;
}
.bg-info-transparent:hover {
  @apply bg-info/10 text-info !important;
}

.bg-success-transparent {
  @apply bg-success/10 text-success !important;
}
.bg-success-transparent:hover {
  @apply bg-success/10 text-success !important;
}

.bg-warning-transparent {
  @apply bg-warning/10 text-warning !important;
}
.bg-warning-transparent:hover {
  @apply bg-warning/10 text-warning !important;
}

.bg-danger-transparent {
  @apply bg-danger/10 text-danger !important;
}
.bg-danger-transparent:hover {
  @apply bg-danger/10 text-danger !important;
}

.bg-light-transparent {
  @apply bg-light/10 text-light !important;
}
.bg-light-transparent:hover {
  @apply bg-light/10 text-light !important;
}

.bg-dark-transparent {
  @apply bg-dark/10 text-dark !important;
}
.bg-dark-transparent:hover {
  @apply bg-dark/10 text-dark !important;
}

.bg-pink-transparent {
  @apply bg-pinkmain/10 text-pinkmain !important;
}
.bg-pink-transparent:hover {
  @apply bg-pinkmain/10 text-pinkmain !important;
}

.bg-orange-transparent {
  @apply bg-orangemain/10 text-orangemain !important;
}
.bg-orange-transparent:hover {
  @apply bg-orangemain/10 text-orangemain !important;
}

.bg-purple-transparent {
  @apply bg-purplemain/10 text-purplemain !important;
}
.bg-purple-transparent:hover {
  @apply bg-purplemain/10 text-purplemain !important;
}

.bg-teal-transparent {
  @apply bg-tealmain/10 text-tealmain !important;
}
.bg-teal-transparent:hover {
  @apply bg-tealmain/10 text-tealmain !important;
}

.bg-green-transparent {
  @apply bg-greenmain/10 !important;
}
.bg-green-transparent:hover {
  @apply bg-greenmain/10 !important;
}

.bg-indigo-transparent {
  @apply bg-indigomain/10 text-indigomain !important;
}
.bg-indigo-transparent:hover {
  @apply bg-indigomain/10 text-indigomain !important;
}

.bg-yellow-transparent {
  @apply bg-yellowmain/10 text-yellowmain !important;
}
.bg-yellow-transparent:hover {
  @apply bg-yellowmain/10 text-yellowmain !important;
}

.bg-blue-transparent {
  @apply bg-bluemain/10 text-bluemain !important;
}
.bg-blue-transparent:hover {
  @apply bg-bluemain/10 text-bluemain !important;
}

.bg-white-transparent {
  @apply bg-white/10 text-white !important;
}
.bg-white-transparent:hover {
  @apply bg-white/10 text-white !important;
}

.bg-black-transparent {
  @apply bg-black/10 text-black !important;
}
.bg-black-transparent:hover {
  @apply bg-black/10 text-black !important;
}

/* End::background transparent */
/* Start::backgrounds with colors */
.text-bg-primary {
  @apply bg-primary text-white !important;
}

.text-bg-primary1 {
  @apply bg-primary/10 text-white !important;
}

.text-bg-primary2 {
  @apply bg-primary/20 text-white !important;
}

.text-bg-primary3 {
  @apply bg-primary/30 text-white !important;
}

.text-bg-secondary {
  @apply bg-secondary text-white !important;
}

.text-bg-warning {
  @apply bg-warning text-white !important;
}

.text-bg-info {
  @apply bg-info text-white !important;
}

.text-bg-success {
  @apply bg-success text-white !important;
}

.text-bg-danger {
  @apply bg-danger text-white !important;
}

.text-bg-light {
  @apply bg-light text-white !important;
}

.text-bg-dark {
  @apply bg-dark text-white !important;
}

/* End::backgrounds with colors */
/* Start::svg colors */
.svg-primary svg {
  @apply fill-primary;
}

.svg-primarytint1color svg {
  @apply fill-primarytint1color;
}

.svg-primarytint2color svg {
  @apply fill-primarytint2color;
}

.svg-primarytint3color svg {
  @apply fill-primarytint3color;
}

.svg-secondary svg {
  @apply fill-secondary;
}

.svg-success svg {
  @apply fill-success;
}

.svg-danger svg {
  @apply fill-danger;
}

.svg-warning svg {
  @apply fill-warning;
}

.svg-white svg {
  @apply fill-white;
}

.svg-black svg {
  @apply fill-black;
}

.svg-pink svg {
  @apply fill-pinkmain;
}

.svg-orange svg {
  @apply fill-orangemain;
}

.svg-purple svg {
  @apply fill-purplemain;
}

.svg-indigo svg {
  @apply fill-indigomain;
}

.svg-info svg {
  @apply fill-info;
}

.svg-yellow svg {
  @apply fill-yellowmain;
}

.svg-light svg {
  @apply fill-light;
}

.svg-dark svg {
  @apply fill-dark;
}

.svg-teal svg {
  @apply fill-tealmain;
}

.svg-default svg {
  @apply fill-defaulttextcolor;
}

/* End::svg colors */
/* Start::Colors */
/* Start::blue set */
.bg-blue-100 {
  @apply bg-bluemain/10 text-defaulttextcolor;
}

.bg-blue-200 {
  @apply bg-bluemain/20 text-defaulttextcolor;
}

.bg-blue-300 {
  @apply bg-bluemain/30 text-defaulttextcolor;
}

.bg-blue-400 {
  @apply bg-bluemain/40 text-defaulttextcolor;
}

.bg-blue-500 {
  @apply bg-bluemain/50 text-defaulttextcolor;
}

.bg-blue-600 {
  @apply bg-bluemain/60 text-defaulttextcolor;
}

.bg-blue-700 {
  @apply bg-bluemain/70 text-defaulttextcolor;
}

.bg-blue-800 {
  @apply bg-bluemain/80 text-defaulttextcolor;
}

.bg-blue-900 {
  @apply bg-bluemain/90 text-defaulttextcolor;
}

.bg-blue {
  @apply bg-bluemain text-defaulttextcolor;
}

/* Start::blue set */
/* Start::indigo set */
.bg-indigo-100 {
  @apply bg-indigomain/10 text-defaulttextcolor;
}

.bg-indigo-200 {
  @apply bg-indigomain/20 text-defaulttextcolor;
}

.bg-indigo-300 {
  @apply bg-indigomain/30 text-defaulttextcolor;
}

.bg-indigo-400 {
  @apply bg-indigomain/40 text-defaulttextcolor;
}

.bg-indigo-500 {
  @apply bg-indigomain/50 text-defaulttextcolor;
}

.bg-indigo-600 {
  @apply bg-indigomain/60 text-defaulttextcolor;
}

.bg-indigo-700 {
  @apply bg-indigomain/70 text-defaulttextcolor;
}

.bg-indigo-800 {
  @apply bg-indigomain/80 text-defaulttextcolor;
}

.bg-indigo-900 {
  @apply bg-indigomain/90 text-defaulttextcolor;
}

.bg-indigo {
  @apply bg-indigomain text-defaulttextcolor;
}

/* Start::indigo set */
/* Start::purple set */
.bg-purple-100 {
  @apply bg-purplemain/10 text-defaulttextcolor;
}

.bg-purple-200 {
  @apply bg-purplemain/20 text-defaulttextcolor;
}

.bg-purple-300 {
  @apply bg-purplemain/30 text-defaulttextcolor;
}

.bg-purple-400 {
  @apply bg-purplemain/40 text-defaulttextcolor;
}

.bg-purple-500 {
  @apply bg-purplemain/50 text-defaulttextcolor;
}

.bg-purple-600 {
  @apply bg-purplemain/60 text-defaulttextcolor;
}

.bg-purple-700 {
  @apply bg-purplemain/70 text-defaulttextcolor;
}

.bg-purple-800 {
  @apply bg-purplemain/80 text-defaulttextcolor;
}

.bg-purple-900 {
  @apply bg-purplemain/90 text-defaulttextcolor;
}

.bg-purple {
  @apply bg-purplemain text-defaulttextcolor;
}

/* Start::purple set */
/* Start::pink set */
.bg-pink-100 {
  @apply bg-pinkmain/10 text-defaulttextcolor;
}

.bg-pink-200 {
  @apply bg-pinkmain/20 text-defaulttextcolor;
}

.bg-pink-300 {
  @apply bg-pinkmain/30 text-defaulttextcolor;
}

.bg-pink-400 {
  @apply bg-pinkmain/40 text-defaulttextcolor;
}

.bg-pink-500 {
  @apply bg-pinkmain/50 text-defaulttextcolor;
}

.bg-pink-600 {
  @apply bg-pinkmain/60 text-white;
}

.bg-pink-700 {
  @apply bg-pinkmain/70 text-white;
}

.bg-pink-800 {
  @apply bg-pinkmain/80 text-white;
}

.bg-pink-900 {
  @apply bg-pinkmain/90 text-white;
}

.bg-pink {
  @apply bg-pinkmain text-white;
}

/* Start::pink set */
/* Start::red set */
.bg-red-100 {
  @apply bg-redmain/10 text-defaulttextcolor;
}

.bg-red-200 {
  @apply bg-redmain/20 text-defaulttextcolor;
}

.bg-red-300 {
  @apply bg-redmain/30 text-defaulttextcolor;
}

.bg-red-400 {
  @apply bg-redmain/40 text-defaulttextcolor;
}

.bg-red-500 {
  @apply bg-redmain/50 text-defaulttextcolor;
}

.bg-red-600 {
  @apply bg-redmain/60 text-white;
}

.bg-red-700 {
  @apply bg-redmain/70 text-white;
}

.bg-red-800 {
  @apply bg-redmain/80 text-white;
}

.bg-red-900 {
  @apply bg-redmain/90 text-white;
}

.bg-red {
  @apply bg-redmain text-white;
}

/* Start::red set */
/* Start::orange set */
.bg-orange-100 {
  @apply bg-orangemain/10 text-defaulttextcolor;
}

.bg-orange-200 {
  @apply bg-orangemain/20 text-defaulttextcolor;
}

.bg-orange-300 {
  @apply bg-orangemain/30 text-defaulttextcolor;
}

.bg-orange-400 {
  @apply bg-orangemain/40 text-defaulttextcolor;
}

.bg-orange-500 {
  @apply bg-orangemain/50 text-defaulttextcolor;
}

.bg-orange-600 {
  @apply bg-orangemain/60 text-white;
}

.bg-orange-700 {
  @apply bg-orangemain/70 text-white;
}

.bg-orange-800 {
  @apply bg-orangemain/80 text-white;
}

.bg-orange-900 {
  @apply bg-orangemain/90 text-white;
}

.bg-orange {
  @apply bg-orangemain text-white;
}

/* Start::orange set */
/* Start::yellow set */
.bg-yellow-100 {
  @apply bg-yellowmain/10 text-defaulttextcolor;
}

.bg-yellow-200 {
  @apply bg-yellowmain/20 text-defaulttextcolor;
}

.bg-yellow-300 {
  @apply bg-yellowmain/30 text-defaulttextcolor;
}

.bg-yellow-400 {
  @apply bg-yellowmain/40 text-defaulttextcolor;
}

.bg-yellow-500 {
  @apply bg-yellowmain/50 text-defaulttextcolor;
}

.bg-yellow-600 {
  @apply bg-yellowmain/60 text-white;
}

.bg-yellow-700 {
  @apply bg-yellowmain/70 text-white;
}

.bg-yellow-800 {
  @apply bg-yellowmain/80 text-white;
}

.bg-yellow-900 {
  @apply bg-yellowmain/90 text-white;
}

.bg-yellow {
  @apply bg-yellowmain text-white;
}

/* Start::yellow set */
/* Start::green set */
.bg-green-100 {
  @apply bg-greenmain/10 text-defaulttextcolor;
}

.bg-green-200 {
  @apply bg-greenmain/20 text-defaulttextcolor;
}

.bg-green-300 {
  @apply bg-greenmain/30 text-defaulttextcolor;
}

.bg-green-400 {
  @apply bg-greenmain/40 text-defaulttextcolor;
}

.bg-green-500 {
  @apply bg-greenmain/50 text-defaulttextcolor;
}

.bg-green-600 {
  @apply bg-greenmain/60 text-white;
}

.bg-green-700 {
  @apply bg-greenmain/70 text-white;
}

.bg-green-800 {
  @apply bg-greenmain/80 text-white;
}

.bg-green-900 {
  @apply bg-greenmain/90 text-white;
}

.bg-green {
  @apply bg-greenmain text-white;
}

/* Start::green set */
/* Start::teal set */
.bg-teal-100 {
  @apply bg-tealmain/10 text-defaulttextcolor;
}

.bg-teal-200 {
  @apply bg-tealmain/20 text-defaulttextcolor;
}

.bg-teal-300 {
  @apply bg-tealmain/30 text-defaulttextcolor;
}

.bg-teal-400 {
  @apply bg-tealmain/40 text-defaulttextcolor;
}

.bg-teal-500 {
  @apply bg-tealmain/50 text-defaulttextcolor;
}

.bg-teal-600 {
  @apply bg-tealmain/60 text-white;
}

.bg-teal-700 {
  @apply bg-tealmain/70 text-white;
}

.bg-teal-800 {
  @apply bg-tealmain/80 text-white;
}

.bg-teal-900 {
  @apply bg-tealmain/90 text-white;
}

.bg-teal {
  @apply bg-tealmain text-white;
}

/* Start::teal set */
/* Start::cyan set */
.bg-cyan-100 {
  @apply bg-cyanmain/10 text-defaulttextcolor;
}

.bg-cyan-200 {
  @apply bg-cyanmain/20 text-defaulttextcolor;
}

.bg-cyan-300 {
  @apply bg-cyanmain/30 text-defaulttextcolor;
}

.bg-cyan-400 {
  @apply bg-cyanmain/40 text-defaulttextcolor;
}

.bg-cyan-500 {
  @apply bg-cyanmain/50 text-defaulttextcolor;
}

.bg-cyan-600 {
  @apply bg-cyanmain/60 text-white;
}

.bg-cyan-700 {
  @apply bg-cyanmain/70 text-white;
}

.bg-cyan-800 {
  @apply bg-cyanmain/80 text-white;
}

.bg-cyan-900 {
  @apply bg-cyanmain/90 text-white;
}

.bg-cyan {
  @apply bg-cyanmain text-white;
}

/* Start::cyan set */
/* Start::gray set */
.bg-gray-100 {
  @apply bg-gray1 text-defaulttextcolor;
}

.bg-gray-200 {
  @apply bg-gray2 text-defaulttextcolor;
}

.bg-gray-300 {
  @apply bg-gray3 text-defaulttextcolor;
}

.bg-gray-400 {
  @apply bg-gray4 text-defaulttextcolor;
}

.bg-gray-500 {
  @apply bg-gray5 text-defaulttextcolor;
}

.bg-gray-600 {
  @apply bg-gray6 text-white;
}

.bg-gray-700 {
  @apply bg-gray7 text-white;
}

.bg-gray-800 {
  @apply bg-gray8 text-white;
}

.bg-gray-900 {
  @apply bg-gray9 text-white;
}

.bg-gray {
  @apply bg-gray9 text-white;
}

/* Start::gray set */
/* Start::Colors */
/* Start:: filter */
[class=dark] .invert-1 {
  @apply invert-[1];
}

.backdrop-blur {
  @apply backdrop-blur-[30px];
}

/* End:: filter */
/* End:: background */
/* Start::border */
.border-container {
  @apply inline-block w-20 h-20 bg-light/30 m-1;
}

/*Start::border */
.border.border-primary1 {
  @apply border-primarytint1color opacity-100 border-solid;
}
.border.border-primary1.border-opacity-10 {
  @apply border-primarytint1color/10 !important;
}
.border.border-primary1.border-opacity-25 {
  @apply border-primarytint1color/25 !important;
}
.border.border-primary1.border-opacity-50 {
  @apply border-primarytint1color/50 !important;
}
.border.border-primary1.border-opacity-75 {
  @apply border-primarytint1color/75 !important;
}
.border.border-primary1.border-opacity-100 {
  @apply border-primarytint1color/100 !important;
}
.border.border-primary2 {
  @apply border-primarytint2color opacity-100 border-solid;
}
.border.border-primary2.border-opacity-10 {
  @apply border-primarytint2color/10 !important;
}
.border.border-primary2.border-opacity-25 {
  @apply border-primarytint2color/25 !important;
}
.border.border-primary2.border-opacity-50 {
  @apply border-primarytint2color/50 !important;
}
.border.border-primary2.border-opacity-75 {
  @apply border-primarytint2color/75 !important;
}
.border.border-primary2.border-opacity-100 {
  @apply border-primarytint2color/100 !important;
}
.border.border-primary3 {
  @apply border-primarytint3color opacity-100 border-solid;
}
.border.border-primary3.border-opacity-10 {
  @apply border-primarytint3color/10 !important;
}
.border.border-primary3.border-opacity-25 {
  @apply border-primarytint3color/25 !important;
}
.border.border-primary3.border-opacity-50 {
  @apply border-primarytint3color/50 !important;
}
.border.border-primary3.border-opacity-75 {
  @apply border-primarytint3color/75 !important;
}
.border.border-primary3.border-opacity-100 {
  @apply border-primarytint3color/100 !important;
}

/* End::Border Colors */
/* Start::vr */
.vr {
  @apply bg-defaultborder opacity-100;
}

/* End::vr */
/* End::border */
/* Start::typography */
/* Start::basic sizes */
/* End::basic sizes */
/* Start::font sizes */
/* Start::text colors */
/* End::text colors */
/* Start::Colored Links */
.link-primary {
  @apply text-primary decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))];
}

.link-primary:hover,
.link-primary:focus,
.link-primary:active {
  @apply decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))] text-primary;
}

.link-primary1 {
  @apply text-primarytint1color decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))];
}

.link-primary1:hover,
.link-primary1:focus,
.link-primary1:active {
  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;
}

.link-primary2 {
  @apply text-primary/20 decoration-[rgba(var(--primary-tint2-rgb),var(--bs-link-underline-opacity,1))];
}

.link-primary2:hover,
.link-primary2:focus,
.link-primary2:active {
  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/20;
}

.link-primary3 {
  @apply text-primary/30 decoration-[rgba(var(--primary-tint3-rgb),var(--bs-link-underline-opacity,1))];
}

.link-primary3:hover,
.link-primary3:focus,
.link-primary3:active {
  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;
}

.link-secondary {
  @apply text-secondary decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))];
}

.link-secondary:hover,
.link-secondary:focus,
.link-secondary:active {
  @apply decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))] text-secondary;
}

.link-success {
  @apply text-success decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))];
}

.link-success:hover,
.link-success:focus,
.link-success:active {
  @apply decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))] text-success;
}

.link-danger {
  @apply text-danger decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))];
}

.link-danger:hover,
.link-danger:focus,
.link-danger:active {
  @apply decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))] text-danger;
}

.link-warning {
  @apply text-warning decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))];
}

.link-warning:hover,
.link-warning:focus,
.link-warning:active {
  @apply decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))] text-warning;
}

.link-info {
  @apply text-info decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))];
}

.link-info:hover,
.link-info:focus,
.link-info:active {
  @apply decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))] text-info;
}

.link-light {
  @apply text-light decoration-[rgba(var(--light-rgb),var(--bs-link-underline-opacity,1))];
}

.link-light:hover,
.link-light:focus,
.link-light:active {
  @apply decoration-[rgba(var(--light-rgb),var(--bs-link-underline-opacity,1))] text-light;
}

.link-dark {
  @apply text-dark decoration-[rgba(var(--dark-rgb),var(--bs-link-underline-opacity,1))];
}

.link-dark:hover,
.link-dark:focus,
.link-dark:active {
  @apply decoration-[rgba(var(--dark-rgb),var(--bs-link-underline-opacity,1))] text-dark;
}

/* End::Colored Links */
/* Start::Blockquote */
.blockquote-container .bg-outline-danger {
  @apply border border-defaultborder border-t-defaultborder rounded-md relative p-5 border-t-4 border-solid;
}
.blockquote-container:before {
  position: absolute;
  content: "\f6b0";
  @apply w-6 h-6 text-[0.813rem] font-normal font-bootstrap border border-defaultborder bg-white dark:bg-bodybg top-[-0.875rem] items-center flex justify-center shadow-[0px_0.25rem_1rem_rgba(0,0,0,0.1)] p-0 rounded-[3.125rem] border-solid start-2/4;
}

.blockquote.custom-blockquote {
  @apply text-[0.85rem] font-normal rounded-md relative p-4;
}
.blockquote.custom-blockquote:before {
  @apply content-[""] font-remix z-0 text-[4rem] absolute bottom-[-1.5rem] end-[-0.25rem];
}
.blockquote.custom-blockquote .quote-icon {
  @apply w-8 h-8 flex items-center justify-center absolute start-[-1rem] bg-primarytint2color rounded-[3.125rem] top-0;
}
.blockquote.custom-blockquote .quote-icon i {
  @apply text-base font-medium;
}
.blockquote.custom-blockquote.primary {
  @apply bg-primary/10 border-s-2 border-s-primary border-solid;
}
.blockquote.custom-blockquote.primary .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.primary:before {
  @apply text-primary/30;
}
.blockquote.custom-blockquote.primary2 {
  @apply bg-primarytint2color/20 border-s-primary/20 border-s-2 border-solid;
}
.blockquote.custom-blockquote.primary2 .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.primary2:before {
  @apply text-primarytint2color/20;
}
.blockquote.custom-blockquote.primary1 {
  @apply bg-primarytint1color/10 border-s-primary/10 border-t-primary/10 border-s-2 border-t-2 border-solid;
}
.blockquote.custom-blockquote.primary1 .quote-icon i {
  @apply text-primary/10;
}
.blockquote.custom-blockquote.primary1:before {
  @apply text-primarytint1color/10;
}
.blockquote.custom-blockquote.primary3 {
  @apply bg-primarytint3color/10 border-s-primary/30 border-s-2 border-solid;
}
.blockquote.custom-blockquote.primary3 .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.primary3:before {
  @apply text-primarytint3color/10;
}
.blockquote.custom-blockquote.secondary {
  @apply bg-secondary/10 border-s-2 border-s-secondary border-solid;
}
.blockquote.custom-blockquote.secondary .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.secondary:before {
  @apply text-secondary/10;
}
.blockquote.custom-blockquote.info {
  @apply bg-info/10 border-s-2 border-s-info border-solid;
}
.blockquote.custom-blockquote.info .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.info:before {
  @apply text-info/10;
}
.blockquote.custom-blockquote.warning {
  @apply bg-warning/10 border-s-2 border-s-warning border-solid;
}
.blockquote.custom-blockquote.warning .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.warning:before {
  @apply text-warning/10;
}
.blockquote.custom-blockquote.success {
  @apply bg-success/10 border-s-2 border-s-success border-solid;
}
.blockquote.custom-blockquote.success .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.success:before {
  @apply text-success/10;
}
.blockquote.custom-blockquote.danger {
  @apply bg-danger/10 border-s-2 border-s-danger border-solid;
}
.blockquote.custom-blockquote.danger .quote-icon i {
  @apply text-white;
}
.blockquote.custom-blockquote.danger:before {
  @apply text-danger/10;
}

/* End::Blockquote */
/* Start::Shadows */
[class=dark] .shadow-sm {
  @apply shadow-[0_0.125rem_0.25rem_rgba(33,37,41,0.3)] !important;
}
[class=dark] .shadow {
  @apply shadow-[0_0.5rem_1rem_rgba(33,37,41,0.3)] !important;
}
[class=dark] .shadow-lg {
  @apply shadow-[0_1rem_3rem_rgba(33,37,41,0.3)] !important;
}

/* End::Shadows */
@media screen and (min-width: 576px) {
  .w-sm-auto {
    @apply w-auto;
  }
}
@media (min-width: 576px) {
  .w-sm-50 {
    @apply w-6/12 !important;
  }
}
.w-95 {
  @apply w-[95%];
}

/* Start::Text Break */
[dir=rtl] .text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* End::Text Break */
/* Start::Transform */
.transform-none {
  transform: none !important;
}

/* End::Transform */
/* Start::Text gradient */
.text-gradient {
  @apply bg-primarygradient bg-clip-text;
}

/* End::Text gradient */
/* Start:: Focus Ring */
.focus-ring:focus {
  --bs-focus-ring-color: rgba(var(--primary-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-primary1:focus {
  --bs-focus-ring-color: rgba(var(--primary-tint1-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-primary2:focus {
  --bs-focus-ring-color: rgba(var(--primary-tint2-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-primary3:focus {
  --bs-focus-ring-color: rgba(var(--primary-tint3-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-secondary:focus {
  --bs-focus-ring-color: rgba(var(--secondary-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-success:focus {
  --bs-focus-ring-color: rgba(var(--success-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-danger:focus {
  --bs-focus-ring-color: rgba(var(--danger-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-warning:focus {
  --bs-focus-ring-color: rgba(var(--warning-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-info:focus {
  --bs-focus-ring-color: rgba(var(--info-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-light:focus {
  --bs-focus-ring-color: rgba(var(--light-rgb), var(--bs-focus-ring-opacity));
}
.focus-ring.focus-ring-dark:focus {
  --bs-focus-ring-color: rgba(var(--dark-rgb), var(--bs-focus-ring-opacity));
}

/* End:: Focus Ring */
/* End:: typography */
.fs-2 {
  @apply text-[calc(1.325rem_+_0.9vw)] !important;
}

@media (min-width: 1200px) {
  .fs-2 {
    @apply text-[2rem] !important;
  }
}
/* Google Fonts now loaded via Next.js font optimization */

/*# sourceMappingURL=styles.css.map */
