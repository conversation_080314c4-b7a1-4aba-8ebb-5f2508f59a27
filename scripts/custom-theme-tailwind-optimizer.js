#!/usr/bin/env node

/**
 * Custom Theme-Aware Tailwind Config Optimizer
 * 
 * This script optimizes the Tailwind config while preserving all custom theme
 * definitions and ensuring compatibility with the custom theme system.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CustomThemeTailwindOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.configPath = path.join(this.projectRoot, 'tailwind.config.ts');
    this.backupPath = path.join(this.projectRoot, 'tailwind.config.ts.custom-theme-backup');
    
    // Custom theme colors that must be preserved
    this.customThemeColors = [
      'golden', 'primary', 'secondary', 'success', 'warning', 'danger', 'info',
      'nav', 'section', 'filter', 'elevated', 'form', 'background', 'table',
      'modal', 'card', 'bodybg', 'textmuted', 'defaultborder', 'defaulttextcolor'
    ];

    // Essential custom utilities that must be preserved
    this.essentialUtilities = [
      'golden-button', 'golden-button-shadow', 'golden-button-hover-shadow',
      'custom-theme-colors', 'custom-container', 'custom-container-primary',
      'custom-container-secondary', 'custom-container-background'
    ];

    this.usedClasses = new Set();
    this.optimizations = [];
    this.errors = [];
  }

  // Create backup
  createBackup() {
    console.log('📦 Creating backup of tailwind.config.ts...');
    
    try {
      fs.copyFileSync(this.configPath, this.backupPath);
      console.log(`✅ Backup created at: ${this.backupPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      return false;
    }
  }

  // Analyze used classes in codebase
  analyzeUsedClasses() {
    console.log('🔍 Analyzing used Tailwind classes (preserving custom theme)...');

    const componentFiles = this.scanComponentFiles();
    console.log(`   Scanning ${componentFiles.length} component files...`);

    for (const filePath of componentFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.extractUsedClasses(content);
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    // Always preserve custom theme classes
    this.customThemeColors.forEach(color => {
      this.usedClasses.add(`bg-${color}`);
      this.usedClasses.add(`text-${color}`);
      this.usedClasses.add(`border-${color}`);
    });

    this.essentialUtilities.forEach(utility => {
      this.usedClasses.add(utility);
    });

    console.log(`   Found ${this.usedClasses.size} used classes (including preserved custom theme)`);
  }

  // Scan for component files
  scanComponentFiles() {
    const extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx'];
    const directories = ['app', 'shared', 'src'];
    
    const files = [];
    
    directories.forEach(dir => {
      const fullDir = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullDir)) {
        files.push(...this.scanDirectory(fullDir, extensions));
      }
    });
    
    return files;
  }

  // Generic directory scanner
  scanDirectory(dir, extensions) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract used classes from component content
  extractUsedClasses(content) {
    const patterns = [
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      /className\s*=\s*`([^`]+)`/g,
      /@apply\s+([^;]+);/g,
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          const classes = classString.split(/\s+/).filter(cls => cls.trim());
          classes.forEach(cls => {
            const cleanClass = cls.trim().replace(/[{}$]/g, '');
            if (this.isTailwindClass(cleanClass)) {
              this.usedClasses.add(cleanClass);
            }
          });
        }
      }
    });
  }

  // Check if a class looks like a Tailwind class
  isTailwindClass(className) {
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h|max-w|max-h|min-w|min-h)-/,
      /^(flex|grid|block|inline|hidden|visible)$/,
      /^(rounded|shadow|opacity|z-)/,
      /^(hover|focus|active|disabled|group-hover):/,
      /^(sm|md|lg|xl|2xl):/,
      /^(dark|light):/,
      /^(space-|divide-|ring-|transform|transition)/,
      /^(absolute|relative|fixed|sticky|static)/,
      /^(top|bottom|left|right|inset)-/,
      /^(justify|items|content|self|place)-/,
      /^(overflow|whitespace|break|truncate)/,
      /^(font|leading|tracking|text-)/,
      /^(cursor|select|pointer-events)/,
      /^(animate-|duration-|delay-|ease-)/,
    ];

    const customPatterns = [
      /^(bg-|text-|border-)(primary|secondary|success|warning|danger|info)/,
      /^(bg-|text-)(golden|nav|section|filter|elevated|form-)/,
      /^(bg-|text-)(background|table-|modal-|card-)/,
      /^ti-/,
      /^hs-/,
    ];

    return tailwindPatterns.some(pattern => pattern.test(className)) ||
           customPatterns.some(pattern => pattern.test(className)) ||
           className.includes('/') ||
           className.includes('[') ||
           className.includes('!');
  }

  // Optimize the Tailwind config while preserving custom theme
  optimizeConfig() {
    console.log('\n🔧 Optimizing Tailwind config (preserving custom theme)...');

    try {
      let configContent = fs.readFileSync(this.configPath, 'utf8');
      const originalContent = configContent;

      // Remove unused gradient stops (but preserve custom theme gradients)
      configContent = this.removeUnusedGradientStops(configContent);

      // Remove unused color variants (but preserve custom theme colors)
      configContent = this.removeUnusedColorVariants(configContent);

      // Remove unused utilities (but preserve essential ones)
      configContent = this.removeUnusedUtilities(configContent);

      // Clean up formatting
      configContent = this.cleanupFormatting(configContent);

      if (configContent !== originalContent) {
        fs.writeFileSync(this.configPath, configContent);
        console.log('✅ Tailwind config optimized (custom theme preserved)');
        return true;
      } else {
        console.log('ℹ️  No optimizations needed');
        return false;
      }
    } catch (error) {
      console.error(`❌ Config optimization failed: ${error.message}`);
      this.errors.push({ file: 'tailwind.config.ts', error: error.message });
      return false;
    }
  }

  // Remove unused gradient stops while preserving custom theme gradients
  removeUnusedGradientStops(content) {
    console.log('   Removing unused gradient stops (preserving custom theme)...');

    // Only remove gradient stops that are not related to custom theme
    const customThemeGradients = this.customThemeColors.map(color => 
      `'${color}'|"${color}"`
    ).join('|');

    // Remove gradient stops that don't match custom theme colors
    const gradientStopsPattern = new RegExp(
      `gradientColorStops:\\s*\\{[\\s\\S]*?\\},?\\s*\\n`,
      'g'
    );

    const gradientSection = content.match(gradientStopsPattern);
    if (gradientSection) {
      // Check if any custom theme colors are in the gradient section
      const hasCustomThemeGradients = this.customThemeColors.some(color =>
        gradientSection[0].includes(`'${color}'`) || gradientSection[0].includes(`"${color}"`)
      );

      if (!hasCustomThemeGradients) {
        const newContent = content.replace(gradientStopsPattern, '');
        if (newContent !== content) {
          this.optimizations.push('Removed unused gradientColorStops section (no custom theme gradients)');
          console.log('   ✅ Removed unused gradientColorStops section');
          return newContent;
        }
      } else {
        console.log('   ℹ️  Preserved gradientColorStops (contains custom theme gradients)');
      }
    }

    return content;
  }

  // Remove unused color variants while preserving custom theme colors
  removeUnusedColorVariants(content) {
    console.log('   Removing unused color variants (preserving custom theme)...');

    // Colors that are safe to remove (not part of custom theme)
    const safeToRemoveColors = [
      'primarytint1color', 'primarytint2color', 'primarytint3color',
      'headerbg', 'menubg', 'gray1', 'gray2', 'gray3', 'gray4', 'gray5',
      'gray6', 'gray7', 'gray8', 'gray9', 'customwhite',
      'primarylegacy', 'secondarylegacy', 'successlegacy', 'infolegacy', 
      'warninglegacy', 'dangerlegacy', 'light', 'dark'
    ];

    let newContent = content;
    let removedCount = 0;

    safeToRemoveColors.forEach(colorName => {
      // Only remove if it's not a custom theme color
      if (!this.customThemeColors.includes(colorName)) {
        const colorPattern = new RegExp(`\\s*${colorName}:\\s*["'][^"']*["'],?\\s*(?://.*)?\\n`, 'g');
        const beforeLength = newContent.length;
        newContent = newContent.replace(colorPattern, '');
        if (newContent.length < beforeLength) {
          removedCount++;
        }
      }
    });

    if (removedCount > 0) {
      this.optimizations.push(`Removed ${removedCount} unused color definitions (preserved custom theme)`);
      console.log(`   ✅ Removed ${removedCount} unused color definitions`);
    }

    return newContent;
  }

  // Remove unused utilities while preserving essential ones
  removeUnusedUtilities(content) {
    console.log('   Removing unused utilities (preserving essential ones)...');

    // Find addUtilities sections and preserve essential ones
    const utilitiesPattern = /addUtilities\(\{([\s\S]*?)\}\);/g;
    let match;
    let newContent = content;
    let removedCount = 0;

    while ((match = utilitiesPattern.exec(content)) !== null) {
      const utilitiesBlock = match[1];
      
      // Check if this utilities block contains essential utilities
      const hasEssentialUtilities = this.essentialUtilities.some(utility =>
        utilitiesBlock.includes(`.${utility}`) || utilitiesBlock.includes(`'${utility}'`) || utilitiesBlock.includes(`"${utility}"`)
      );

      // Check if it contains custom theme colors
      const hasCustomThemeColors = this.customThemeColors.some(color =>
        utilitiesBlock.includes(color)
      );

      if (!hasEssentialUtilities && !hasCustomThemeColors) {
        // This utilities block can be removed
        newContent = newContent.replace(match[0], '');
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this.optimizations.push(`Removed ${removedCount} unused utility blocks (preserved essential ones)`);
      console.log(`   ✅ Removed ${removedCount} unused utility blocks`);
    }

    return newContent;
  }

  // Clean up formatting
  cleanupFormatting(content) {
    // Remove excessive newlines
    let newContent = content.replace(/\n\n\n+/g, '\n\n');
    
    // Remove trailing commas before closing braces
    newContent = newContent.replace(/,(\s*\n\s*})/g, '$1');
    
    return newContent;
  }

  // Test build after optimization
  testBuild() {
    console.log('\n🔨 Testing build after custom theme-aware optimization...');

    try {
      // Test TypeScript compilation
      console.log('   Testing TypeScript compilation...');
      execSync('npm run type-check', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ TypeScript compilation passed');

      // Test build
      console.log('   Testing Next.js build...');
      execSync('npm run build:fast', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ Next.js build passed');

      return true;
    } catch (error) {
      console.error(`   ❌ Build failed: ${error.message}`);
      this.errors.push({ 
        file: 'build_test', 
        error: error.message,
        stdout: error.stdout?.toString(),
        stderr: error.stderr?.toString()
      });
      return false;
    }
  }

  // Rollback to backup
  rollback() {
    console.log('\n🔄 Rolling back to backup...');

    try {
      if (fs.existsSync(this.backupPath)) {
        fs.copyFileSync(this.backupPath, this.configPath);
        console.log('✅ Rollback successful');
        return true;
      } else {
        console.error('❌ Backup file not found');
        return false;
      }
    } catch (error) {
      console.error(`❌ Rollback failed: ${error.message}`);
      return false;
    }
  }

  // Generate optimization report
  generateReport() {
    const reportContent = `# Custom Theme-Aware Tailwind Config Optimization Report

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Used classes analyzed**: ${this.usedClasses.size}
- **Custom theme colors preserved**: ${this.customThemeColors.length}
- **Essential utilities preserved**: ${this.essentialUtilities.length}
- **Optimizations performed**: ${this.optimizations.length}
- **Errors encountered**: ${this.errors.length}
- **Backup location**: ${this.backupPath}

## 🎨 Custom Theme Colors Preserved

${this.customThemeColors.map(color => `- \`${color}\``).join('\n')}

## 🛠️ Essential Utilities Preserved

${this.essentialUtilities.map(utility => `- \`${utility}\``).join('\n')}

## 🔧 Optimizations Performed

${this.optimizations.map(opt => `- ${opt}`).join('\n')}

## 🎯 Sample Used Classes

${Array.from(this.usedClasses).slice(0, 30).map(cls => `- \`${cls}\``).join('\n')}
${this.usedClasses.size > 30 ? `\n*... and ${this.usedClasses.size - 30} more*` : ''}

## ❌ Errors

${this.errors.length > 0 ? 
  this.errors.map(error => `- **${error.file}**: ${error.error}`).join('\n') : 
  'No errors encountered ✅'
}

## 🔄 Rollback Instructions

If issues are detected, restore from backup:
\`\`\`bash
cp ${this.backupPath} ${this.configPath}
\`\`\`

## 📋 Next Steps

1. Test critical components to ensure custom theme is working
2. Verify golden buttons and custom styling are preserved
3. Check that all custom theme colors are available
4. Validate critical components functionality

---
*Generated by Custom Theme-Aware Tailwind Config Optimizer*
`;

    const reportPath = path.join(this.projectRoot, 'custom-theme-tailwind-optimization-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Optimization report saved to: ${reportPath}`);
  }

  // Main execution function
  async execute(options = {}) {
    console.log('🚀 Starting Custom Theme-Aware Tailwind Config Optimization...\n');

    // Handle rollback request
    if (options.rollback) {
      return this.rollback();
    }

    // Step 1: Create backup
    if (!this.createBackup()) {
      console.error('❌ Cannot proceed without backup');
      return false;
    }

    // Step 2: Analyze used classes
    this.analyzeUsedClasses();

    // Step 3: Optimize config
    const optimized = this.optimizeConfig();

    if (!optimized) {
      console.log('ℹ️  No optimizations performed');
      this.generateReport();
      return true;
    }

    // Step 4: Test build
    const buildPassed = this.testBuild();

    if (!buildPassed) {
      console.log('\n⚠️  Build failed after optimization. Rolling back...');
      this.rollback();
    }

    // Step 5: Generate report
    this.generateReport();

    console.log('\n✅ Custom theme-aware Tailwind config optimization complete!');
    console.log(`   Optimizations: ${this.optimizations.length}`);
    console.log(`   Custom theme colors preserved: ${this.customThemeColors.length}`);
    console.log(`   Essential utilities preserved: ${this.essentialUtilities.length}`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Build status: ${buildPassed ? '✅ PASSED' : '❌ FAILED (rolled back)'}`);

    return buildPassed;
  }
}

// Run the optimization
if (import.meta.url === `file://${process.argv[1]}`) {
  const options = {
    rollback: process.argv.includes('--rollback')
  };
  
  const optimizer = new CustomThemeTailwindOptimizer();
  optimizer.execute(options).catch(console.error);
}

export default CustomThemeTailwindOptimizer;
