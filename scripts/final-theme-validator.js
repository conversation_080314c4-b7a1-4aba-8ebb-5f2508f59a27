#!/usr/bin/env node

/**
 * Final Theme Validator
 * 
 * This script performs comprehensive validation of the custom theme
 * after all cleanup operations to ensure everything is working correctly.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FinalThemeValidator {
  constructor() {
    this.projectRoot = process.cwd();
    
    // Critical components to validate
    this.criticalComponents = [
      {
        path: 'shared/UI/buttons/PrimaryButton.tsx',
        name: 'PrimaryButton',
        expectedClasses: ['bg-golden-button', 'shadow-golden-button'],
        description: 'Golden button with custom styling'
      },
      {
        path: 'shared/UI/tables/SpkTable.tsx',
        name: 'SpkTable',
        expectedClasses: ['text-white', 'font-semibold'],
        description: 'Table with custom typography'
      },
      {
        path: 'shared/UI/components/StatusBadge.tsx',
        name: 'StatusBadge',
        expectedClasses: ['bg-success-notification', 'text-success-message'],
        description: 'Status badges with custom colors'
      }
    ];

    // Custom theme variables to validate
    this.customThemeVariables = [
      'golden', 'primary', 'secondary', 'success', 'warning', 'danger', 'info',
      'bodybg', 'background', 'defaulttextcolor', 'textmuted', 'defaultborder'
    ];

    this.validationResults = [];
    this.errors = [];
  }

  // Validate build system
  validateBuildSystem() {
    console.log('🔨 Validating build system...');

    const tests = [
      { name: 'SCSS Compilation', command: 'npm run sass' },
      { name: 'TypeScript Check', command: 'npm run type-check' },
      { name: 'Next.js Build', command: 'npm run build:fast' }
    ];

    let allPassed = true;

    tests.forEach(test => {
      try {
        console.log(`   Testing ${test.name}...`);
        execSync(test.command, { 
          stdio: 'pipe',
          cwd: this.projectRoot 
        });
        console.log(`   ✅ ${test.name} passed`);
        
        this.validationResults.push({
          category: 'build_system',
          test: test.name,
          status: 'PASS',
          message: 'Build test passed successfully'
        });
      } catch (error) {
        console.error(`   ❌ ${test.name} failed: ${error.message}`);
        allPassed = false;
        
        this.validationResults.push({
          category: 'build_system',
          test: test.name,
          status: 'FAIL',
          message: error.message,
          error: error.toString()
        });
        
        this.errors.push({
          category: 'build_system',
          test: test.name,
          error: error.message
        });
      }
    });

    return allPassed;
  }

  // Validate custom theme variables in Tailwind config
  validateTailwindConfig() {
    console.log('\n🎨 Validating Tailwind config custom theme...');

    const configPath = path.join(this.projectRoot, 'tailwind.config.ts');
    
    if (!fs.existsSync(configPath)) {
      console.error('   ❌ tailwind.config.ts not found');
      this.errors.push({
        category: 'tailwind_config',
        error: 'Config file not found'
      });
      return false;
    }

    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      let allVariablesFound = true;

      this.customThemeVariables.forEach(variable => {
        const found = configContent.includes(`${variable}:`) || 
                     configContent.includes(`"${variable}"`) ||
                     configContent.includes(`'${variable}'`);
        
        if (found) {
          console.log(`   ✅ Custom theme variable '${variable}' found`);
          this.validationResults.push({
            category: 'tailwind_config',
            test: `custom_variable_${variable}`,
            status: 'PASS',
            message: `Custom theme variable '${variable}' is defined`
          });
        } else {
          console.log(`   ⚠️  Custom theme variable '${variable}' not found`);
          allVariablesFound = false;
          this.validationResults.push({
            category: 'tailwind_config',
            test: `custom_variable_${variable}`,
            status: 'WARN',
            message: `Custom theme variable '${variable}' not found in config`
          });
        }
      });

      return allVariablesFound;
    } catch (error) {
      console.error(`   ❌ Failed to validate config: ${error.message}`);
      this.errors.push({
        category: 'tailwind_config',
        error: error.message
      });
      return false;
    }
  }

  // Validate critical components
  validateCriticalComponents() {
    console.log('\n🧩 Validating critical components...');

    let allComponentsValid = true;

    this.criticalComponents.forEach(component => {
      const componentPath = path.join(this.projectRoot, component.path);
      
      if (!fs.existsSync(componentPath)) {
        console.log(`   ⚠️  Component not found: ${component.name}`);
        this.validationResults.push({
          category: 'critical_components',
          test: component.name,
          status: 'WARN',
          message: `Component file not found: ${component.path}`
        });
        return;
      }

      try {
        const content = fs.readFileSync(componentPath, 'utf8');
        let componentValid = true;

        component.expectedClasses.forEach(expectedClass => {
          if (content.includes(expectedClass)) {
            console.log(`   ✅ ${component.name}: '${expectedClass}' found`);
          } else {
            console.log(`   ⚠️  ${component.name}: '${expectedClass}' not found`);
            componentValid = false;
          }
        });

        this.validationResults.push({
          category: 'critical_components',
          test: component.name,
          status: componentValid ? 'PASS' : 'WARN',
          message: componentValid ? 
            'All expected classes found' : 
            'Some expected classes missing',
          description: component.description
        });

        if (!componentValid) {
          allComponentsValid = false;
        }

      } catch (error) {
        console.error(`   ❌ Failed to validate ${component.name}: ${error.message}`);
        allComponentsValid = false;
        this.errors.push({
          category: 'critical_components',
          component: component.name,
          error: error.message
        });
      }
    });

    return allComponentsValid;
  }

  // Validate custom theme overrides file
  validateCustomThemeOverrides() {
    console.log('\n🎭 Validating custom theme overrides...');

    const overridesPath = path.join(this.projectRoot, 'public/assets/scss/custom/_custom_theme_overrides.scss');
    
    if (!fs.existsSync(overridesPath)) {
      console.log('   ⚠️  Custom theme overrides file not found');
      this.validationResults.push({
        category: 'theme_overrides',
        test: 'overrides_file_exists',
        status: 'WARN',
        message: 'Custom theme overrides file not found'
      });
      return false;
    }

    try {
      const content = fs.readFileSync(overridesPath, 'utf8');
      
      const expectedClasses = [
        '.custom-theme-colors',
        '.custom-container',
        '.custom-container-primary',
        '.btn-golden',
        '.custom-golden-btn'
      ];

      let allClassesFound = true;

      expectedClasses.forEach(className => {
        if (content.includes(className)) {
          console.log(`   ✅ Custom class '${className}' found`);
        } else {
          console.log(`   ⚠️  Custom class '${className}' not found`);
          allClassesFound = false;
        }
      });

      this.validationResults.push({
        category: 'theme_overrides',
        test: 'custom_classes',
        status: allClassesFound ? 'PASS' : 'WARN',
        message: allClassesFound ? 
          'All custom theme classes found' : 
          'Some custom theme classes missing'
      });

      return allClassesFound;
    } catch (error) {
      console.error(`   ❌ Failed to validate theme overrides: ${error.message}`);
      this.errors.push({
        category: 'theme_overrides',
        error: error.message
      });
      return false;
    }
  }

  // Check for vendor remnants
  checkForVendorRemnants() {
    console.log('\n🔍 Checking for remaining vendor remnants...');

    const scssFiles = this.scanSCSSFiles();
    let vendorRemnantsFound = 0;

    const vendorPatterns = [
      /\.theme-colors(?!\w)/g,
      /\.theme-container(?!-)/g,
      /xtentra/gi,
      /vendor-/gi
    ];

    scssFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(this.projectRoot, filePath);

        vendorPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            vendorRemnantsFound += matches.length;
            console.log(`   ⚠️  Vendor remnant found in ${relativePath}: ${matches.join(', ')}`);
          }
        });
      } catch (error) {
        console.warn(`Warning: Could not check ${filePath}: ${error.message}`);
      }
    });

    if (vendorRemnantsFound === 0) {
      console.log('   ✅ No vendor remnants found');
      this.validationResults.push({
        category: 'vendor_remnants',
        test: 'vendor_cleanup',
        status: 'PASS',
        message: 'No vendor remnants found'
      });
      return true;
    } else {
      console.log(`   ⚠️  Found ${vendorRemnantsFound} vendor remnants`);
      this.validationResults.push({
        category: 'vendor_remnants',
        test: 'vendor_cleanup',
        status: 'WARN',
        message: `Found ${vendorRemnantsFound} vendor remnants`
      });
      return false;
    }
  }

  // Scan SCSS files
  scanSCSSFiles() {
    const files = [];
    const scssDir = path.join(this.projectRoot, 'public/assets/scss');
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDir(fullPath);
          } else if (item.endsWith('.scss')) {
            files.push(fullPath);
          }
        });
      } catch (error) {
        console.warn(`Warning: Could not scan ${dir}: ${error.message}`);
      }
    };

    if (fs.existsSync(scssDir)) {
      scanDir(scssDir);
    }
    
    return files;
  }

  // Generate final validation report
  generateReport() {
    const timestamp = new Date().toISOString();
    
    const passedTests = this.validationResults.filter(r => r.status === 'PASS').length;
    const warnTests = this.validationResults.filter(r => r.status === 'WARN').length;
    const failedTests = this.validationResults.filter(r => r.status === 'FAIL').length;
    const totalTests = this.validationResults.length;

    const reportContent = `# Final Theme Validation Report

Generated on: ${timestamp}

## 📊 Summary

- **Total tests**: ${totalTests}
- **Passed**: ${passedTests} ✅
- **Warnings**: ${warnTests} ⚠️
- **Failed**: ${failedTests} ❌
- **Errors encountered**: ${this.errors.length}

## 🎯 Overall Status

${failedTests === 0 ? '✅ **VALIDATION PASSED**' : '❌ **VALIDATION FAILED**'}

${failedTests === 0 ? 
  'All critical systems are working correctly with custom theme preserved.' :
  'Some critical issues were found that need attention.'
}

## 📋 Test Results by Category

### Build System
${this.validationResults
  .filter(r => r.category === 'build_system')
  .map(r => `- **${r.test}**: ${r.status === 'PASS' ? '✅' : r.status === 'WARN' ? '⚠️' : '❌'} ${r.message}`)
  .join('\n')}

### Tailwind Config
${this.validationResults
  .filter(r => r.category === 'tailwind_config')
  .map(r => `- **${r.test}**: ${r.status === 'PASS' ? '✅' : r.status === 'WARN' ? '⚠️' : '❌'} ${r.message}`)
  .join('\n')}

### Critical Components
${this.validationResults
  .filter(r => r.category === 'critical_components')
  .map(r => `- **${r.test}**: ${r.status === 'PASS' ? '✅' : r.status === 'WARN' ? '⚠️' : '❌'} ${r.message}`)
  .join('\n')}

### Theme Overrides
${this.validationResults
  .filter(r => r.category === 'theme_overrides')
  .map(r => `- **${r.test}**: ${r.status === 'PASS' ? '✅' : r.status === 'WARN' ? '⚠️' : '❌'} ${r.message}`)
  .join('\n')}

### Vendor Remnants
${this.validationResults
  .filter(r => r.category === 'vendor_remnants')
  .map(r => `- **${r.test}**: ${r.status === 'PASS' ? '✅' : r.status === 'WARN' ? '⚠️' : '❌'} ${r.message}`)
  .join('\n')}

## ❌ Errors Encountered

${this.errors.length > 0 ? 
  this.errors.map(error => 
    `- **${error.category}${error.test ? ` (${error.test})` : ''}**: ${error.error}`
  ).join('\n') : 
  'No errors encountered ✅'
}

## 🎨 Custom Theme Status

- **Golden buttons**: ${this.validationResults.some(r => r.test === 'PrimaryButton' && r.status === 'PASS') ? '✅ Working' : '⚠️ Needs check'}
- **Custom colors**: ${this.validationResults.filter(r => r.category === 'tailwind_config' && r.status === 'PASS').length > 10 ? '✅ Preserved' : '⚠️ Some missing'}
- **Vendor cleanup**: ${this.validationResults.some(r => r.test === 'vendor_cleanup' && r.status === 'PASS') ? '✅ Complete' : '⚠️ Remnants found'}
- **Theme overrides**: ${this.validationResults.some(r => r.test === 'custom_classes' && r.status === 'PASS') ? '✅ Active' : '⚠️ Issues found'}

## 📋 Recommendations

${failedTests > 0 ? `
### Critical Issues to Address:
${this.validationResults
  .filter(r => r.status === 'FAIL')
  .map(r => `- Fix ${r.test}: ${r.message}`)
  .join('\n')}
` : ''}

${warnTests > 0 ? `
### Warnings to Review:
${this.validationResults
  .filter(r => r.status === 'WARN')
  .map(r => `- Review ${r.test}: ${r.message}`)
  .join('\n')}
` : ''}

${failedTests === 0 && warnTests === 0 ? `
### All Systems Operational ✅
- Custom theme is fully preserved and working
- All vendor remnants have been cleaned up
- Build system is stable
- Critical components are functioning correctly
` : ''}

---
*Generated by Final Theme Validator*
`;

    const reportPath = path.join(this.projectRoot, 'final-theme-validation-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Final validation report saved to: ${reportPath}`);
    
    return failedTests === 0;
  }

  // Main execution function
  async execute() {
    console.log('🚀 Starting Final Theme Validation...\n');

    // Step 1: Validate build system
    const buildValid = this.validateBuildSystem();

    // Step 2: Validate Tailwind config
    const configValid = this.validateTailwindConfig();

    // Step 3: Validate critical components
    const componentsValid = this.validateCriticalComponents();

    // Step 4: Validate custom theme overrides
    const overridesValid = this.validateCustomThemeOverrides();

    // Step 5: Check for vendor remnants
    const vendorClean = this.checkForVendorRemnants();

    // Step 6: Generate report
    const overallValid = this.generateReport();

    console.log('\n✅ Final theme validation complete!');
    console.log(`   Build system: ${buildValid ? '✅ VALID' : '❌ ISSUES'}`);
    console.log(`   Tailwind config: ${configValid ? '✅ VALID' : '⚠️ WARNINGS'}`);
    console.log(`   Critical components: ${componentsValid ? '✅ VALID' : '⚠️ WARNINGS'}`);
    console.log(`   Theme overrides: ${overridesValid ? '✅ VALID' : '⚠️ WARNINGS'}`);
    console.log(`   Vendor cleanup: ${vendorClean ? '✅ CLEAN' : '⚠️ REMNANTS'}`);
    console.log(`   Overall status: ${overallValid ? '✅ PASSED' : '❌ NEEDS ATTENTION'}`);

    return overallValid;
  }
}

// Run the validation
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new FinalThemeValidator();
  validator.execute().catch(console.error);
}

export default FinalThemeValidator;
