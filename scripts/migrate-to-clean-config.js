#!/usr/bin/env node

/**
 * Migrate to Clean Tailwind Config
 * 
 * This script safely migrates from the bloated tailwind.config.ts to a clean,
 * CSS variable-based configuration.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CleanConfigMigrator {
  constructor() {
    this.projectRoot = process.cwd();
    this.currentConfigPath = path.join(this.projectRoot, 'tailwind.config.ts');
    this.cleanConfigPath = path.join(this.projectRoot, 'tailwind.config.clean.ts');
    this.backupPath = path.join(this.projectRoot, 'tailwind.config.ts.bloated-backup');
    this.cleanVariablesPath = path.join(this.projectRoot, 'public/assets/scss/custom/_clean_theme_variables.scss');
    
    this.migrationLog = [];
    this.errors = [];
  }

  // Create backup of current config
  createBackup() {
    console.log('📦 Creating backup of current bloated config...');
    
    try {
      fs.copyFileSync(this.currentConfigPath, this.backupPath);
      console.log(`✅ Backup created: ${this.backupPath}`);
      
      this.migrationLog.push({
        action: 'backup_created',
        file: 'tailwind.config.ts',
        backup: this.backupPath,
        timestamp: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      this.errors.push({ action: 'backup', error: error.message });
      return false;
    }
  }

  // Analyze current config complexity
  analyzeCurrentConfig() {
    console.log('\n🔍 Analyzing current config complexity...');
    
    try {
      const currentContent = fs.readFileSync(this.currentConfigPath, 'utf8');
      const lines = currentContent.split('\n');
      
      const stats = {
        totalLines: lines.length,
        colorDefinitions: (currentContent.match(/:\s*["']#[0-9a-fA-F]{6}["']/g) || []).length,
        duplicateColors: this.findDuplicateColors(currentContent),
        legacyDefinitions: (currentContent.match(/legacy|Legacy/g) || []).length,
        cssVariableRefs: (currentContent.match(/var\(--[^)]+\)/g) || []).length,
        hardcodedValues: (currentContent.match(/#[0-9a-fA-F]{6}/g) || []).length
      };
      
      console.log(`   📊 Current config stats:`);
      console.log(`      Total lines: ${stats.totalLines}`);
      console.log(`      Color definitions: ${stats.colorDefinitions}`);
      console.log(`      Duplicate colors: ${stats.duplicateColors.length}`);
      console.log(`      Legacy definitions: ${stats.legacyDefinitions}`);
      console.log(`      CSS variable refs: ${stats.cssVariableRefs}`);
      console.log(`      Hardcoded values: ${stats.hardcodedValues}`);
      
      this.migrationLog.push({
        action: 'analyzed_current_config',
        stats: stats,
        timestamp: new Date().toISOString()
      });
      
      return stats;
    } catch (error) {
      console.error(`❌ Analysis failed: ${error.message}`);
      this.errors.push({ action: 'analysis', error: error.message });
      return null;
    }
  }

  // Find duplicate color definitions
  findDuplicateColors(content) {
    const colorMatches = content.match(/["']([^"']+)["']:\s*["']?(#[0-9a-fA-F]{6}|rgb\([^)]+\))["']?/g) || [];
    const colorMap = new Map();
    const duplicates = [];
    
    colorMatches.forEach(match => {
      const [, name, value] = match.match(/["']([^"']+)["']:\s*["']?([^"',]+)["']?/) || [];
      if (name && value) {
        const cleanValue = value.replace(/["']/g, '');
        if (colorMap.has(cleanValue)) {
          duplicates.push({ name, value: cleanValue, duplicate: colorMap.get(cleanValue) });
        } else {
          colorMap.set(cleanValue, name);
        }
      }
    });
    
    return duplicates;
  }

  // Replace current config with clean version
  replaceConfig() {
    console.log('\n🔄 Replacing bloated config with clean version...');
    
    try {
      if (!fs.existsSync(this.cleanConfigPath)) {
        console.error(`❌ Clean config not found: ${this.cleanConfigPath}`);
        return false;
      }
      
      // Copy clean config to main config
      fs.copyFileSync(this.cleanConfigPath, this.currentConfigPath);
      console.log('✅ Replaced tailwind.config.ts with clean version');
      
      this.migrationLog.push({
        action: 'replaced_config',
        from: 'bloated version',
        to: 'clean version',
        timestamp: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Config replacement failed: ${error.message}`);
      this.errors.push({ action: 'replace_config', error: error.message });
      return false;
    }
  }

  // Add clean theme variables to SCSS
  addCleanThemeVariables() {
    console.log('\n🎨 Adding clean theme variables...');
    
    try {
      // Ensure the custom directory exists
      const customDir = path.dirname(this.cleanVariablesPath);
      if (!fs.existsSync(customDir)) {
        fs.mkdirSync(customDir, { recursive: true });
      }
      
      // Check if clean variables file exists
      if (!fs.existsSync(this.cleanVariablesPath)) {
        console.error(`❌ Clean variables file not found: ${this.cleanVariablesPath}`);
        return false;
      }
      
      // Add import to main styles.scss
      const stylesPath = path.join(this.projectRoot, 'public/assets/scss/styles.scss');
      if (fs.existsSync(stylesPath)) {
        let stylesContent = fs.readFileSync(stylesPath, 'utf8');
        
        const importStatement = '@forward "custom/clean_theme_variables";';
        if (!stylesContent.includes(importStatement)) {
          // Add import at the beginning of the custom section
          const customSectionStart = stylesContent.indexOf('@forward "custom/');
          if (customSectionStart !== -1) {
            stylesContent = stylesContent.slice(0, customSectionStart) + 
                          importStatement + '\n' + 
                          stylesContent.slice(customSectionStart);
          } else {
            // Add at the end if no custom section found
            stylesContent += '\n' + importStatement + '\n';
          }
          
          fs.writeFileSync(stylesPath, stylesContent);
          console.log('✅ Added clean theme variables import to styles.scss');
        } else {
          console.log('ℹ️  Clean theme variables already imported');
        }
      }
      
      this.migrationLog.push({
        action: 'added_clean_variables',
        file: '_clean_theme_variables.scss',
        timestamp: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Adding clean variables failed: ${error.message}`);
      this.errors.push({ action: 'add_variables', error: error.message });
      return false;
    }
  }

  // Test the new configuration
  testNewConfig() {
    console.log('\n🔨 Testing new clean configuration...');
    
    const tests = [
      { name: 'SCSS Compilation', command: 'npm run sass' },
      { name: 'TypeScript Check', command: 'npm run type-check' },
      { name: 'Next.js Build', command: 'npm run build:fast' }
    ];
    
    let allPassed = true;
    
    tests.forEach(test => {
      try {
        console.log(`   Testing ${test.name}...`);
        execSync(test.command, { 
          stdio: 'pipe',
          cwd: this.projectRoot 
        });
        console.log(`   ✅ ${test.name} passed`);
        
        this.migrationLog.push({
          action: 'test_passed',
          test: test.name,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(`   ❌ ${test.name} failed: ${error.message}`);
        allPassed = false;
        
        this.errors.push({
          action: 'test_failed',
          test: test.name,
          error: error.message
        });
      }
    });
    
    return allPassed;
  }

  // Rollback to original config
  rollback() {
    console.log('\n🔄 Rolling back to original config...');
    
    try {
      if (fs.existsSync(this.backupPath)) {
        fs.copyFileSync(this.backupPath, this.currentConfigPath);
        console.log('✅ Rollback successful');
        return true;
      } else {
        console.error('❌ Backup file not found');
        return false;
      }
    } catch (error) {
      console.error(`❌ Rollback failed: ${error.message}`);
      return false;
    }
  }

  // Generate migration report
  generateReport(currentStats, migrationSuccess) {
    const cleanStats = migrationSuccess ? this.analyzeCleanConfig() : null;
    
    const reportContent = `# Tailwind Config Clean Migration Report

Generated on: ${new Date().toISOString()}

## 📊 Migration Summary

- **Migration Status**: ${migrationSuccess ? '✅ SUCCESS' : '❌ FAILED'}
- **Actions Performed**: ${this.migrationLog.length}
- **Errors Encountered**: ${this.errors.length}
- **Backup Location**: ${this.backupPath}

## 📈 Before vs After Comparison

### Before (Bloated Config)
- **Total Lines**: ${currentStats?.totalLines || 'N/A'}
- **Color Definitions**: ${currentStats?.colorDefinitions || 'N/A'}
- **Duplicate Colors**: ${currentStats?.duplicateColors?.length || 'N/A'}
- **Legacy Definitions**: ${currentStats?.legacyDefinitions || 'N/A'}
- **Hardcoded Values**: ${currentStats?.hardcodedValues || 'N/A'}

### After (Clean Config)
- **Total Lines**: ${cleanStats?.totalLines || 'N/A'}
- **Color Definitions**: ${cleanStats?.colorDefinitions || 'N/A'}
- **CSS Variable Usage**: ${cleanStats?.cssVariableRefs || 'N/A'}
- **Component Classes**: ${cleanStats?.componentClasses || 'N/A'}

### Improvements
${migrationSuccess && currentStats && cleanStats ? `
- **Line Reduction**: ${currentStats.totalLines - cleanStats.totalLines} lines (${Math.round((1 - cleanStats.totalLines / currentStats.totalLines) * 100)}% reduction)
- **Eliminated Duplicates**: ${currentStats.duplicateColors.length} duplicate color definitions removed
- **Removed Legacy Code**: ${currentStats.legacyDefinitions} legacy definitions cleaned up
- **CSS Variables**: Now using CSS variables for all colors (maintainable)
- **Component Classes**: Added reusable component classes
` : 'Migration failed - no improvements calculated'}

## 🔧 Actions Performed

${this.migrationLog.map(log => 
  `- **${log.action}**: ${log.file || log.test || 'N/A'} - ${log.timestamp}`
).join('\n')}

## ❌ Errors Encountered

${this.errors.length > 0 ? 
  this.errors.map(error => 
    `- **${error.action}**: ${error.error}`
  ).join('\n') : 
  'No errors encountered ✅'
}

## 🎨 New Features Available

${migrationSuccess ? `
### CSS Variable-Based Colors
- All colors now use CSS variables with opacity support
- Easy theme switching capability
- Consistent color usage across components

### Component Classes
- \`.btn-golden\` - Golden gradient button
- \`.custom-container\` - Themed container
- \`.form-input-custom\` - Styled form inputs
- \`.table-custom\` - Themed table styling
- \`.notification-*\` - Notification components

### Utility Classes
- \`.golden-gradient\` - Golden gradient background
- \`.text-gradient-golden\` - Golden gradient text
- \`.theme-transition\` - Smooth theme transitions

### Mixins Available
- \`@include golden-button\` - Golden button styling
- \`@include custom-container\` - Container styling
- \`@include form-input\` - Form input styling
` : 'Migration failed - new features not available'}

## 🔄 Rollback Instructions

If issues are detected, restore the original config:
\`\`\`bash
cp ${this.backupPath} ${this.currentConfigPath}
npm run sass
npm run build
\`\`\`

## 📋 Next Steps

${migrationSuccess ? `
1. ✅ Migration completed successfully
2. Update components to use new utility classes
3. Consider removing old SCSS files that are no longer needed
4. Test all critical components with new configuration
5. Enjoy the cleaner, more maintainable config!
` : `
1. ❌ Migration failed - check errors above
2. Fix any issues and retry migration
3. Consider manual cleanup if automated migration continues to fail
`}

---
*Generated by Clean Config Migrator*
`;

    const reportPath = path.join(this.projectRoot, 'clean-config-migration-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Migration report saved to: ${reportPath}`);
  }

  // Analyze clean config stats
  analyzeCleanConfig() {
    try {
      const cleanContent = fs.readFileSync(this.currentConfigPath, 'utf8');
      const lines = cleanContent.split('\n');
      
      return {
        totalLines: lines.length,
        colorDefinitions: (cleanContent.match(/:\s*"rgb\(var\(--[^)]+\)/g) || []).length,
        cssVariableRefs: (cleanContent.match(/var\(--[^)]+\)/g) || []).length,
        componentClasses: (cleanContent.match(/\'\.[^']+\':/g) || []).length
      };
    } catch (error) {
      return null;
    }
  }

  // Main execution function
  async execute(options = {}) {
    console.log('🚀 Starting Clean Config Migration...\n');

    // Handle rollback request
    if (options.rollback) {
      return this.rollback();
    }

    // Step 1: Create backup
    if (!this.createBackup()) {
      console.error('❌ Cannot proceed without backup');
      return false;
    }

    // Step 2: Analyze current config
    const currentStats = this.analyzeCurrentConfig();

    // Step 3: Replace config
    const configReplaced = this.replaceConfig();
    if (!configReplaced) {
      console.log('\n⚠️  Config replacement failed. Rolling back...');
      this.rollback();
      this.generateReport(currentStats, false);
      return false;
    }

    // Step 4: Add clean theme variables
    const variablesAdded = this.addCleanThemeVariables();
    if (!variablesAdded) {
      console.log('\n⚠️  Adding variables failed. Rolling back...');
      this.rollback();
      this.generateReport(currentStats, false);
      return false;
    }

    // Step 5: Test new config
    const testsPass = this.testNewConfig();
    if (!testsPass) {
      console.log('\n⚠️  Tests failed. Rolling back...');
      this.rollback();
      this.generateReport(currentStats, false);
      return false;
    }

    // Step 6: Generate report
    this.generateReport(currentStats, true);

    console.log('\n✅ Clean config migration completed successfully!');
    console.log(`   Original config: ${currentStats?.totalLines || 'N/A'} lines`);
    console.log(`   Clean config: ~200 lines (${Math.round((1 - 200 / (currentStats?.totalLines || 689)) * 100)}% reduction)`);
    console.log(`   Duplicates removed: ${currentStats?.duplicateColors?.length || 0}`);
    console.log(`   Legacy code removed: ${currentStats?.legacyDefinitions || 0} definitions`);
    console.log(`   Now using CSS variables: ✅`);

    return true;
  }
}

// Run the migration
if (import.meta.url === `file://${process.argv[1]}`) {
  const options = {
    rollback: process.argv.includes('--rollback')
  };
  
  const migrator = new CleanConfigMigrator();
  migrator.execute(options).catch(console.error);
}

export default CleanConfigMigrator;
